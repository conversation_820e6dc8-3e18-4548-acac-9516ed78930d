import { NextRequest, NextResponse } from 'next/server';
import { verifySupabaseAuth } from "@/lib/supabaseAuth";
import pkg from 'pg';
const { Client } = pkg;
import { mapUuidToNumericId } from '@/utils/userIdMapping';

export async function GET(request: NextRequest) {
  try {
    // Get strategy ID and showAllCompanies flag from query params
    const { searchParams } = new URL(request.url);
    const strategyId = searchParams.get('strategyId');
    const showAllCompanies = searchParams.get('showAllCompanies') === 'true';

    if (!strategyId) {
      return NextResponse.json({ error: 'Strategy ID is required' }, { status: 400 });
    }

    // Verify user authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();

    if (!authResult || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { user } = authResult;

    // Map the UUID to a numeric ID for database compatibility
    const userId = await mapUuidToNumericId(user.id);

    // Connect to the database
    const connectionString = process.env.POSTGRES_URL;
    if (!connectionString) {
      throw new Error('POSTGRES_URL environment variable not found.');
    }

    // Deaktiviere SSL-Überprüfung für selbstsignierte Zertifikate
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

    const client = new Client({
      connectionString,
      ssl: {
        rejectUnauthorized: false,
      },
    });

    await client.connect();

    // Query scan_results table for results related to the strategy
    // Use DISTINCT ON only when not showing all companies to get the most recent entry per company_id
    let query = `
      SELECT
          sr.id,
          COALESCE(c.name, 'Unbekanntes Unternehmen') as title, -- Use COALESCE to handle NULL values
          sr.result_text as description,
          sr.created_at,
          sr.place_id
      FROM
          scan_results sr
      LEFT JOIN
          companies c ON sr.place_id = c.place_id
      LEFT JOIN
          selected_companies sc ON sc.place_id = sr.place_id AND sc.user_id = $1 AND sc.is_deleted = false
      WHERE
          sr.user_id = $1
          AND sr.strategy_id = $2
    `;
    if (!showAllCompanies) {
      query += `
          AND sc.id IS NOT NULL
      `;
    }
    query += `
      ORDER BY
          sr.created_at DESC -- Order by latest date
    `;
    if (!showAllCompanies) {
      query = `
        SELECT DISTINCT ON (sr.place_id)
            sr.id,
            COALESCE(c.name, 'Unbekanntes Unternehmen') as title,
            sr.result_text as description,
            sr.created_at,
            sr.place_id
        FROM
            scan_results sr
        LEFT JOIN
            companies c ON sr.place_id = c.place_id
        LEFT JOIN
            selected_companies sc ON sc.place_id = sr.place_id AND sc.user_id = $1 AND sc.is_deleted = false
        WHERE
            sr.user_id = $1
            AND sr.strategy_id = $2
            AND sc.id IS NOT NULL
        ORDER BY
            sr.place_id, sr.created_at DESC -- Order by place_id first, then latest date within company
      `;
    }
    const result = await client.query(query, [userId, strategyId]);

    // Close the database connection
    await client.end();

    // Return the results
    return NextResponse.json({
      results: result.rows,
      message: 'Scan results loaded successfully'
    });

  } catch (error) {
    console.error('Error in strategy-scan-results API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
