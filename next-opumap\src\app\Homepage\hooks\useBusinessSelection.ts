import { useState, useRef, useCallback, useEffect } from "react";
import { BusinessData } from "@/types";
import type { User } from "@/contexts/AuthContext";

// Helper function to map Google's Photo object to PlacePhoto-like structure
const mapGooglePhotoToPlacePhoto = (photo: google.maps.places.Photo): google.maps.places.PlacePhoto => {
  return {
    getUrl: (opts?: google.maps.places.PhotoOptions) => photo.getURI(opts),
    height: photo.heightPx,
    width: photo.widthPx,
    html_attributions: photo.authorAttributions.map(attr => attr.displayName),
  } as google.maps.places.PlacePhoto; // Explicit assertion though structure should match
};

export const useBusinessSelection = (
  user: User | null,
  saveMapState: (businessPlaceId?: string) => void,
  checkExistingAnalysis: (placeId: string) => void,
  resetAnalysisState: () => void
) => {
  const [selectedBusiness, setSelectedBusiness] = useState<BusinessData | null>(null);
  const [isFlipped, setIsFlipped] = useState<boolean>(false);
  const [isRestoringBusiness, setIsRestoringBusiness] = useState<boolean>(false);

  // Track the last selected business place_id to prevent twitching
  const lastSelectedBusinessRef = useRef<string | undefined>(undefined);

  // Function to restore a selected business by its place_id
  const restoreSelectedBusiness = useCallback(async (placeId: string) => {
    if (isRestoringBusiness) return;

    console.log("Restoring business with place_id:", placeId);

    // Set flag to prevent concurrent restorations
    setIsRestoringBusiness(true);

    // Update the last selected business ref
    lastSelectedBusinessRef.current = placeId;

    try {
      // Check if Google Maps API and Places library import function are available
      if (!window.google?.maps?.importLibrary) {
        console.error("Google Maps JavaScript API not fully loaded or importLibrary is not available.");
        setIsRestoringBusiness(false);
        lastSelectedBusinessRef.current = undefined;
        return;
      }

      const { Place } = await google.maps.importLibrary("places") as typeof google.maps.places;

      const place = new Place({ id: placeId });
      const placeResult = await place.fetchFields({
        fields: [
          "id",
          "displayName",
          "formattedAddress",
          "internationalPhoneNumber",
          "nationalPhoneNumber",
          "websiteURI",
          "photos",
          "regularOpeningHours",
          "location",
          "types",
          "rating",
          "userRatingCount",
        ],
      });

      if (placeResult.place) {
        const fetchedPlace = placeResult.place;
        console.log("Restored business from saved state:", fetchedPlace);
        console.log("Raw Opening Hours from API (restoreSelectedBusiness):", fetchedPlace.regularOpeningHours);
        if (fetchedPlace.regularOpeningHours) {
            console.log("Weekday Text from API (restoreSelectedBusiness):", fetchedPlace.regularOpeningHours.weekday_text);
        }

        // Map the Place object to the existing BusinessData type
        const businessData: BusinessData = {
          id: fetchedPlace.id || '',
          name: fetchedPlace.displayName || '',
          formatted_address: fetchedPlace.formattedAddress || '',
          formatted_phone_number: fetchedPlace.internationalPhoneNumber || fetchedPlace.nationalPhoneNumber || '',
          website: fetchedPlace.websiteURI || '',
          photos: fetchedPlace.photos ? fetchedPlace.photos.map(mapGooglePhotoToPlacePhoto) : [],
          opening_hours: fetchedPlace.regularOpeningHours ?? undefined, // Ensure BusinessData.opening_hours is compatible with google.maps.places.OpeningHours
          place_id: fetchedPlace.id || '',
          geometry: fetchedPlace.location ? { location: fetchedPlace.location } : undefined, // Ensure BusinessData.geometry is compatible
          types: fetchedPlace.types || [],
          rating: fetchedPlace.rating === null || fetchedPlace.rating === undefined ? undefined : fetchedPlace.rating,
          user_ratings_total: fetchedPlace.userRatingCount === null || fetchedPlace.userRatingCount === undefined ? undefined : fetchedPlace.userRatingCount,
        };

        // First set the selected business
        setSelectedBusiness(businessData);
        setIsFlipped(false);

        // Then check for existing analysis data, only if place_id is available
        if (businessData.place_id) {
          checkExistingAnalysis(businessData.place_id);
        } else {
          resetAnalysisState();
        }
        setIsRestoringBusiness(false);
      } else {
        console.error("Error fetching place details: No place data returned");
        setIsRestoringBusiness(false);
        lastSelectedBusinessRef.current = undefined;
      }
    } catch (error) {
      console.error("Error fetching place details:", error);
      setIsRestoringBusiness(false);
      lastSelectedBusinessRef.current = undefined;
    }
  }, [isRestoringBusiness, checkExistingAnalysis, resetAnalysisState]);

  // Handle marker click
  const handleMarkerClick = useCallback(async (markerData: BusinessData) => {
    console.log("Marker clicked:", markerData);

    if (!markerData.place_id) {
      console.warn("Marker clicked without place_id. Displaying provided data without fetching details.", markerData);
      // If markerData is already complete enough, set it. Otherwise, consider not setting or resetting.
      setSelectedBusiness(markerData);
      setIsFlipped(false);
      resetAnalysisState(); // No place_id to check analysis for
      if (user) {
        saveMapState(undefined); // Or markerData.place_id if it's somehow relevant despite missing
      }
      return;
    }

    const placeId = markerData.place_id;

    // Prevent selecting the same business again or re-fetching if already selected
    if (selectedBusiness?.place_id === placeId) return;

    // Update the last selected business ref
    lastSelectedBusinessRef.current = placeId;

    // Double-check that we're not in the middle of restoring a business (optional, could use a different flag if needed)
    if (isRestoringBusiness) {
        console.log("Ignoring marker click while restoring another business");
        return;
    }

    try {
      if (!window.google?.maps?.importLibrary) {
        console.error("Google Maps JavaScript API not fully loaded or importLibrary is not available.");
        lastSelectedBusinessRef.current = undefined; // Clear ref if fetch fails early
        return;
      }

      const { Place } = await google.maps.importLibrary("places") as typeof google.maps.places;
      const place = new Place({ id: placeId });
      const placeResult = await place.fetchFields({
        fields: [
          "id",
          "displayName",
          "formattedAddress",
          "internationalPhoneNumber",
          "nationalPhoneNumber",
          "websiteURI",
          "photos",
          "regularOpeningHours", // Ensure this is fetched
          "location",
          "types",
          "rating",
          "userRatingCount",
        ],
      });

      if (placeResult.place) {
        const fetchedPlace = placeResult.place;
        console.log("Fetched details for marker click:", fetchedPlace);
        console.log("Raw Opening Hours from API (handleMarkerClick):", fetchedPlace.regularOpeningHours);
        if (fetchedPlace.regularOpeningHours) {
            console.log("Weekday Text from API (handleMarkerClick):", fetchedPlace.regularOpeningHours.weekday_text);
        }

        const businessData: BusinessData = {
          id: fetchedPlace.id || '',
          name: fetchedPlace.displayName || '',
          formatted_address: fetchedPlace.formattedAddress || '',
          formatted_phone_number: fetchedPlace.internationalPhoneNumber || fetchedPlace.nationalPhoneNumber || '',
          website: fetchedPlace.websiteURI || '',
          photos: fetchedPlace.photos ? fetchedPlace.photos.map(mapGooglePhotoToPlacePhoto) : [],
          opening_hours: fetchedPlace.regularOpeningHours ?? undefined,
          place_id: fetchedPlace.id || '',
          geometry: fetchedPlace.location ? { location: fetchedPlace.location } : undefined,
          types: fetchedPlace.types || [],
          rating: fetchedPlace.rating === null || fetchedPlace.rating === undefined ? undefined : fetchedPlace.rating,
          user_ratings_total: fetchedPlace.userRatingCount === null || fetchedPlace.userRatingCount === undefined ? undefined : fetchedPlace.userRatingCount,
        };

        setSelectedBusiness(businessData);
        setIsFlipped(false);
        // Check for existing analysis data, only if place_id is a valid non-empty string
        if (businessData.place_id) {
          checkExistingAnalysis(businessData.place_id);
        } else {
          resetAnalysisState();
        }
        if (user) {
          saveMapState(businessData.place_id);
        }
      } else {
        console.error("Error fetching place details for marker: No place data returned");
        lastSelectedBusinessRef.current = undefined;
      }
    } catch (error) {
      console.error("Error fetching place details for marker:", error);
      lastSelectedBusinessRef.current = undefined;
    }
  }, [selectedBusiness, user, checkExistingAnalysis, resetAnalysisState, saveMapState, isRestoringBusiness]);

  // Handle map click (for POIs)
  const handleMapClick = useCallback(async (event: google.maps.MapMouseEvent | google.maps.IconMouseEvent) => {
    // Check if it's a Place ID click event
    if ("placeId" in event && event.placeId) {
      const placeId = event.placeId;
      // Prevent selecting the same business again
      if (selectedBusiness?.place_id === placeId) return;

      // Update the last selected business ref
      lastSelectedBusinessRef.current = placeId;

      event.stop(); // Prevent info window from opening automatically

      // Double-check that we're not in the middle of restoring a business
      if (isRestoringBusiness) {
        console.log("Ignoring map click while restoring business");
        return;
      }

      try {
        // Check if Google Maps API and Places library import function are available
        if (!window.google?.maps?.importLibrary) {
          console.error("Google Maps JavaScript API not fully loaded or importLibrary is not available.");
          lastSelectedBusinessRef.current = undefined;
          return;
        }

        const { Place } = await google.maps.importLibrary("places") as typeof google.maps.places;

        const place = new Place({ id: placeId });
        const placeResult = await place.fetchFields({
          fields: [
            "id",
            "displayName",
            "formattedAddress",
            "internationalPhoneNumber",
            "nationalPhoneNumber",
            "websiteURI",
            "photos",
            "regularOpeningHours",
            "location",
            "types",
            "rating",
            "userRatingCount",
          ],
        });

        if (placeResult.place) {
          const fetchedPlace = placeResult.place;
          console.log("Place details:", fetchedPlace);
          console.log("Raw Opening Hours from API (handleMapClick):", fetchedPlace.regularOpeningHours);
          if (fetchedPlace.regularOpeningHours) {
              console.log("Weekday Text from API (handleMapClick):", fetchedPlace.regularOpeningHours.weekday_text);
          }

          // Map the Place object to the existing BusinessData type
          const businessData: BusinessData = {
            id: fetchedPlace.id || '',
            name: fetchedPlace.displayName || '',
            formatted_address: fetchedPlace.formattedAddress || '',
            formatted_phone_number: fetchedPlace.internationalPhoneNumber || fetchedPlace.nationalPhoneNumber || '',
            website: fetchedPlace.websiteURI || '',
            photos: fetchedPlace.photos ? fetchedPlace.photos.map(mapGooglePhotoToPlacePhoto) : [],
            opening_hours: fetchedPlace.regularOpeningHours ?? undefined, // Ensure BusinessData.opening_hours is compatible
            place_id: fetchedPlace.id || '',
            geometry: fetchedPlace.location ? { location: fetchedPlace.location } : undefined, // Ensure BusinessData.geometry is compatible
            types: fetchedPlace.types || [],
            rating: fetchedPlace.rating === null || fetchedPlace.rating === undefined ? undefined : fetchedPlace.rating,
            user_ratings_total: fetchedPlace.userRatingCount === null || fetchedPlace.userRatingCount === undefined ? undefined : fetchedPlace.userRatingCount,
          };

          // First set the selected business
          setSelectedBusiness(businessData);
          setIsFlipped(false); // Reset flip state

          // Then check for existing analysis data, only if place_id is available
          if (businessData.place_id) {
            checkExistingAnalysis(businessData.place_id);
          } else {
            resetAnalysisState();
          }

          // Save map state after selecting a business
          if (user) {
            saveMapState(businessData.place_id);
          }
        } else {
          console.error("Error fetching place details: No place data returned");
          lastSelectedBusinessRef.current = undefined;
        }
      } catch (error) {
        console.error("Error fetching place details:", error);
        lastSelectedBusinessRef.current = undefined;
      }
    }
    // Could add logic here to deselect business if clicking elsewhere on the map
  }, [selectedBusiness, isRestoringBusiness, user, checkExistingAnalysis, resetAnalysisState, saveMapState]);

  // Toggle flip state for business card
  const toggleFlip = useCallback(() => {
    setIsFlipped(!isFlipped);
  }, [isFlipped]);

  // Restore selected business when component mounts or user changes, if a placeId is stored
  useEffect(() => {
    // Only proceed if user is logged in, and we have a business ID to restore
    if (user && lastSelectedBusinessRef.current && !selectedBusiness && !isRestoringBusiness) {
      console.log("Attempting to restore business from saved state on mount/user change:", lastSelectedBusinessRef.current);
      restoreSelectedBusiness(lastSelectedBusinessRef.current);
    }
  }, [user, selectedBusiness, isRestoringBusiness, restoreSelectedBusiness, lastSelectedBusinessRef]);

  // Save selected business ID when component unmounts or selected business changes
  useEffect(() => {
    return () => {
      if (user && selectedBusiness?.place_id) {
        lastSelectedBusinessRef.current = selectedBusiness.place_id;
        saveMapState(selectedBusiness.place_id);
      } else if (user && !selectedBusiness) {
         // If no business is selected, clear the saved state
        saveMapState(undefined);
      }
    };
  }, [user, selectedBusiness, saveMapState]);

  return {
    selectedBusiness,
    setSelectedBusiness,
    isFlipped,
    setIsFlipped,
    isRestoringBusiness,
    setIsRestoringBusiness,
    lastSelectedBusinessRef,
    restoreSelectedBusiness,
    handleMarkerClick,
    handleMapClick,
    toggleFlip,
  };
};
