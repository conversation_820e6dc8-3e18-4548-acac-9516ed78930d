import type { Metadata } from "next";
import { Inter } from "next/font/google"; // Or another font if preferred
import "./globals.css";
import Navbar from "@/components/Navbar"; // Import Navbar
import Footer from "@/components/Footer"; // Import Footer
import { AuthProvider } from "@/contexts/AuthContext"; // Import AuthProvider
import { ThemeProvider } from "next-themes";
import SessionExpiredModal from "@/components/SessionExpiredModal"; // Import SessionExpiredModal
import PageTransitionContainer from "@/components/PageTransitionContainer"; // Import PageTransitionContainer
import StyledComponentsRegistry from '@/lib/registry'; // Import the registry

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "OpuMap Next", // Update title
  description: "OpuMap application migrated to Next.js", // Update description
  icons: {
    icon: [
      {
        url: '/weiss-ohne-bg-svg.svg?v=7',
        sizes: 'any',
        type: 'image/svg+xml',
      },
    ],
    apple: [
      {
        url: '/weiss-ohne-bg-svg.svg?v=7',
        sizes: '180x180',
        type: 'image/svg+xml',
      },
    ],
  },
};
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // NOTE: We cannot directly use hooks like useContext in Server Components (RootLayout is one by default).
  // Authentication state needs to be handled differently:
  // 1. Fetch auth state on the server (e.g., from cookies/session in middleware or layout).
  // 2. Pass auth state down to client components OR use a Client Component wrapper here.
  // 3. For simplicity now, we'll wrap everything in AuthProvider (a Client Component)
  //    and let Navbar/Pages handle conditional rendering based on context.

  return (
    // Add suppressHydrationWarning to ignore extension-added attributes
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" type="image/svg+xml" sizes="any" href="/weiss-ohne-bg-svg.svg?v=7" />
        <link rel="apple-touch-icon" type="image/svg+xml" sizes="180x180" href="/weiss-ohne-bg-svg.svg?v=7" />
      </head>
      <body className={`${inter.className} flex flex-col h-screen`}>
        <StyledComponentsRegistry>
          <AuthProvider>
            <ThemeProvider attribute="class" defaultTheme="light">
              <Navbar />
              <main className="flex-1 flex flex-col">
                <PageTransitionContainer>
                  {children}
                </PageTransitionContainer>
              </main>
              <Footer />
              <SessionExpiredModal />
            </ThemeProvider>
          </AuthProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
