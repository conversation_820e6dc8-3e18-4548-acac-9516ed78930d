// Analysis card styles
export const analysisCardStyles = {
  container: 'bg-card p-4 rounded-lg shadow border border-border w-full md:w-64',
  header: 'flex items-center justify-between mb-4',
  title: 'text-lg font-semibold text-card-foreground',
  newButton: (disabled: boolean) => `bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-xs font-medium py-1 px-2 rounded flex items-center space-x-1 transition ${disabled ? "opacity-50 cursor-not-allowed" : ""}`,
  dateSection: 'mb-4',
  dateLabel: 'text-xs text-muted-foreground',
  dateValue: 'text-sm font-medium text-card-foreground',
  viewButton: 'w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-sm rounded disabled:opacity-50 disabled:cursor-not-allowed transition'
};

// Analysis modal styles
export const modalStyles = {
  backdrop: 'fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4',
  container: 'bg-card rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden flex flex-col',
  header: 'flex justify-between items-center p-4 border-b border-border',
  title: 'text-xl font-semibold text-card-foreground',
  closeButton: 'text-muted-foreground hover:text-primary',
  body: 'p-6 overflow-y-auto flex-grow',
  dateInfo: 'mb-4 text-sm text-muted-foreground',
  content: 'prose prose-sm dark:prose-invert max-w-none p-4 rounded bg-background [&>strong]:font-bold [&>strong]:text-black dark:[&>strong]:text-white [&>h1]:text-3xl [&>h1]:font-bold [&>h1]:mb-6 [&>h2]:text-2xl [&>h2]:font-semibold [&>h2]:mb-4 [&>h3]:text-xl [&>h3]:mb-3 [&>p]:text-base [&>p]:leading-relaxed [&>ul]:list-disc [&>ul]:ml-6 [&>ul]:mb-4 [&>ol]:list-decimal [&>ol]:ml-6 [&>ol]:mb-4',
  footer: 'flex justify-end p-4 border-t border-border bg-muted/50 rounded-b-lg',
  closeButtonLarge: 'bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-5 rounded-lg transition duration-150 ease-in-out'
};

// Layout styles for analysis section
export const analysisLayoutStyles = {
  analysisContainer: 'flex flex-col w-full md:w-auto mt-4 md:mt-0',
  analysisCardsWrapper: 'flex flex-col md:flex-row gap-4 w-full'
};

// Additional styles for analysis components
export const analysisAdditionalStyles = {
  arrowSpan: 'ml-1',
  modalCloseIcon: 'w-6 h-6',
  indentedParagraph: 'ml-4'
};
