import { NextRequest, NextResponse } from 'next/server';
import { User } from '@/contexts/AuthContext'; // Import User type from AuthContext
import { createClient } from '@/utils/supabase/server';

// GET /api/profile - Fetch user profile
export async function GET() {
    try {
        // Create Supabase client
        const supabase = await createClient();

        // Get the authenticated user - this is the recommended approach
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
            console.error('Error getting authenticated user:', userError);
            return NextResponse.json({ error: 'Nicht authentifiziert.' }, { status: 401 });
        }

        const userId = user.id;

        // Fetch the user profile from the profiles table
        const { data: profileData, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            console.error('Error fetching profile:', error);
            return NextResponse.json({ error: '<PERSON><PERSON> beim Abrufen des Profils.' }, { status: 500 });
        }

        // Combine Supabase user data with profile data
        const userData = {
            id: userId,
            email: user.email,
            name: profileData?.name,
            company_name: profileData?.company_name,
            address: profileData?.address,
            phone: profileData?.phone,
            website: profileData?.website,
            employee_count: profileData?.employee_count,
            company_info_points: profileData?.company_info_points,
            createdAt: profileData?.created_at
        };

        // Return the user profile data
        return NextResponse.json({ profile: { user: userData } });

    } catch (error: unknown) {
        console.error('Fehler beim Abrufen des Profils:', error);
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({ error: 'Interner Serverfehler beim Abrufen des Profils.', details: message }, { status: 500 });
    }
}

// PUT /api/profile - Update user profile
export async function PUT(request: NextRequest) {
    try {
        // Create Supabase client
        const supabase = await createClient();

        // Get the authenticated user - this is the recommended approach
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
            console.error('Error getting authenticated user:', userError);
            return NextResponse.json({ error: 'Nicht authentifiziert.' }, { status: 401 });
        }

        const userId = user.id;
        const body = await request.json();

        // Extract expected fields
        const {
            name,
            email,
            company_name,
            address,
            phone,
            website,
            employee_count,
            company_info_points
        } = body;

        // Basic validation
        if (!name || !email) {
            return NextResponse.json({ error: 'Name und Email sind erforderlich.' }, { status: 400 });
        }

        // Prepare data for database update
        const profileData: Partial<User> = {
            name,
            company_name,
            address,
            phone,
            website,
            employee_count: employee_count ? String(employee_count) : null,
            company_info_points
        };

        // Update the profile in the database
        const { error } = await supabase
            .from('profiles')
            .update(profileData)
            .eq('id', userId);

        if (error) {
            console.error('Error updating profile:', error);
            return NextResponse.json({ error: 'Fehler beim Aktualisieren des Profils.' }, { status: 500 });
        }

        // Fetch the updated profile
        const { data: updatedProfile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

        // Combine Supabase user data with updated profile data
        const updatedUser = {
            id: userId,
            email: user.email,
            name: updatedProfile?.name,
            company_name: updatedProfile?.company_name,
            address: updatedProfile?.address,
            phone: updatedProfile?.phone,
            website: updatedProfile?.website,
            employee_count: updatedProfile?.employee_count,
            company_info_points: updatedProfile?.company_info_points,
            createdAt: updatedProfile?.created_at
        };

        // Return success message and the updated user profile
        return NextResponse.json({ message: 'Profil erfolgreich aktualisiert.', user: updatedUser });

    } catch (error) {
        console.error('Fehler beim Aktualisieren des Profils:', error);
        if (error instanceof SyntaxError) {
            return NextResponse.json({ error: 'Ungültige Anfrage-Daten.' }, { status: 400 });
        }
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({ error: 'Interner Serverfehler beim Speichern des Profils.', details: message }, { status: 500 });
    }
}
