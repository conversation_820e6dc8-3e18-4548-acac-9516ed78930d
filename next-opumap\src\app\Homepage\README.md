# Homepage Component

Diese Komponente dient als Startseite für eingeloggte Benutzer und zeigt eine interaktive Karte mit Unternehmensinformationen und Analysefunktionen.

## Struktur

Die Homepage-Komponente folgt einem hybriden Ansatz zur Codeorganisation:

- **Seitenspezifische Komponenten** befinden sich im `components/`-Unterordner
- **Seitenspezifische Hooks** befinden sich im `hooks/`-Unterordner
- **Seitenspezifische Styles** befinden sich im `styles/`-Unterordner
- **Wiederverwendbare Utilities** wurden in den globalen `src/utils/`-Ordner ausgelagert
- **Wiederverwendbare Styles** wurden in den globalen `src/styles/`-Ordner ausgelagert

## Hauptkomponenten

- `MapComponent.tsx` - Hauptkomponente, die die Karte und alle Unterkomponenten zusammenführt
- `BusinessCard.tsx` - Flip-Card für Unternehmensinformationen
- `AnalysisSection.tsx` - Komponente für den Analysebereich
- `OpuScannerSection.tsx` - Komponente für den OpuScanner-Bereich
- `AnalysisModal.tsx` - Modal für die Anzeige von Analyseergebnissen

## Custom Hooks

- `useMapState.ts` - Verwaltung des Kartenzustands
- `useBusinessSelection.ts` - Verwaltung der Unternehmensauswahl
- `useAnalysis.ts` - Verwaltung der Analysefunktionalität
- `useOpuScannerIntegration.ts` - Verwaltung der OpuScanner-Integration

## Verwendung

Die Homepage-Komponente wird automatisch als Startseite für eingeloggte Benutzer angezeigt. Sie wird in `app/page.tsx` importiert und bedingt gerendert, wenn ein Benutzer eingeloggt ist.
