import { useState, useEffect, useCallback } from 'react';
import { OpuscannerService } from '../apiService';
import { OpulabStrategy, DisplayScanResult } from '../types';

interface UseStrategiesResult {
  opulabStrategies: OpulabStrategy[];
  isLoadingOpulabStrategies: boolean;
  opulabLoadError: string | null;
  selectedOption: string;
  displayedScanResults: DisplayScanResult[];
  isLoadingDisplayedScanResults: boolean;
  displayLoadError: string | null;
  handleRadioChange: (value: string, showAllCompanies?: boolean) => void;
  loadOpulabStrategies: () => Promise<void>;
}

export const useStrategies = (): UseStrategiesResult => {
  const [opulabStrategies, setOpulabStrategies] = useState<OpulabStrategy[]>([]);
  const [isLoadingOpulabStrategies, setIsLoadingOpulabStrategies] = useState<boolean>(false);
  const [opulabLoadError, setOpulabLoadError] = useState<string | null>(null);

  const [selectedOption, setSelectedOption] = useState<string>('');
  const [displayedScanResults, setDisplayedScanResults] = useState<DisplayScanResult[]>([]);
  const [isLoadingDisplayedScanResults, setIsLoadingDisplayedScanResults] = useState<boolean>(false);
  const [displayLoadError, setDisplayLoadError] = useState<string | null>(null);

  // Function to load Opulab strategies
  const loadOpulabStrategies = useCallback(async () => {
    setIsLoadingOpulabStrategies(true);
    setOpulabLoadError(null);
    try {
      const loadedStrategies = await OpuscannerService.loadUserStrategies();
      setOpulabStrategies(loadedStrategies);
    } catch (error) {
      const errorMsg = 'Fehler beim Laden der Opulab Strategien.';
      console.error("Error loading Opulab strategies:", error);
      setOpulabLoadError(errorMsg);
      setOpulabStrategies([]);
    } finally {
      setIsLoadingOpulabStrategies(false);
    }
  }, []);

  // Initial load of strategies
  useEffect(() => {
    loadOpulabStrategies();
  }, [loadOpulabStrategies]);

  // Handle radio button change and load previous scans if needed
  const handleRadioChange = useCallback(async (value: string, showAllCompanies: boolean = false) => {
    setSelectedOption(value);
    setDisplayedScanResults([]); // Clear previous results on change
    setDisplayLoadError(null); // Clear previous errors

    if (value.startsWith('opulab_')) {
      const strategyIdStr = value.split('_')[1];
      const strategyId = parseInt(strategyIdStr, 10);

      if (!isNaN(strategyId)) {
        setIsLoadingDisplayedScanResults(true);
        try {
          // Use showAllCompanies to decide whether to include all companies or only selected ones
          const latestResults = await OpuscannerService.loadLatestScanResultsForStrategy(strategyId, showAllCompanies);
          setDisplayedScanResults(latestResults);
        } catch (error) {
          console.error('Error loading previous scan results:', error);
          const errorMsg = 'Fehler beim Laden früherer Scan-Ergebnisse.';
          setDisplayLoadError(errorMsg);
          setDisplayedScanResults([]);
        } finally {
          setIsLoadingDisplayedScanResults(false);
        }
      } else {
        console.warn('Invalid strategy ID parsed from value:', value);
        setDisplayedScanResults([]); // Clear if ID is invalid
      }
    } else {
      // Clear displayed results for non-Opulab options
      setDisplayedScanResults([]);
    }
  }, []); // No dependencies needed as it uses setters and constants

  return {
    opulabStrategies,
    isLoadingOpulabStrategies,
    opulabLoadError,
    selectedOption,
    displayedScanResults,
    isLoadingDisplayedScanResults,
    displayLoadError,
    handleRadioChange,
    loadOpulabStrategies,
  };
};
