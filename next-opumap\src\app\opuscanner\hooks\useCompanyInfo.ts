import { useState, useEffect, useCallback, ChangeEvent } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { OpuscannerService } from '../apiService';
import { UserProfileUpdateData } from '../types';

interface UseCompanyInfoProps {
  initialCompanyInfo?: string;
  onSaveSuccess?: () => void; // Optional callback on successful save
  onSaveError?: (errorMessage: string) => void; // Optional callback on save error
}

export const useCompanyInfo = ({ initialCompanyInfo = '', onSaveSuccess, onSaveError }: UseCompanyInfoProps = {}) => {
  const { user, refreshUserProfile } = useAuth();
  const [companyInfo, setCompanyInfo] = useState<string>(initialCompanyInfo);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccessMessage, setSaveSuccessMessage] = useState<string | null>(null);

  // Update company info when user data is available
  useEffect(() => {
    if (user?.company_info_points) {
      setCompanyInfo(user.company_info_points);
    } else {
      // Optionally set to initial prop or empty if user has no info yet
      setCompanyInfo(initialCompanyInfo);
    }
  }, [user?.company_info_points, initialCompanyInfo]);

  // Handler for textarea change (traditional textarea event)
  const handleTextareaChange = useCallback((e: ChangeEvent<HTMLTextAreaElement>) => {
    setCompanyInfo(e.target.value);
    setSaveError(null); // Clear error on edit
    setSaveSuccessMessage(null); // Clear success message on edit
  }, []);

  // Handler for markdown editor change (direct value)
  const handleMarkdownChange = useCallback((value?: string) => {
    setCompanyInfo(value || '');
    setSaveError(null); // Clear error on edit
    setSaveSuccessMessage(null); // Clear success message on edit
  }, []);

  // Handler for saving company info
  const handleSaveCompanyInfo = useCallback(async () => {
    setSaveError(null);
    setSaveSuccessMessage(null);
    setIsSaving(true);
    // const token = localStorage.getItem("token");

    // if (!token) {
    //   const errorMsg = "Fehler: Nicht authentifiziert. Bitte neu einloggen.";
    //   setSaveError(errorMsg);
    //   setIsSaving(false);
    //   onSaveError?.(errorMsg);
    //   return;
    // }

    if (!user) {
        const errorMsg = "Benutzerdaten nicht verfügbar. Bitte laden Sie die Seite neu.";
        setSaveError(errorMsg);
        setIsSaving(false);
        onSaveError?.(errorMsg);
        return;
    }

    try {
      // Construct profile data safely, handling potentially null user fields
      const profileData: UserProfileUpdateData = {
        name: user.name ?? null,
        email: user.email ?? null,
        company_name: user.company_name ?? null,
        address: user.address ?? null,
        phone: user.phone ?? null,
        website: user.website ?? null,
        employee_count: typeof user.employee_count === 'string' ? parseInt(user.employee_count, 10) : (user.employee_count ?? null),
        company_info_points: companyInfo
      };

      const result = await OpuscannerService.saveCompanyInfo(profileData);
      const successMsg = "Unternehmensinformationen erfolgreich gespeichert.";
      setSaveSuccessMessage(successMsg);

      // Update auth context if the user profile was successfully saved
      if (result && refreshUserProfile) {
        // No need for token here, refreshUserProfile handles fetching the latest user data
        refreshUserProfile();
      }
      onSaveSuccess?.();
    } catch (error) {
      const errorMsg = error instanceof Error ? `Fehler: ${error.message}` : "Unbekannter Fehler";
      setSaveError(errorMsg);
      onSaveError?.(errorMsg);
    } finally {
      setIsSaving(false);
    }
  }, [user, companyInfo, refreshUserProfile, onSaveSuccess, onSaveError]);

  return {
    companyInfo,
    isSaving,
    saveError,
    saveSuccessMessage,
    handleTextareaChange,
    handleMarkdownChange,
    handleSaveCompanyInfo,
    setSaveError, // Expose setter to allow clearing error externally if needed
    setSaveSuccessMessage // Expose setter to allow clearing success message externally
  };
};