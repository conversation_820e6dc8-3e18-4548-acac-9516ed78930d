# Login Component

Diese Komponente dient zur Benutzerauthentifizierung und bietet sowohl Login- als auch Registrierungsfunktionalität.

## Struktur

Die Login-Komponente folgt dem gleichen Organisationsansatz wie andere Seitenkomponenten:

- **Hauptkomponente** - `Login.tsx` enthält die Hauptlogik und das UI der Login/Registrierungsseite
- **Seitenspezifische Komponenten** können bei Bedarf im `components/`-Unterordner hinzugefügt werden
- **Seitenspezifische Styles** können bei Bedarf im `styles/`-Unterordner hinzugefügt werden

## Funktionalität

- Bietet ein Formular für Login und Registrierung
- Wechselt zwischen Login- und Registrierungsmodus
- Validiert Benutzereingaben
- Kommuniziert mit der API für Authentifizierung
- Zeigt Erfolgs- und Fehlermeldungen an
- Leitet nach erfolgreicher Anmeldung zur Startseite weiter
- Verwendet das globale Farbschema der Anwendung für konsistentes Design

## API-Integration

Die Komponente kommuniziert mit folgenden API-Endpunkten:
- `/api/auth/login` - Für die Benutzeranmeldung
- `/api/auth/register` - Für die Benutzerregistrierung

## Verwendung

Die Login-Komponente wird in `app/login/page.tsx` importiert und als Hauptkomponente der Login-Seite verwendet. Sie ist über die Route `/login` erreichbar und wird auch von anderen Komponenten (wie der Landingpage) verlinkt.
