'use client';

import React from 'react';
import {
  <PERSON><PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import MarkdownEditor from '@/components/MarkdownEditor';

// Import styles
import { formStyles, cardStyles } from '../styles';

interface StrategieBriefingProps {
  shortTermTasks: string;
  longTermGoals: string;
  onShortTermTasksChange: (value: string) => void;
  onLongTermGoalsChange: (value: string) => void;
}

const StrategieBriefing: React.FC<StrategieBriefingProps> = ({
  shortTermTasks,
  longTermGoals,
  onShortTermTasksChange,
  onLongTermGoalsChange
}) => {
  return (
    <div className="h-full flex flex-col">
      <CardHeader className={cardStyles.header}>
        <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>Strategiebriefing</CardTitle>
        <p className={cardStyles.subtitle}>Definieren Sie kurz- und langfristige Ziele für Ihre Strategie</p>
      </CardHeader>
      <CardContent className={`flex-grow ${cardStyles.content}`}>
        {/* Changed to horizontal layout with flex-row */}
        <div className="flex flex-col md:flex-row gap-6 h-full">
          <div className="flex-1 flex flex-col">
            <label htmlFor="short-term" className={formStyles.label}>Kurzfristige To-Dos (1-3 Monate)</label>
            <div className="flex-1 min-h-[120px]">
              <MarkdownEditor
                value={shortTermTasks}
                onChange={(value) => onShortTermTasksChange(value || '')}
                height={240}
                preview="edit"
              />
            </div>
            <p className={formStyles.helperText}>
              Definieren Sie konkrete, zeitnah umsetzbare Maßnahmen, die direkt zur Strategieumsetzung beitragen.
            </p>
          </div>
          <div className="flex-1 flex flex-col">
            <label htmlFor="long-term" className={formStyles.label}>Langfristige Ziele (1-3 Jahre)</label>
            <div className="flex-1 min-h-[120px]">
              <MarkdownEditor
                value={longTermGoals}
                onChange={(value) => onLongTermGoalsChange(value || '')}
                height={240}
                preview="edit"
              />
            </div>
            <p className={formStyles.helperText}>
              Formulieren Sie messbare, strategische Ziele, die Ihr Unternehmen nachhaltig voranbringen und zur Vision passen.
            </p>
          </div>
        </div>
      </CardContent>
    </div>
  );
};

export default StrategieBriefing;
