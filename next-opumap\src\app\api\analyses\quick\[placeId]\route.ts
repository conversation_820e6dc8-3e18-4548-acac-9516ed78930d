import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { AnalysisResult } from '@/types';

// GET /api/analyses/quick/[placeId] - Fetch latest quick analysis by placeId
export async function GET(request: NextRequest, context: { params: { placeId: string } }) {
    try {
        // In Next.js 15, we need to await params before accessing its properties
        const { params } = context; // o await needed here
        const { placeId } = await params;

        if (!placeId) {
            return NextResponse.json({ error: 'Place ID ist erforderlich' }, { status: 400 });
        }

        // Create a Supabase client
        const supabase = await createClient();

        // First, find the company_id for the given place_id
        // Include both active and deleted companies to allow re-adding previously deleted companies
        const { data: companyData, error: companyError } = await supabase
            .from('companies')
            .select('id')
            .eq('place_id', placeId)
            .single();

        if (companyError) {
            if (companyError.code === 'PGRST116') {
                return NextResponse.json({
                    message: '<PERSON><PERSON> mit dieser Place ID gefunden'
                }, { status: 404 });
            }
            throw companyError;
        }

        // Now query the analyses table for quick analyses with the found company_id
        const { data, error } = await supabase
            .from('analyses')
            .select('*')
            .eq('company_id', companyData.id)
            .eq('type', 'quick')
            .order('analysis_date', { ascending: false })
            .limit(1)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({
                    message: 'Keine Schnellanalyse für dieses Unternehmen gefunden'
                }, { status: 404 });
            }

            throw error;
        }

        // Return the analysis data in the expected format
        const responseData: AnalysisResult = {
            analysisContent: data.content,
            analysisDate: data.analysis_date,
            // Include other fields if needed
        };

        return NextResponse.json(responseData);

    } catch (error: unknown) {
        console.error('Fehler beim Abrufen der Schnellanalyse:', error);
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({
            error: 'Serverfehler beim Abrufen der Analyse',
            details: message
        }, { status: 500 });
    }
}
