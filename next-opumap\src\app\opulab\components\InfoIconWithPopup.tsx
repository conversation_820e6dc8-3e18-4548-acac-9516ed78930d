'use client';

import React from 'react';

interface InfoIconWithPopupProps {
  aspectLabel: string;
}

const InfoIconWithPopup: React.FC<InfoIconWithPopupProps> = ({ aspectLabel }) => {
  return (
    <span className="relative inline-block ml-2 cursor-help group">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-gray-500 hover:text-gray-700"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <div className="absolute invisible opacity-0 w-64 bg-white text-gray-800 text-center text-sm py-2 px-3 rounded-md shadow-lg z-10 bottom-full left-1/2 transform -translate-x-1/2 mb-1 group-hover:visible group-hover:opacity-100 transition-opacity duration-300 border border-gray-200">
        {(() => {
          switch (aspectLabel) {
            case 'Marketingstrategien':
              return "Wählen Sie diese Option, um durch integrierte Marketingstrategien die Markenbekanntheit und den Marktanteil effizient zu steigern.";
            case 'Vertrieb & Expansion':
              return "Diese Option bietet Einblicke, um Ihre Vertriebsstrategie durch gezielte Markterweiterung und moderne Technologien effizient auszubauen.";
            case 'Produktentwicklung':
              return "Durch diese Option können Sie die Produktentwicklung beschleunigen und optimieren, um innovative, nachhaltige und kundenzentrierte Lösungen zu schaffen.";
            case 'Kooperationen':
              return "Wählen Sie diese Option, um durch strategische Kooperationen Synergien zu nutzen und die Wettbewerbsfähigkeit zu steigern.";
            case 'Innovationsprozesse':
              return "Diese Option unterstützt die Verbesserung und Effizienzsteigerung von Neuerungen in Ihrem Unternehmen.";
            case 'Prozessoptimierung':
              return "Diese Option verbessert Abläufe mit moderner Technik, um Prozesse effizienter und kostensparender zu gestalten.";
            default:
              return "Informationen zu diesem Aspekt sind derzeit nicht verfügbar.";
          }
        })()}
      </div>
    </span>
  );
};

export default InfoIconWithPopup;