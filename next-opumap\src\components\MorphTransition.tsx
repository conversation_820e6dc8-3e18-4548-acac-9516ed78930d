'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface MorphTransitionProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  type?: 'fade' | 'scale' | 'slide' | 'morph';
  transitionState?: 'in' | 'out' | 'none';
  onTransitionComplete?: () => void;
}

const MorphTransition: React.FC<MorphTransitionProps> = ({
  children,
  className,
  direction = 'up',
  type = 'morph',
  transitionState = 'none',
  onTransitionComplete
}) => {
  const router = useRouter();
  const [isTransitioning, setIsTransitioning] = useState(transitionState !== 'none');

  // Handle transition state changes
  useEffect(() => {
    if (transitionState !== 'none') {
      setIsTransitioning(true);
      
      // If transitioning out, call onTransitionComplete after animation
      if (transitionState === 'out' && onTransitionComplete) {
        const timer = setTimeout(() => {
          onTransitionComplete();
        }, 300); // Match duration-300
        
        return () => clearTimeout(timer);
      }
      return undefined;
    } else {
      setIsTransitioning(false);
      return undefined;
    }
  }, [transitionState, onTransitionComplete]);

  // Define transition effects based on direction, type and state
  const getTransitionClasses = () => {
    // Base transition classes - smoother cubic bezier curve for more professional feel
    const baseClasses = "transition-all duration-300 ease-[cubic-bezier(0.34,1.56,0.64,1)]";

    if (!isTransitioning || transitionState === 'none') {
      return `${baseClasses} opacity-100 scale-100 translate-x-0 translate-y-0 rotate-0`;
    }

    // Different transition effects with direction consideration
    const isOut = transitionState === 'out';
    
    switch (type) {
      case 'fade':
        return `${baseClasses} opacity-0`;
      case 'scale':
        return `${baseClasses} opacity-0 ${isOut ? 'scale-95' : 'scale-105'}`;
      case 'slide':
        switch (direction) {
          case 'up': return `${baseClasses} opacity-0 ${isOut ? '-translate-y-4' : 'translate-y-4'}`;
          case 'down': return `${baseClasses} opacity-0 ${isOut ? 'translate-y-4' : '-translate-y-4'}`;
          case 'left': return `${baseClasses} opacity-0 ${isOut ? '-translate-x-4' : 'translate-x-4'}`;
          case 'right': return `${baseClasses} opacity-0 ${isOut ? 'translate-x-4' : '-translate-x-4'}`;
          default: return `${baseClasses} opacity-0 ${isOut ? '-translate-y-4' : 'translate-y-4'}`;
        }
      case 'morph':
      default:
        // Enhanced morph effect with slight rotation for more dynamic feel
        switch (direction) {
          case 'up': 
            return `${baseClasses} opacity-0 ${isOut ? 'scale-95 -translate-y-2' : 'scale-105 translate-y-2'} ${isOut ? '-rotate-1' : 'rotate-1'}`;
          case 'down': 
            return `${baseClasses} opacity-0 ${isOut ? 'scale-95 translate-y-2' : 'scale-105 -translate-y-2'} ${isOut ? 'rotate-1' : '-rotate-1'}`;
          case 'left': 
            return `${baseClasses} opacity-0 ${isOut ? 'scale-95 -translate-x-2' : 'scale-105 translate-x-2'} ${isOut ? 'rotate-1' : '-rotate-1'}`;
          case 'right': 
            return `${baseClasses} opacity-0 ${isOut ? 'scale-95 translate-x-2' : 'scale-105 -translate-x-2'} ${isOut ? '-rotate-1' : 'rotate-1'}`;
          default: 
            return `${baseClasses} opacity-0 ${isOut ? 'scale-95 -translate-y-2' : 'scale-105 translate-y-2'} ${isOut ? '-rotate-1' : 'rotate-1'}`;
        }
    }
    // Fallback return for TypeScript
    return baseClasses;
  };

  // Expose a method to trigger transition to a specific path
  const transitionTo = React.useCallback((path: string) => {
    console.log(`Transitioning to ${path}`);
    setIsTransitioning(true);

    // Wait for animation to complete before navigating
    setTimeout(() => {
      router.push(path);
    }, 300); // Match duration-300
  }, [setIsTransitioning, router]);

  // Make the transition method available to children
  useEffect(() => {
    // Add the transition method to the window object for easy access
    window.morphTransitionTo = transitionTo;

    return () => {
      // Clean up
      delete window.morphTransitionTo;
    };
  }, [transitionTo]);

  return (
    <div
      className={cn(
        getTransitionClasses(),
        className
      )}
    >
      {children}
    </div>
  );
};

export default MorphTransition;
