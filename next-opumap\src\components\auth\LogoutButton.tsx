'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { setIntentionalLogout } from '@/components/SessionExpiredModal';
import React from 'react';

interface LogoutButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline';
  size?: 'sm' | 'lg' | 'icon' | 'md';
  className?: string;
  asChild?: boolean;
}

export default function LogoutButton({
  variant = 'default',
  size = 'md',
  className = '',
  children,
  ...props
}: LogoutButtonProps) {
  const { signOut } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    // Markieren, dass dies ein absichtliches Ausloggen ist
    setIntentionalLogout();
    await signOut();
    router.push('/');
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleLogout}
      className={`bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white ${className || ''}`}
      {...props}
    >
      {children}
    </Button>
  );
}
