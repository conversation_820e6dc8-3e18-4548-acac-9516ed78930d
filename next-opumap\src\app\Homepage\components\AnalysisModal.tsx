import React from "react";
import { BusinessData } from "@/types";
import {
  modalStyles,
  analysisAdditionalStyles
} from "../styles";
import { iconPaths } from "@/styles";
import { formatAnalysisDate } from "@/utils";
import ReactMarkdown from 'react-markdown';

interface AnalysisModalProps {
  showModal: boolean;
  setShowModal: (show: boolean) => void;
  analysisType: "schnell" | "tief";
  analysisData: string | null;
  analysisDate: string | null;
  selectedBusiness: BusinessData | null;
}

/**
 * Modal component to display analysis results
 */
const AnalysisModal: React.FC<AnalysisModalProps> = ({
  showModal,
  setShowModal,
  analysisType,
  analysisData,
  analysisDate,
  selectedBusiness
}) => {
  if (!showModal || !analysisData) return null;

  return (
    // Modal backdrop
    <div className={modalStyles.backdrop}>
      {/* Modal content */}
      <div className={modalStyles.container}>
        {/* Modal header */}
        <div className={modalStyles.header}>
          <h2 className={modalStyles.title}>
            {analysisType === "schnell" ? "Schnelle" : "Tiefe"} Analyse: {selectedBusiness?.name}
          </h2>
          {/* Close button */}
          <button onClick={() => setShowModal(false)} className={modalStyles.closeButton}>
            <svg className={analysisAdditionalStyles.modalCloseIcon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={iconPaths.close.path}></path>
            </svg>
          </button>
        </div>
        {/* Modal body - scrollable */}
        <div className={modalStyles.body}>
          <div className={modalStyles.dateInfo}>
            Analyse vom: {formatAnalysisDate(analysisDate)}
          </div>
          {/* Use prose for basic markdown-like formatting if analysis content is text */}
          <div className={`${modalStyles.content} prose dark:prose-invert`}>
            {/* Use ReactMarkdown to render analysisData */}
            <ReactMarkdown>{analysisData}</ReactMarkdown>
          </div>
        </div>
        {/* Modal footer */}
        <div className={modalStyles.footer}>
          <button
            onClick={() => setShowModal(false)}
            className={modalStyles.closeButtonLarge}
          >
            Schließen
          </button>
        </div>
      </div>
    </div>
  );
};

export default AnalysisModal;
