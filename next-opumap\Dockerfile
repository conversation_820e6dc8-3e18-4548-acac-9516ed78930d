# Basis-Image
FROM node:23-alpine AS base

# Arbeitsverzeichnis festlegen
WORKDIR /app

# Abhängigkeiten installieren
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine für mehr Informationen
RUN apk add --no-cache libc6-compat

# Kopieren der package.json und package-lock.json
COPY package.json package-lock.json* ./

# Installieren der Abhängigkeiten
RUN npm ci

# Build-Stage
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Umgebungsvariablen für den Build
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Build der Anwendung
RUN npm run build

# Produktions-Stage
FROM base AS runner
WORKDIR /app

# Umgebungsvariablen für die Produktion
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Nicht-Root-Benutzer für Sicherheit
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Kopieren der Build-Artefakte
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Benutzer wechseln
USER nextjs

# Port freigeben
EXPOSE 3000

# Umgebungsvariable für den Host
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Anwendung starten
CMD ["node", "server.js"]
