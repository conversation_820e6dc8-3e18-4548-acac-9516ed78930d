# PostgreSQL Security Checklist

## 1. Datenverschlüsselung
- [ ] Passwörter mit bcrypt hashen (mind. 12 <PERSON><PERSON>)
- [ ] Sensible Felder (API Keys, Emails) in DB verschlüsseln
- [ ] `pgcrypto` Extension aktiviert

## 2. Zugriffskontrolle
- [ ] Keine `public` <PERSON><PERSON><PERSON>
- [ ] Rollen mit minimalen Privilegien:
  ```sql
  REVOKE ALL ON DATABASE opumap FROM public;
  CREATE ROLE read_only;
  GRANT CONNECT ON DATABASE opumap TO read_only;
  ```

## 3. JWT Validierung
- [ ] Token-Signatur prüfen
- [ ] Expiry-Time (max 1h für access tokens)
- [ ] Refresh Token Rotation

## 4. API Sicherheit
- [ ] Rate Limiting (z.B. express-rate-limit)
- [ ] CORS strikt eingeschränkt
- [ ] Helmet.js für HTTP Header

## 5. Audit-Logging
- [ ] Login-Versuche protokollieren
- [ ] Sensitive Operationen loggen
  ```javascript
  // Beispiel-Logging Middleware
  app.use((req, res, next) => {
    if(req.path.includes('/admin')) {
      logAdminAccess(req.user);
    }
    next();
  });
  ```

## Prüfkommandos:
```bash
# Aktive Verbindungen checken
psql -c "SELECT usename, client_addr FROM pg_stat_activity;"

# Berechtigungen anzeigen
psql -c "\dp *.*"

# Verschlüsselung prüfen
psql -c "SELECT * FROM pg_available_extensions WHERE name = 'pgcrypto';"
```
