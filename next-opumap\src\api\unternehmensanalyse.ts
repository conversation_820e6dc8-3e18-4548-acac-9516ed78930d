// Define the expected input data structure
interface CompanyAnalysisData {
    companyName?: string;
    name?: string;
    address?: string;
    phone?: string;
    website?: string;
    employeeCount?: string | number;
    // Add other fields if necessary, but email is excluded based on original comment
}

/**
 * Runs an analysis on company data using the Perplexity API.
 * IMPORTANT: Calling Perplexity API directly from the client exposes the API key.
 * Consider moving this logic to a Next.js API route for better security.
 * @param companyData - Data of the company to analyze.
 * @returns The analysis content as a string.
 */
export async function runAnalysis(companyData: CompanyAnalysisData): Promise<string> {
    // Construct prompt safely
    let prompt = "Gebe mir in Stichpunkten alle Informationen Über das folgende Unternehmen. Lege dabei den Fokus auf Key metriken die für eine Unternehmensanalyse wichtig sind:\n\n";
    if (companyData.companyName) prompt += `- Unternehmensname: ${companyData.companyName}\n`;
    if (companyData.name) prompt += `- Name (Ansprechpartner): ${companyData.name}\n`; // Clarified label
    if (companyData.address) prompt += `- Adresse: ${companyData.address}\n`;
    if (companyData.phone) prompt += `- Telefonnummer: ${companyData.phone}\n`;
    if (companyData.website) prompt += `- Webseite: ${companyData.website}\n`;
    if (companyData.employeeCount) prompt += `- Mitarbeiteranzahl: ${companyData.employeeCount}\n`;

    // Get API key
    const token = process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;
    if (!token) {
        console.error("Perplexity API Token (NEXT_PUBLIC_PERPLEXITY_API_KEY) is not defined.");
        throw new Error("Perplexity API Token ist nicht konfiguriert.");
    }

    const options = {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            accept: 'application/json',
        },
        body: JSON.stringify({
            model: "sonar", // Using sonar-small-online (similar to original 'sonar')
            messages: [
                { role: "system", content: "Be precise and concise. Provide key metrics relevant for company analysis in bullet points. No explanations or extra formatting." },
                { role: "user", content: prompt }
            ],
            // Adjust parameters as needed
            // max_tokens: 3000,
            // temperature: 0.2,
        })
    };

    try {
        const response = await fetch('https://api.perplexity.ai/chat/completions', options);
        if (!response.ok) {
            const errorText = await response.text();
             console.error("Perplexity API Error Response:", errorText);
            throw new Error(`Perplexity API Fehler (${response.status}): ${errorText.substring(0, 100)}...`);
        }

        const data = await response.json();

        // Extract analysis content
        if (data?.choices?.[0]?.message?.content) {
            return data.choices[0].message.content;
        } else {
             console.warn("Could not extract analysis content from Perplexity response:", data);
             // Return a default message or stringified data on failure
             return "Analyseinformationen konnten nicht extrahiert werden.";
             // return JSON.stringify(data, null, 2); // Alternative: return raw data string
        }
    } catch (error) { // Use unknown type for error
        console.error("Fehler in runAnalysis:", error);
        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
        throw new Error(`Fehler bei der Unternehmensanalyse: ${message}`);
    }
}
