import pkg from 'pg';
const { Client } = pkg;

/**
 * Helper function to get a PostgreSQL client configuration with SSL disabled for development.
 * In production, this should be properly configured.
 */
export function getPgClientConfig() {
  const connectionString = process.env.POSTGRES_URL;
  if (!connectionString) {
    throw new Error('POSTGRES_URL environment variable not found.');
  }
  
  // Disable SSL certificate validation for development
  // This is a global setting that affects all Node.js HTTPS requests
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  
  return {
    connectionString,
    ssl: { rejectUnauthorized: false }
  };
}

/**
 * Creates a new PostgreSQL client with SSL disabled for development.
 * In production, this should be properly configured.
 */
export function createPgClient() {
  return new Client(getPgClientConfig());
}
