# OpuMap Next.js

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Codebase-Struktur und Refaktorisierungsansatz

Dieses Projekt folgt einem hybriden Ansatz zur Codeorganisation, der die Vorteile einer feature-basierten und einer globalen Strukturierung kombiniert.

### Hybrider Strukturierungsansatz

Der hybride Ansatz teilt den Code in zwei Hauptkategorien:

1. **Globale, wiederverwendbare Elemente** - In übergeordneten Ordnern auf Projektebene
2. **Feature-spezifische Elemente** - In den jeweiligen Feature-Ordnern

Diese Struktur bietet mehrere Vorteile:
- **Klare Trennung** zwischen wiederverwendbaren und spezifischen Komponenten
- **Bessere Auffindbarkeit** durch logische Gruppierung
- **Reduzierte Duplizierung** durch zentrale Verwaltung gemeinsamer Funktionalitäten
- **Verbesserte Wartbarkeit** durch klare Zuständigkeiten
- **Skalierbarkeit** für zukünftige Erweiterungen

### Ordnerstruktur

```
src/
  components/           # Globale, wiederverwendbare UI-Komponenten
    ui/                 # Grundlegende UI-Elemente (Buttons, Inputs, etc.)
    Layout/             # Layoutkomponenten (Header, Footer, etc.)
    ...

  hooks/                # Globale, wiederverwendbare React Hooks
    useWindowSize.ts
    useLocalStorage.ts
    ...

  utils/                # Globale Hilfsfunktionen
    formatters.ts       # Formatierungsfunktionen (Datum, Währung, etc.)
    validators.ts       # Validierungsfunktionen
    ...

  styles/               # Globale Styles
    iconPaths.ts        # SVG-Pfade für Icons
    theme.ts            # Themendefinitionen
    ...

  types/                # Globale TypeScript-Typdefinitionen
    index.ts
    ...

  contexts/             # Globale React Contexts
    AuthContext.tsx
    ThemeContext.tsx
    ...

  api/                  # API-Funktionen und -Clients
    ...

  app/                  # Next.js App Router Struktur
    layout.tsx          # Root Layout
    page.tsx            # Homepage

    # Feature-basierte Ordner für Seiten/Routen
    Homepage/           # Homepage-Feature (wenn eingeloggt)
      components/       # Feature-spezifische Komponenten
        BusinessCard.tsx
        AnalysisSection.tsx
        ...

      hooks/            # Feature-spezifische Hooks
        useMapState.ts
        useBusinessSelection.ts
        ...

      styles/           # Feature-spezifische Styles
        mapStyles.ts
        businessCardStyles.ts
        ...

      MapComponent.tsx  # Hauptkomponente des Features
      constants.ts      # Feature-spezifische Konstanten
      index.tsx         # Exportiert die Hauptkomponente
      README.md         # Feature-Dokumentation

    profile/            # Profilseite-Feature
      components/
      hooks/
      ...

    opuscanner/         # OpuScanner-Feature
      components/
      hooks/
      ...
```

### Entscheidungskriterien für die Zuordnung

Bei der Entscheidung, ob ein Element global oder feature-spezifisch sein sollte, werden folgende Kriterien angewendet:

#### Global platzieren, wenn:
- Das Element in mehreren Features verwendet wird oder werden könnte
- Es sich um eine grundlegende Funktionalität handelt (z.B. Formatierungsfunktionen, UI-Basiskomponenten)
- Es sich um projektweite Konfigurationen oder Definitionen handelt

#### Feature-spezifisch platzieren, wenn:
- Das Element nur innerhalb eines Features verwendet wird
- Es eng mit der Geschäftslogik des Features verbunden ist
- Es spezifische Anpassungen für das Feature enthält

### Import-Konventionen

- Globale Elemente werden mit absoluten Pfaden importiert: `import { formatDate } from "@/utils/formatters";`
- Feature-spezifische Elemente werden mit relativen Pfaden importiert: `import { BusinessCard } from "./components/BusinessCard";`

### Vorteile dieses Ansatzes

1. **Bessere Übersichtlichkeit**: Klare Trennung zwischen globalen und feature-spezifischen Elementen
2. **Verbesserte Wiederverwendbarkeit**: Gemeinsam genutzte Funktionalitäten sind zentral verfügbar
3. **Reduzierte Kopplung**: Features sind weitgehend unabhängig voneinander
4. **Einfachere Wartung**: Änderungen an einem Feature beeinflussen nicht andere Features
5. **Bessere Skalierbarkeit**: Neue Features können einfach hinzugefügt werden, ohne die bestehende Struktur zu beeinträchtigen
6. **Verbesserte Teamarbeit**: Mehrere Entwickler können gleichzeitig an verschiedenen Features arbeiten

### Best Practices

1. **Regelmäßige Refaktorisierung**: Identifizieren Sie Elemente, die in mehreren Features verwendet werden, und verschieben Sie sie in die globalen Ordner
2. **Dokumentation**: Jedes Feature sollte eine README.md-Datei haben, die seine Funktionalität und Struktur beschreibt
3. **Konsistente Benennung**: Verwenden Sie konsistente Namenskonventionen für Dateien und Ordner
4. **Klare Verantwortlichkeiten**: Jede Komponente, Hook oder Funktion sollte eine klare, einzelne Verantwortlichkeit haben
5. **Vermeidung von Zirkelbezügen**: Vermeiden Sie zirkuläre Abhängigkeiten zwischen Features
6. **Modularität**: Teilen Sie große Komponenten in kleinere, wiederverwendbare Teile auf

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
