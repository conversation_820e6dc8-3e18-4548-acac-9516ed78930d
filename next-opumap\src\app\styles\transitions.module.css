/* Transition animations - simplified and optimized */
.pageTransition {
  animation-duration: 250ms;
  animation-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1); /* Material Design standard easing */
  animation-fill-mode: both;
  will-change: opacity, transform;
  overflow: hidden; /* Prevent scrollbars during animation */
}

/* Container to prevent layout shifts */
.transitionContainer {
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: relative;
}

/* Simple fade in animation */
.fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Simple fade out animation */
.fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.98);
  }
}

/* Smooth morph in animation - minimal movement to prevent layout shifts */
.morphIn {
  animation-name: morphIn;
}

@keyframes morphIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Smooth morph out animation - minimal movement to prevent layout shifts */
.morphOut {
  animation-name: morphOut;
}

@keyframes morphOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.98);
  }
}
