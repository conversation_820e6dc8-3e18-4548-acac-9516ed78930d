# OpuMap Next.js Deployment-Anleitung

Diese Anleitung beschreibt, wie Sie die OpuMap Next.js-Anwendung für die Produktion deployen können.

## Voraussetzungen

- Node.js 18.x oder höher
- npm 8.x oder höher
- PM2 (wird automatisch installiert, wenn nicht vorhanden)
- Eine `.env.local` Datei mit den notwendigen Umgebungsvariablen

## Umgebungsvariablen

Stellen Si<PERSON> sicher, dass Ihre `.env.local` Datei die folgenden Variablen enthält:

```
# Supabase Konfiguration
NEXT_PUBLIC_SUPABASE_URL=https://ihre-supabase-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=ihr-supabase-anon-key

# Weitere Umgebungsvariablen je nach Bedarf
```

## Deployment-Optionen

### Option 1: Lokales Deployment mit PM2

PM2 ist ein Prozessmanager für Node.js-<PERSON><PERSON><PERSON><PERSON><PERSON>, der Ihre Anwendung im Hintergrund laufen lässt und automatisch neustartet, wenn sie abstürzt.

1. Führen Sie das Deployment-Skript aus:

```bash
npm run deploy
```

Dieses Skript:
- Installiert alle Abhängigkeiten
- Baut die Anwendung
- Startet die Anwendung mit PM2

2. Überprüfen Sie den Status der Anwendung:

```bash
npm run pm2:logs
```

3. Um die Anwendung zu stoppen:

```bash
npm run pm2:stop
```

### Option 2: Deployment auf einem Server

1. Klonen Sie das Repository auf Ihrem Server:

```bash
git clone https://ihr-repository-url.git
cd next-opumap
```

2. Erstellen Sie eine `.env.local` Datei mit den notwendigen Umgebungsvariablen.

3. Führen Sie das Deployment-Skript aus:

```bash
npm run deploy:prod
```

4. Konfigurieren Sie einen Reverse-Proxy (Nginx oder Apache), um den Traffic zu Ihrer Anwendung weiterzuleiten.

### Option 3: Deployment mit Docker

Ein Dockerfile ist in der Anwendung enthalten. Um die Anwendung mit Docker zu deployen:

1. Bauen Sie das Docker-Image:

```bash
docker build -t opumap-next .
```

2. Starten Sie den Container:

```bash
docker run -p 3000:3000 --env-file .env.local opumap-next
```

## Nginx-Konfiguration (Beispiel)

Hier ist eine Beispiel-Nginx-Konfiguration für Ihre Anwendung:

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## SSL-Konfiguration

Für HTTPS empfehlen wir die Verwendung von Let's Encrypt:

```bash
sudo certbot --nginx -d yourdomain.com
```

## Monitoring

PM2 bietet ein Dashboard zur Überwachung Ihrer Anwendung:

```bash
pm2 monit
```

## Automatischer Neustart nach Serverneustart

Um sicherzustellen, dass Ihre Anwendung nach einem Serverneustart automatisch startet:

```bash
pm2 startup
pm2 save
```

## Fehlerbehebung

Wenn Probleme auftreten, überprüfen Sie die Logs:

```bash
npm run pm2:logs
```

Oder schauen Sie in die Logdateien im `logs`-Verzeichnis.
