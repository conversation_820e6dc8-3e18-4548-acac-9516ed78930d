import client from '@/lib/opulabAIClient';

/**
 * Runs an AI analysis for the personal goal within a strategic process,
 * stores prompt and result.
 * @param companyInfo - Pre-formulated company information string
 * @param personalGoal - User’s personal goal text
 */
export async function runOpulabPersoenlicheZielsetzung(
  companyInfo: string,
  personalGoal: string
): Promise<{ prompt: string; content: string }> {
  let prompt = `Gegebene Unternehmensinformationen:\n///\n${companyInfo}\n///\n\n`;
  prompt +=
    "Nutze die gegebenen Unternehmensinformationen und befolge vollständig den folgenden Aspekt und die bezogenen Aufgaben. " +
    "Dein Output soll keinen unnötigen Fluff, keine gesonderte Formatierung und auch keine unnötige Einleitung haben, " +
    "aber dennoch eine Erklärende Sichtweise einnehmen und die Ergebnisse als gegebene Anforderungen ausgeben:\n\n///\n";
  prompt +=
    "Bitte verarbeite die folgende individuelle Zielsetzung innerhalb eines strategischen Gesamtprozesses für ein Unternehmen. " +
    "Die Zielsetzung lautet:\n\n";
  prompt += `${personalGoal}\n\n`;
  prompt +=
    "Analysiere diese Eingabe und leite daraus die wichtigsten strategischen Ziele ab. " +
    "Binde diese in eine strategische Planung ein, indem du folgende Schritte ausführst:\n\n" +
    "1. Identifiziere die Kernaussage und Hauptziele der Eingabe.\n" +
    "2. Ordne die Ziele geeigneten strategischen Bereichen zu (z. B. Wachstum, Effizienz, Innovation, Marktpositionierung).\n" +
    "3. Entwickle konkrete Vorschläge, wie diese Ziele in einem übergeordneten strategischen Rahmen berücksichtigt und umgesetzt werden können.\n" +
    "4. Beschreibe die relevanten Maßnahmen, Zwischenschritte und Erfolgsindikatoren.\n" +
    "5. Zeige auf, wie die individuelle Zielsetzung mit der Gesamtstrategie des Unternehmens verbunden werden kann, sodass sie synergetisch wirkt.\n\n";
  prompt +=
    "Der Output soll eine strukturierte Darstellung der individuellen Zielsetzung und ihrer strategischen Einbindung sein. " +
    "Stelle sicher, dass der Text klar, umsetzbar und auf einen praktischen Unternehmenskontext bezogen ist.\n\n///";

  const completion = await client.chat.completions.create({
    model: "google/gemma-3-27b-it:free",
    messages: [
      { role: "system", content: "Bitte präzise Antwort liefern." },
      { role: "user", content: prompt }
    ]
  });

  const content = completion.choices?.[0]?.message?.content ?? "";
  return { prompt, content };
}
