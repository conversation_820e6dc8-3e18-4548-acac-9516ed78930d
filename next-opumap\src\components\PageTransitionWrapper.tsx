'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

interface PageTransitionWrapperProps {
  children: React.ReactNode;
  className?: string;
}

// Extend the Window interface to include our custom method
declare global {
  interface Window {
    morphTransitionTo?: (path: string) => void;
  }
}

const PageTransitionWrapper: React.FC<PageTransitionWrapperProps> = ({
  children,
  className
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [transitionType, setTransitionType] = useState<'in' | 'out'>('in');
  const [displayChildren, setDisplayChildren] = useState(children);
  const [prevPathname, setPrevPathname] = useState(pathname);

  // Handle page transitions when pathname changes
  useEffect(() => {
    if (pathname !== prevPathname) {
      // Start transition out
      setIsTransitioning(true);
      setTransitionType('out');
      
      // After transition out completes, update children and start transition in
      const timeout = setTimeout(() => {
        setDisplayChildren(children);
        setTransitionType('in');
        
        // After transition in completes, reset state
        const inTimeout = setTimeout(() => {
          setIsTransitioning(false);
          setPrevPathname(pathname);
        }, 300); // Match duration-300
        
        return () => clearTimeout(inTimeout);
      }, 300); // Match duration-300
      
      return () => clearTimeout(timeout);
    }
    
    return () => {}; // Return empty function for when pathname === prevPathname
  }, [children, pathname, prevPathname]);

  // Define transition classes based on transition type
  const getTransitionClasses = () => {
    const baseClasses = "transition-all duration-300 ease-in-out";
    
    if (!isTransitioning) {
      return `${baseClasses} opacity-100 scale-100 translate-x-0 translate-y-0`;
    }
    
    if (transitionType === 'out') {
      // Transition out effect - morph out with slight movement and scaling
      return `${baseClasses} opacity-0 scale-95 -translate-y-2`;
    } else {
      // Transition in effect - morph in from slightly below
      return `${baseClasses} opacity-0 scale-95 translate-y-2`;
    }
  };

  // Expose a method to trigger transition to a specific path
  const transitionTo = React.useCallback((path: string) => {
    if (path === pathname) return; // Don't transition to the same page
    
    console.log(`Transitioning to ${path}`);
    setIsTransitioning(true);
    setTransitionType('out');
    
    // Wait for animation to complete before navigating
    setTimeout(() => {
      router.push(path);
    }, 300); // Match duration-300
  }, [pathname, setIsTransitioning, setTransitionType, router]);

  // Make the transition method available globally
  useEffect(() => {
    window.morphTransitionTo = transitionTo;
    
    return () => {
      window.morphTransitionTo = undefined;
    };
  }, [transitionTo, pathname, router]);

  return (
    <div
      className={cn(
        getTransitionClasses(),
        className
      )}
    >
      {displayChildren}
    </div>
  );
};

export default PageTransitionWrapper;
