import { NextRequest, NextResponse } from 'next/server';
import pkg from 'pg'; // Import pg
const { Client } = pkg; // Destructure Client

// Define structure expected from the request body
interface RequestPayload {
    businessData: {
        name: string;
        address: string;
        phone: string;
        website: string;
        placeId: string;
        lat: number;
        lng: number;
    };
    analysisContent: string;
    analysisDate: string; // ISO string date
}

// Helper function to get pg client configuration (copied from lib/db.ts for consistency)
function getPgClientConfig() {
    const connectionString = process.env.POSTGRES_URL;
    if (!connectionString) {
        throw new Error('POSTGRES_URL environment variable not found.');
    }
    return {
        connectionString,
        ssl: {
            rejectUnauthorized: false, // Allow self-signed certificates (use with caution)
        },
    };
}


// POST /api/schnelle-analyse - Save or update quick analysis
export async function POST(request: NextRequest) {
    const client = new Client(getPgClientConfig());
    try {
        await client.connect();

        const payload: RequestPayload = await request.json();
        const { businessData, analysisContent, analysisDate } = payload;
        const { placeId, name, address, phone, website, lat, lng } = businessData;

        // Validate required fields
        if (!placeId || !analysisContent || !analysisDate) {
            return NextResponse.json({ error: 'Place ID, Analyseinhalt und Analysedatum sind erforderlich.' }, { status: 400 });
        }

        // Use pg client query with parameterized query
        const query = `
            INSERT INTO schnelle_analyse (
                place_id, name, address, phone, website, lat, lng, analysis_content, analysis_date, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW()
            )
            ON CONFLICT (place_id) DO UPDATE SET
                name = EXCLUDED.name,
                address = EXCLUDED.address,
                phone = EXCLUDED.phone,
                website = EXCLUDED.website,
                lat = EXCLUDED.lat,
                lng = EXCLUDED.lng,
                analysis_content = EXCLUDED.analysis_content,
                analysis_date = EXCLUDED.analysis_date,
                updated_at = NOW()
            RETURNING *;
        `;
        const values = [placeId, name, address, phone, website, lat, lng, analysisContent, analysisDate];
        const result = await client.query(query, values);

        return NextResponse.json({
            message: 'Analyse erfolgreich gespeichert.',
            data: result.rows[0] // Return the saved/updated data
        }, { status: 201 }); // 201 Created or 200 OK if updated

    } catch (error: unknown) {
        console.error('Fehler beim Speichern der Schnellanalyse:', error);
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
         if (error instanceof SyntaxError) { // Handle JSON parsing errors
            return NextResponse.json({ error: 'Ungültige Anfrage-Daten.' }, { status: 400 });
        }
        return NextResponse.json({
            error: 'Serverfehler beim Speichern der Analyse',
            details: message
        }, { status: 500 });
    } finally {
         if (client) { // Ensure client exists before trying to end
            await client.end();
         }
    }
}
