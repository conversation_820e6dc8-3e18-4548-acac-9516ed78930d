import React from 'react';
import DeleteButton from './DeleteButton';
import { SelectedCompany } from '../types';

interface SelectedCompaniesListProps {
  companies: SelectedCompany[];
  isLoading: boolean;
  onRemoveCompany: (company: SelectedCompany) => void;
}

const SelectedCompaniesList: React.FC<SelectedCompaniesListProps> = ({ companies, isLoading, onRemoveCompany }) => {
  return (
    <div className="selected-companies w-full">
      {/* Scrollbarer Container nur für die Liste der Unternehmen */}
      <div className="selected-companies-list max-h-[24rem] overflow-y-auto border rounded p-2">
        {isLoading ? (
          <p className="p-2">Lade...</p>
        ) : companies.length === 0 ? (
          <p className="p-2">Keine Unternehmen ausgewählt.</p>
        ) : (
          companies.map((company) => (
            <div key={company.id} className="company-item flex flex-col p-2 border-b last:border-0">
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold">{company.name}</span>
                <DeleteButton onClick={() => onRemoveCompany(company)} />
              </div>
              {company.address && (
                <span className="text-sm text-gray-500 mt-1">{company.address}</span>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default SelectedCompaniesList;
