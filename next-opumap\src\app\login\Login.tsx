'use client'; // Mark as a client component

import React, { useState, useContext, FormEvent, useEffect } from 'react';
import { AuthContext } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import styles from '../styles/transitions.module.css'; // Import CSS module
import GoogleSignInButton from './components/GoogleSignInButton';

const Login: React.FC = () => {
  const authContext = useContext(AuthContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isRegister, setIsRegister] = useState<boolean>(false);
  const [name, setName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);

  // Check for message parameter in URL (e.g., from password reset)
  useEffect(() => {
    const messageParam = searchParams.get('message');
    if (messageParam === 'password-updated') {
      setMessage('Ihr Passwort wurde erfolgreich aktualisiert. Sie können sich jetzt mit Ihrem neuen Passwort anmelden.');

      // Clear any existing errors
      setError(null);

      // Focus the email field after a short delay to improve UX
      setTimeout(() => {
        const emailInput = document.getElementById('email');
        if (emailInput) {
          emailInput.focus();
        }
      }, 500);
    }
  }, [searchParams]);

  if (!authContext) {
    console.error("AuthContext not found. Ensure Login component is wrapped in AuthProvider.");
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">Laden...</p>
        </div>
      </div>
    );
  }

  const { signIn, signUp, signInWithGoogle, isLoading: isAuthLoading } = authContext; // Add isLoading

  // Show loading indicator if auth is loading
  if (isAuthLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">Authentifizierung wird geladen...</p>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setMessage(null);

    try {
      if (isRegister) {
        const { error } = await signUp(email, password);
        if (error) {
          setError(error.message || "Registrierung fehlgeschlagen");
        } else {
          setMessage("Registrierung erfolgreich. Bitte bestätigen Sie Ihre E-Mail-Adresse und melden Sie sich dann an.");
          setIsRegister(false);
          setName("");
          setEmail("");
          setPassword("");
        }
      } else {
        const { error } = await signIn(email, password);
        if (error) {
          setError(error.message || "Login fehlgeschlagen");
        } else {
          router.push('/');
        }
      }
    } catch (err) {
      console.error(`${isRegister ? 'Registration' : 'Login'} error:`, err);
      setError("Netzwerkfehler oder Server nicht erreichbar.");
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null);
    setMessage(null);
    if (isAuthLoading) { // Prevent action if auth is still loading
        setError("Authentifizierung wird noch geladen. Bitte warten Sie einen Moment.");
        return;
    }
    if (signInWithGoogle) {
      const { error } = await signInWithGoogle();
      if (error) {
        setError(error.message || "Google Login fehlgeschlagen");
      } else {
        // Redirect will be handled by Supabase and the callback route
        // router.push('/'); // No need to push here
      }
    } else {
      setError("Google Login Funktion nicht verfügbar.");
    }
  };

  // Function to handle back to landing page with animation
  const handleBackToLanding = () => {
    if (isAnimating) return; // Prevent multiple clicks during animation

    console.log("Transitioning back to landing page");
    setIsAnimating(true);

    // Add class to prevent scrollbars during transition
    document.body.classList.add('page-transition-active');

    // Navigate after a very short delay
    setTimeout(() => {
      router.push('/');
      // Remove class after navigation
      setTimeout(() => {
        document.body.classList.remove('page-transition-active');
      }, 50);
    }, 100); // Short delay for smoother transition
  };

  return (
      <div className={`flex flex-col items-center bg-[var(--color-background)] ${styles.pageTransition} ${isAnimating ? styles.fadeOut : styles.fadeIn}`}>
        <div className="flex items-center justify-center w-full -mt-px border-t border-[var(--color-border)] rounded-t-none">
          <div className="bg-[var(--color-card)] p-10 rounded shadow-lg text-center max-w-md w-full">
            <h1 className="text-2xl font-bold mb-4 text-[var(--color-card-foreground)]">
              {isRegister ? "Registrierung" : "Login"}
            </h1>
            {/* Add a check for isAuthLoading before rendering the form or disable elements */}
            {isAuthLoading && <p>Authentifizierung wird geladen...</p>}
            {!isAuthLoading && (
              <>
                {message && (
                  <div className="bg-[var(--color-secondary)] border border-[var(--color-secondary)] text-[var(--color-secondary-foreground)] px-4 py-3 rounded relative mb-3" role="alert">
                    {message}
                  </div>
                )}
                {error && (
                  <div className="bg-[var(--color-destructive)] border border-[var(--color-destructive)] text-[var(--color-destructive-foreground)] px-4 py-3 rounded relative mb-3" role="alert">
                    {error}
                  </div>
                )}
                <form onSubmit={handleSubmit}>
                  {isRegister && (
                    <div className="mb-4">
                      <label htmlFor="name" className="block text-[var(--color-card-foreground)] text-sm font-bold mb-2">Name</label>
                      <input
                        type="text"
                        id="name"
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-[var(--color-foreground)] bg-[var(--color-input)] border-[var(--color-border)] leading-tight focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        required
                      />
                    </div>
                  )}
                  <div className="mb-4">
                    <label htmlFor="email" className="block text-[var(--color-card-foreground)] text-sm font-bold mb-2">Email</label>
                    <input
                      type="email"
                      id="email"
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-[var(--color-foreground)] bg-[var(--color-input)] border-[var(--color-border)] leading-tight focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>
                  <div className="mb-6">
                    <label htmlFor="password" className="block text-[var(--color-card-foreground)] text-sm font-bold mb-2">Passwort</label>
                    <input
                      type="password"
                      id="password"
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-[var(--color-foreground)] bg-[var(--color-input)] border-[var(--color-border)] mb-1 leading-tight focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <div className="text-right">
                      <Link
                        href="/forgot-password"
                        className="text-xs text-[var(--color-primary)] hover:underline"
                      >
                        Passwort vergessen?
                      </Link>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <button
                      type="submit"
                      className="bg-[var(--color-primary)] hover:brightness-90 text-[var(--color-primary-foreground)] font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] w-full"
                      disabled={isAuthLoading} // Disable button if auth is loading
                    >
                      {isRegister ? "Registrieren" : "Login"}
                    </button>
                  </div>
                </form>

                <div className="mt-6 text-center flex flex-col space-y-4">
                  <p className="text-[var(--color-muted-foreground)] text-sm">Oder</p>
                  <GoogleSignInButton onClick={handleGoogleSignIn} disabled={isAuthLoading} />
                  <div className="flex flex-col space-y-3 w-full">
                    <button
                      className="inline-block align-baseline font-bold text-sm text-[var(--color-primary)] hover:text-[var(--color-primary-foreground)]"
                      onClick={() => {
                        if (isAuthLoading) return; // Prevent action if auth is still loading
                        setError(null);
                        setMessage(null);
                        setIsRegister(!isRegister);
                      }}
                      disabled={isAuthLoading} // Disable button if auth is loading
                    >
                      {isRegister ? "Bereits registriert? Zum Login wechseln" : "Noch keinen Account? Jetzt registrieren"}
                    </button>

                    <button
                      className="inline-block align-baseline font-bold text-sm text-[var(--color-muted-foreground)] hover:text-[var(--color-foreground)] transition-colors"
                      onClick={handleBackToLanding}
                      disabled={isAuthLoading} // Disable button if auth is loading
                    >
                      Zurück zur Startseite
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
  );
}

export default Login;
