import { BusinessData } from "@/types";

/**
 * Format date string for display in German locale
 */
export const formatAnalysisDate = (dateString: string | null): string => {
  if (!dateString) return "Unbekannt";
  try {
    // Use German locale for formatting
    return new Date(dateString).toLocaleDateString("de-DE", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    // Handle potential invalid date strings gracefully
    return "Ungültiges Datum";
  }
};

/**
 * Helper to get photo URL, providing a fallback
 */
export const getPhotoUrl = (business: BusinessData): string | undefined => {
  if ("photos" in business && business.photos && business.photos.length > 0) {
    // Request a reasonably sized image
    return business.photos[0].getUrl?.({ maxWidth: 300, maxHeight: 300 });
  }
  // Fallback placeholder image
  return "https://via.placeholder.com/150";
};

/**
 * Format opening hours into German weekday strings
 */
export const formatOpeningHours = (
  periods: google.maps.places.OpeningHoursPeriod[] = []
): string[] => {
  const weekdays = [
    "Sonntag",
    "Montag",
    "Dienstag",
    "Mittwoch",
    "Donnerstag",
    "Freitag",
    "Samstag",
  ];
  const grouped: Record<number, { open: string; close: string }[]> = {};
  weekdays.forEach((_, day) => {
    grouped[day] = [];
  });
  const formatPoint = (p: google.maps.places.OpeningHoursPoint | null): string =>
    p
      ? `${p.hour.toString().padStart(2, "0")}:${p.minute
          .toString()
          .padStart(2, "0")}`
      : "00:00";

  periods.forEach((period) => {
    const { open, close } = period;
    const openStr = formatPoint(open);
    const closeStr = formatPoint(close || null);
    grouped[open.day].push({ open: openStr, close: closeStr });
  });
  const result: string[] = [];
  weekdays.forEach((weekday, day) => {
    const dayPeriods = grouped[day];
    if (dayPeriods.length === 0) {
      result.push(`${weekday}: geschlossen`);
    } else {
      dayPeriods.sort((a, b) => a.open.localeCompare(b.open));
      const ranges = dayPeriods.map((p) => `${p.open}–${p.close}`);
      result.push(`${weekday}: ${ranges.join(", ")}`);
    }
  });
  return result;
};
