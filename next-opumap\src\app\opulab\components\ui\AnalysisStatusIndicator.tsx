'use client';

import React from 'react';
import { CheckCircle2, Loader2, <PERSON><PERSON>ircle, HelpCircle } from 'lucide-react'; // Added HelpCircle for pending
import type { AnalysisStepsStatus, AnalysisStepStatus } from '../../hooks/useOpulabStrategie'; // Adjust path as needed

interface AnalysisStatusIndicatorProps {
  status: AnalysisStepsStatus;
}

const stepLabels: Record<keyof AnalysisStepsStatus, string> = {
  goalSetting: 'Pers. Zielsetzung',
  aspects: 'Relevante Aspekte',
  prioritization: 'Priorisierung',
  briefing: 'Strategie-Briefing',
  summary: 'Gesamtanalyse',
};

const StatusIcon: React.FC<{ status: AnalysisStepStatus }> = ({ status }) => {
  switch (status) {
    case 'loading':
      return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
    case 'completed':
      return <CheckCircle2 className="h-3 w-3 text-green-500" />;
    case 'error':
      return <XCircle className="h-3 w-3 text-red-500" />;
    case 'pending':
    default:
       return <HelpCircle className="h-3 w-3 text-gray-400" />; // Icon for pending state
  }
};

const AnalysisStatusIndicator: React.FC<AnalysisStatusIndicatorProps> = ({ status }) => {
  // Ensure the order is as requested
  const stepOrder: (keyof AnalysisStepsStatus)[] = [
    'aspects',
    'prioritization',
    'goalSetting',
    'briefing',
    'summary',
  ];

  return (
    <div className="mt-2 flex flex-wrap justify-center items-center space-x-3 text-xs text-gray-500">
      {stepOrder.map((stepKey) => (
        <div key={stepKey} className="flex items-center space-x-1">
          <StatusIcon status={status[stepKey]} />
          <span>{stepLabels[stepKey]}</span>
        </div>
      ))}
    </div>
  );
};

export default AnalysisStatusIndicator; 