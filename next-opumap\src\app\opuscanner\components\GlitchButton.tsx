import React from 'react';
import styled from 'styled-components';

interface GlitchButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
}

const GlitchButton: React.FC<GlitchButtonProps> = ({ children, onClick }) => {
  // Extract text content for the after pseudo-element
  let buttonText = '';
  if (typeof children === 'string') {
    buttonText = children;
  } else if (Array.isArray(children) && typeof children[0] === 'string') {
    buttonText = children[0];
  }

  // Reference to the button element
  const buttonRef = React.useRef<HTMLButtonElement>(null);

  // Handle click events
  const handleClick = () => {
    if (onClick) onClick();

    // For mobile: explicitly remove hover state after a short delay
    if (buttonRef.current) {
      // Add a class to indicate the button was clicked
      buttonRef.current.classList.add('clicked');

      // Remove the class after animation completes
      setTimeout(() => {
        if (buttonRef.current) {
          buttonRef.current.classList.remove('clicked');
        }
      }, 1000); // Match animation duration
    }
  };

  return (
    <StyledWrapper>
      <button
        ref={buttonRef}
        onClick={handleClick}
        data-content={buttonText}
      >
        {children}
      </button>
    </StyledWrapper>
  );
}

const StyledWrapper = styled.div`
  button,button::after {
    padding: 10px 50px;
    font-size: 20px;
    border-radius: 5px;
    position: relative;
    transition: all 0.3s ease;
  }

  /* Dark mode styles (default) */
  button {
    color: white;
    background-color: transparent;
    border: 1px solid rgba(0, 255, 213, 0.3);
    box-shadow: 0 0 5px rgba(0, 255, 213, 0.3);
    text-shadow: 0 0 2px rgba(29, 242, 240, 0.5);
  }

  /* Light mode styles */
  :root:not(.dark) & button {
    color: white;
    background-color: hsl(var(--primary));
    border: 1px solid hsl(var(--primary));
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2), 0 4px 8px -4px hsl(var(--primary));
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
    animation: pulse_light 2s infinite;
  }

  @keyframes pulse_light {
    0% {
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.2), 0 4px 8px -4px hsl(var(--primary));
    }
    50% {
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.3), 0 4px 12px -4px hsl(var(--primary)), 0 0 20px hsl(var(--primary) / 0.3);
    }
    100% {
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.2), 0 4px 8px -4px hsl(var(--primary));
    }
  }

  /* Ensure dark mode styles are applied correctly */
  .dark & button {
    color: white;
    background-color: transparent;
    border: 1px solid rgba(0, 255, 213, 0.3);
    box-shadow: 0 0 5px rgba(0, 255, 213, 0.3);
    text-shadow: 0 0 2px rgba(29, 242, 240, 0.5);
    animation: none;
  }

  button::after {
    --move1: inset(50% 50% 50% 50%);
    --move2: inset(31% 0 40% 0);
    --move3: inset(39% 0 15% 0);
    --move4: inset(45% 0 40% 0);
    --move5: inset(45% 0 6% 0);
    --move6: inset(14% 0 61% 0);
    clip-path: var(--move1);
    content: attr(data-content);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
  }

  button::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, rgba(0,255,213,0), rgba(0,255,213,0.8), rgba(0,255,213,0));
    border-radius: 0 0 5px 5px;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  :root:not(.dark) & button::before {
    background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.8), rgba(255,255,255,0));
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    opacity: 0.9;
  }

  button:hover::before {
    opacity: 1;
    height: 6px;
    box-shadow: 0 0 10px rgba(0, 255, 213, 0.8);
  }

  button:hover::after,
  button:active::after,
  button.clicked::after {
    animation: glitch_4011 1s;
    text-shadow: 10 10px 10px black;
    animation-timing-function: steps(2, end);
    text-shadow: -3px -3px 0px #1df2f0, 3px 3px 0px #E94BE8;
    background-color: transparent;
    border: 3px solid rgb(0, 255, 213);
  }

  /* Reset animation after it completes */
  button::after {
    animation-fill-mode: forwards;
  }

  button:hover,
  button:active,
  button.clicked {
    text-shadow: -1px -1px 0px #1df2f0, 1px 1px 0px #E94BE8;
    border: 1px solid rgb(0, 255, 213);
    box-shadow: 0px 10px 10px -10px rgb(0, 255, 213), 0 0 15px rgba(0, 255, 213, 0.5);
    transform: translateY(-2px);
  }

  /* Reset hover state on touch devices */
  @media (hover: none) {
    button:hover {
      text-shadow: none;
      border: 1px solid rgba(0, 255, 213, 0.3);
      box-shadow: 0 0 5px rgba(0, 255, 213, 0.3);
      transform: none;
    }

    button:active {
      text-shadow: -1px -1px 0px #1df2f0, 1px 1px 0px #E94BE8;
      border: 1px solid rgb(0, 255, 213);
      box-shadow: 0px 10px 10px -10px rgb(0, 255, 213), 0 0 15px rgba(0, 255, 213, 0.5);
      transform: translateY(-2px);
    }

    button.clicked {
      text-shadow: -1px -1px 0px #1df2f0, 1px 1px 0px #E94BE8;
      border: 1px solid rgb(0, 255, 213);
      box-shadow: 0px 10px 10px -10px rgb(0, 255, 213), 0 0 15px rgba(0, 255, 213, 0.5);
      transform: translateY(-2px);
      animation: button_reset 1s forwards;
    }

    @keyframes button_reset {
      0% {
        text-shadow: -1px -1px 0px #1df2f0, 1px 1px 0px #E94BE8;
        border: 1px solid rgb(0, 255, 213);
        box-shadow: 0px 10px 10px -10px rgb(0, 255, 213), 0 0 15px rgba(0, 255, 213, 0.5);
        transform: translateY(-2px);
      }
      99% {
        text-shadow: -1px -1px 0px #1df2f0, 1px 1px 0px #E94BE8;
        border: 1px solid rgb(0, 255, 213);
        box-shadow: 0px 10px 10px -10px rgb(0, 255, 213), 0 0 15px rgba(0, 255, 213, 0.5);
        transform: translateY(-2px);
      }
      100% {
        text-shadow: none;
        border: 1px solid rgba(0, 255, 213, 0.3);
        box-shadow: 0 0 5px rgba(0, 255, 213, 0.3);
        transform: none;
      }
    }
  }

  /* Light mode hover styles */
  :root:not(.dark) & button:hover,
  :root:not(.dark) & button:active,
  :root:not(.dark) & button.clicked {
    color: white;
    background-color: hsl(var(--primary) / 0.9);
    border: 1px solid hsl(var(--primary) / 0.8);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2), 0 4px 15px -4px hsl(var(--primary)), 0 0 20px hsl(var(--primary) / 0.5);
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    transform: translateY(-2px);
    animation: none; /* Stop the pulsing animation on hover */
  }

  /* Reset light mode hover state on touch devices */
  @media (hover: none) {
    :root:not(.dark) & button:hover {
      color: white;
      background-color: hsl(var(--primary));
      border: 1px solid hsl(var(--primary));
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.2), 0 4px 8px -4px hsl(var(--primary));
      text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
      transform: none;
      animation: pulse_light 2s infinite;
    }

    :root:not(.dark) & button:active {
      color: white;
      background-color: hsl(var(--primary) / 0.9);
      border: 1px solid hsl(var(--primary) / 0.8);
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.2), 0 4px 15px -4px hsl(var(--primary)), 0 0 20px hsl(var(--primary) / 0.5);
      text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
      transform: translateY(-2px);
      animation: none;
    }

    :root:not(.dark) & button.clicked {
      color: white;
      background-color: hsl(var(--primary) / 0.9);
      border: 1px solid hsl(var(--primary) / 0.8);
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.2), 0 4px 15px -4px hsl(var(--primary)), 0 0 20px hsl(var(--primary) / 0.5);
      text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
      transform: translateY(-2px);
      animation: light_button_reset 1s forwards;
    }

    @keyframes light_button_reset {
      0% {
        color: white;
        background-color: hsl(var(--primary) / 0.9);
        border: 1px solid hsl(var(--primary) / 0.8);
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2), 0 4px 15px -4px hsl(var(--primary)), 0 0 20px hsl(var(--primary) / 0.5);
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
        transform: translateY(-2px);
      }
      99% {
        color: white;
        background-color: hsl(var(--primary) / 0.9);
        border: 1px solid hsl(var(--primary) / 0.8);
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2), 0 4px 15px -4px hsl(var(--primary)), 0 0 20px hsl(var(--primary) / 0.5);
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
        transform: translateY(-2px);
      }
      100% {
        color: white;
        background-color: hsl(var(--primary));
        border: 1px solid hsl(var(--primary));
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.2), 0 4px 8px -4px hsl(var(--primary));
        text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
        transform: none;
        animation: pulse_light 2s infinite;
      }
    }
  }

  :root:not(.dark) & button:hover::before,
  :root:not(.dark) & button:active::before,
  :root:not(.dark) & button.clicked::before {
    background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.8), rgba(255,255,255,0));
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    opacity: 0.8;
    height: 6px;
  }

  .dark & button:hover,
  .dark & button:active,
  .dark & button.clicked {
    text-shadow: -1px -1px 0px #1df2f0, 1px 1px 0px #E94BE8;
    border: 1px solid rgb(0, 255, 213);
    box-shadow: 0px 10px 10px -10px rgb(0, 255, 213), 0 0 15px rgba(0, 255, 213, 0.5);
    transform: translateY(-2px);
  }

  @keyframes glitch_4011 {
    0% {
      clip-path: var(--move1);
      transform: translate(0px,-10px);
    }

    10% {
      clip-path: var(--move2);
      transform: translate(-10px,10px);
    }

    20% {
      clip-path: var(--move3);
      transform: translate(10px,0px);
    }

    30% {
      clip-path: var(--move4);
      transform: translate(-10px,10px);
    }

    40% {
      clip-path: var(--move5);
      transform: translate(10px,-10px);
    }

    50% {
      clip-path: var(--move6);
      transform: translate(-10px,10px);
    }

    60% {
      clip-path: var(--move1);
      transform: translate(10px,-10px);
    }

    70% {
      clip-path: var(--move3);
      transform: translate(-10px,10px);
    }

    80% {
      clip-path: var(--move2);
      transform: translate(10px,-10px);
    }

    90% {
      clip-path: var(--move4);
      transform: translate(-10px,10px);
    }

    100% {
      clip-path: var(--move1);
      transform: translate(0);
    }
  }
`;

export default GlitchButton;
