'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function AuthCodeErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case 'no_code':
        return 'Kein Authentifizierungscode gefunden. Der Link ist möglicherweise ungültig.';
      case 'unexpected_error':
        return 'Ein unerwarteter Fehler ist aufgetreten beim Authentifizierungsprozess.';
      default:
        return errorCode ? `Authentifizierungsfehler: ${errorCode}` : 'Ein Fehler ist beim Authentifizierungsprozess aufgetreten.';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[var(--color-background)]">
      <div className="w-full max-w-md p-8 space-y-6 bg-[var(--color-card)] rounded-lg shadow-lg border border-[var(--color-border)]">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-[var(--color-card-foreground)] mb-2">
            Authentifizierungsfehler
          </h1>
          <p className="text-sm text-[var(--color-muted-foreground)]">
            {getErrorMessage(error)}
          </p>
        </div>

        <div className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/forgot-password">
              Neuen Passwort-Reset anfordern
            </Link>
          </Button>
          
          <Button asChild variant="outline" className="w-full">
            <Link href="/login">
              Zurück zur Anmeldung
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
} 