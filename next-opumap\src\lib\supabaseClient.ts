'use client';

import { createClient } from '@/utils/supabase/client';

/**
 * Custom fetch wrapper that adds Supabase Auth token to requests
 * @param url The URL to fetch
 * @param options Fetch options
 * @returns The fetch response
 */
export async function fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
  try {
    // Einfach die Anfrage ohne zusätzliche Header senden, da die Cookies automatisch mitgesendet werden
    const response = await fetch(url, {
      ...options,
      credentials: 'include', // Wichtig: Cookies immer mitsenden
    });

    // Handle 401 Unauthorized errors
    if (response.status === 401) {
      console.warn('Received 401 Unauthorized response from API');

      // Überprüfen, ob wir noch eine gültige Session haben
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.log('No active session found, redirecting to login');
        // Nur umleiten, wenn wir im Browser sind
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }

    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}
