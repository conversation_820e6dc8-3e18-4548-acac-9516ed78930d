import { NextResponse } from 'next/server';

/**
 * Health-Check-Endpunkt für Monitoring und Docker-Healthchecks
 * Gibt 200 OK zurück, wenn die Anwendung läuft
 */
export async function GET() {
  try {
    // Hier könnten zusätzliche Checks eingebaut werden,
    // z.B. Datenbankverbindung prüfen
    
    return NextResponse.json(
      { 
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || 'unknown',
        environment: process.env.NODE_ENV
      }, 
      { status: 200 }
    );
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Health check failed',
        timestamp: new Date().toISOString()
      }, 
      { status: 500 }
    );
  }
}
