'use client';

import React from 'react';
import { CheckCircle2, Loader2, XCircle } from 'lucide-react';

// Definiere die möglichen Statuswerte expliziter
export type ScanProgressStatus = 'loading' | 'completed' | 'error' | 'idle';

interface ScannerStatusIndicatorProps {
  processedCount: number;
  totalCompanies: number;
  status: ScanProgressStatus;
}

const ScannerStatusIndicator: React.FC<ScannerStatusIndicatorProps> = ({
  processedCount,
  totalCompanies,
  status,
}) => {
  // Zeige nichts an, wenn der Status 'idle' ist oder keine Unternehmen vorhanden sind
  if (status === 'idle' || totalCompanies === 0) {
    return null;
  }

  let icon: React.ReactNode;
  let text: string;

  switch (status) {
    case 'loading':
      icon = <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
      text = `Unternehmen ${processedCount} von ${totalCompanies} analysiert...`;
      break;
    case 'completed':
      icon = <CheckCircle2 className="h-3 w-3 text-green-500" />;
      text = `Analyse für ${totalCompanies} Unternehmen abgeschlossen.`;
      break;
    case 'error':
      icon = <XCircle className="h-3 w-3 text-red-500" />;
      // Zeige den Fortschritt bis zum Fehler, falls verfügbar
      if (processedCount > 0 && processedCount <= totalCompanies) {
         text = `Fehler nach Analyse von ${processedCount} von ${totalCompanies} Unternehmen.`;
      } else {
         text = 'Fehler während der Analyse.';
      }
      break;
    default:
      // Sollte nicht erreicht werden wegen der initialen Prüfung, aber zur Sicherheit
      icon = null;
      text = '';
  }

  return (
    <div className="mt-2 flex justify-center items-center space-x-1 text-xs text-gray-500">
      {icon}
      <span>{text}</span>
    </div>
  );
};

export default ScannerStatusIndicator; 