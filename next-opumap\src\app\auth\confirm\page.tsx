'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

type EmailOtpType = 'signup' | 'invite' | 'magiclink' | 'recovery' | 'email_change';

// New component to handle logic depending on searchParams
function ConfirmLogic() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [message, setMessage] = useState('Bestätige E-Mail-Adresse...');
  const [error, setError] = useState<string | null>(null);

  // Function to validate the redirect URL
  const isValidRedirectUrl = (url: string): boolean => {
    // Check if the URL is relative (starts with '/')
    // and does not start with '//' or '/\\' which could be used for protocol-relative URLs or path traversal.
    if (url.startsWith('/') && !url.startsWith('//') && !url.startsWith('/\\\\')) {
      try {
        // Further check if it's a valid path without a hostname
        const { hostname } = new URL(url, window.location.origin);
        return !hostname; // Should be empty for relative paths
      } catch (_e) {
        // Invalid URL format
        return false;
      }
    }
    // Add more sophisticated checks if whitelisted external domains are needed
    // For example:
    // const allowedDomains = ['example.com', 'another.example.com'];
    // if (url.startsWith('http://') || url.startsWith('https://')) {
    //   try {
    //     const { hostname } = new URL(url);
    //     return allowedDomains.includes(hostname);
    //   } catch (e) {
    //     return false;
    //   }
    // }
    return false;
  };

  useEffect(() => {
    const confirmEmail = async () => {
      const token_hash = searchParams.get('token_hash');
      const type = searchParams.get('type') as EmailOtpType;
      let next = searchParams.get('next');

      // Validate the 'next' URL
      if (!next || !isValidRedirectUrl(next)) {
        next = '/login'; // Default to a safe internal path
      }
      
      if (token_hash && type) {
        try {
          const supabase = createClient();
          const { error } = await supabase.auth.verifyOtp({
            token_hash,
            type,
          });
          
          if (error) {
            setError(error.message);
          } else {
            // Set different messages based on type
            if (type === 'recovery') {
              setMessage('Passwort-Reset bestätigt! Sie werden zur Passwort-Eingabe weitergeleitet...');
            } else {
              setMessage('E-Mail-Adresse bestätigt! Sie werden weitergeleitet...');
            }
            
            setTimeout(() => {
              router.push(next);
            }, 2000);
          }
        } catch (err) {
          console.error('Error confirming email:', err);
          setError('Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        }
      } else {
        setError('Ungültiger Bestätigungslink.');
      }
    };
    
    confirmEmail();
  }, [searchParams, router]);

  return (
    <>
      {error ? (
        <div className="bg-[var(--color-destructive)] border border-[var(--color-destructive)] text-[var(--color-destructive-foreground)] px-4 py-3 rounded relative mb-3" role="alert">
          {error}
        </div>
      ) : (
        <div className="bg-[var(--color-primary)] border border-[var(--color-primary)] text-[var(--color-primary-foreground)] px-4 py-3 rounded relative mb-3" role="alert">
          {message}
        </div>
      )}
    </>
  );
}

export default function ConfirmPage() {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[var(--color-background)]">
      <div className="bg-[var(--color-card)] p-8 rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold mb-4 text-[var(--color-card-foreground)]">
          E-Mail-Bestätigung
        </h1>
        
        <Suspense fallback={<div className="bg-[var(--color-muted)] text-[var(--color-muted-foreground)] px-4 py-3 rounded relative mb-3">Lade...</div>}>
          <ConfirmLogic />
        </Suspense>
        
        <button
          onClick={() => router.push('/login')}
          className="mt-4 bg-[var(--color-primary)] hover:brightness-90 text-[var(--color-primary-foreground)] font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]"
        >
          Zurück zum Login
        </button>
      </div>
    </div>
  );
}
