import { UserProfile } from '@/types'; // Import shared User type
import { createClient } from '@/utils/supabase/server';

// Note: Using pg client directly. Assumes POSTGRES_URL is the DIRECT connection string.
// Relies on NODE_TLS_REJECT_UNAUTHORIZED=0 being set externally for local SSL bypass.

/**
 * Finds a user by their ID using Supabase.
 * @param userId - The user's UUID.
 * @returns The user profile, or null if not found.
 */
export async function findUserById(userId: string): Promise<UserProfile | null> {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            console.error('Error fetching user profile:', error);
            return null;
        }

        return data as UserProfile;
    } catch (error) {
        console.error('Unexpected error in findUserById:', error);
        return null;
    }
}

/**
 * Updates a user's profile data using Supabase.
 * @param userId - The UUID of the user to update.
 * @param profileData - An object containing the profile fields to update.
 * @returns The updated user profile.
 */
export async function updateUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile | null> {
    try {
        const supabase = await createClient();

        // Add updated_at timestamp
        const dataToUpdate = {
            ...profileData,
            updated_at: new Date().toISOString()
        };

        const { data, error } = await supabase
            .from('profiles')
            .update(dataToUpdate)
            .eq('id', userId)
            .select()
            .single();

        if (error) {
            console.error('Error updating user profile:', error);
            return null;
        }

        return data as UserProfile;
    } catch (error) {
        console.error('Unexpected error in updateUserProfile:', error);
        return null;
    }
}

/**
 * Finds a user by their email using Supabase.
 * @param email - The user's email address.
 * @returns The user profile, or null if not found.
 */
export async function findUserByEmail(email: string): Promise<UserProfile | null> {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('email', email)
            .single();

        if (error) {
            // Log the error, but don't expose sensitive details
            console.error('Error fetching user profile by email:', error.message);
            return null;
        }

        return data as UserProfile;
    } catch (error) { // Catch potential unexpected errors
        console.error('Unexpected error in findUserByEmail:', error);
        return null;
    }
}
