'use client';

import { useState, useCallback } from 'react';
import { StrategyService, Strategy } from '../strategyService';

/**
 * Custom hook for managing strategies
 */
export const useStrategies = () => {
  // State for strategies
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
  const [isNewStrategy, setIsNewStrategy] = useState<boolean>(true);
  const [isLoadingStrategies, setIsLoadingStrategies] = useState<boolean>(false);
  const [isFormEnabled, setIsFormEnabled] = useState<boolean>(false);

  // Handle selecting a strategy
  const handleSelectStrategy = useCallback((strategy: Strategy) => {
    setSelectedStrategy(strategy);
    setIsNewStrategy(false);
    setIsFormEnabled(true);
  }, []);

  // Load strategies from the database
  const loadStrategies = useCallback(async () => {
    setIsLoadingStrategies(true);
    try {
      const loadedStrategies = await StrategyService.getStrategies();
      setStrategies(loadedStrategies);

      // If we have strategies and none is selected, select the first one
      if (loadedStrategies.length > 0 && !selectedStrategy) {
        handleSelectStrategy(loadedStrategies[0]);
        setIsFormEnabled(true); // Aktiviere die Formularelemente, wenn eine Strategie geladen wurde
      } else if (loadedStrategies.length === 0) {
        // If no strategies exist, create a new one but don't enable form yet
        // Wir erstellen eine neue Strategie, aber aktivieren die Formularelemente noch nicht
        // Der Benutzer muss erst auf "Neue Strategie" klicken
        const newStrategy: Strategy = {
          strategy_name: strategies.length === 0 ? `Strategie 1` : `Strategie ${strategies.length + 1}`,
          // WICHTIG: Wir speichern die Unternehmensinformationen NICHT in der Strategie,
          // sondern verwenden einen leeren String, da die Unternehmensinformationen global im Benutzerprofil gespeichert werden
          company_info: '',
          personal_goal: '',
          short_term_tasks: '',
          long_term_goals: '',
          relevant_aspects: []
        };

        setSelectedStrategy(newStrategy);
        setIsNewStrategy(true);
        setIsFormEnabled(false); // Formularelemente deaktiviert lassen
      }
    } catch (error) {
      console.error('Error loading strategies:', error);
      // If there's an error, create a new strategy but don't enable form yet
      const newStrategy: Strategy = {
        strategy_name: strategies.length === 0 ? `Strategie 1` : `Strategie ${strategies.length + 1}`,
        // WICHTIG: Wir speichern die Unternehmensinformationen NICHT in der Strategie,
        // sondern verwenden einen leeren String, da die Unternehmensinformationen global im Benutzerprofil gespeichert werden
        company_info: '',
        personal_goal: '',
        short_term_tasks: '',
        long_term_goals: '',
        relevant_aspects: []
      };

      setSelectedStrategy(newStrategy);
      setIsNewStrategy(true);
      setIsFormEnabled(false); // Formularelemente deaktiviert lassen
    } finally {
      setIsLoadingStrategies(false);
    }
  }, [selectedStrategy, strategies.length, handleSelectStrategy]);

  // Create a new strategy
  const createStrategy = useCallback(async (
    onSuccess: (strategy: Strategy) => void,
    onError: (error: Error) => void
  ) => {
    try {
      // Überprüfen, ob bereits 5 Strategien vorhanden sind
      if (strategies.length >= 5) {
        throw new Error('Sie können maximal 5 Strategien erstellen. Bitte löschen Sie eine bestehende Strategie, um eine neue zu erstellen.');
      }

      // Create a new empty strategy
      const newStrategy: Strategy = {
        strategy_name: `Strategie ${strategies.length + 1}`,
        // WICHTIG: Wir speichern die Unternehmensinformationen NICHT in der Strategie,
        // sondern verwenden einen leeren String, da die Unternehmensinformationen global im Benutzerprofil gespeichert werden
        company_info: '',
        personal_goal: '',
        short_term_tasks: '',
        long_term_goals: '',
        relevant_aspects: []
      };

      // Speichere die neue Strategie in der Datenbank
      const savedStrategy = await StrategyService.createStrategy(newStrategy);

      // Aktualisiere die Strategieliste
      await loadStrategies();

      // Wähle die neue Strategie aus
      setSelectedStrategy(savedStrategy);
      setIsNewStrategy(false); // Da die Strategie jetzt in der Datenbank ist, ist sie nicht mehr "neu"
      setIsFormEnabled(true);

      onSuccess(savedStrategy);
    } catch (error) {
      if (error instanceof Error) {
        onError(error);
      } else {
        onError(new Error('Unbekannter Fehler beim Erstellen der Strategie.'));
      }
    }
  }, [strategies.length, loadStrategies]);

  // Update an existing strategy
  const updateStrategy = useCallback(async (
    strategyName: string,
    strategyData: Partial<Strategy>,
    onSuccess: (strategy: Strategy) => void,
    onError: (error: Error) => void
  ) => {
    try {
      // Wenn keine Strategie ausgewählt ist oder die Formulare deaktiviert sind, abbrechen
      if (!selectedStrategy || !isFormEnabled) {
        throw new Error('Keine Strategie zum Speichern ausgewählt oder Formular ist deaktiviert.');
      }

      // Wir erlauben nur das Aktualisieren einer bestehenden Strategie über den Speichern-Button
      if (!selectedStrategy.id) {
        throw new Error('Keine gültige Strategie zum Aktualisieren ausgewählt.');
      }

      const updatedStrategyData: Strategy = {
        ...selectedStrategy,
        ...strategyData,
        strategy_name: strategyName
      };

      const savedStrategy = await StrategyService.updateStrategy(selectedStrategy.id, updatedStrategyData);

      // Refresh the strategies list
      await loadStrategies();

      // Select the saved strategy
      if (savedStrategy) {
        setSelectedStrategy(savedStrategy);
        setIsNewStrategy(false);
      }

      onSuccess(savedStrategy);
    } catch (error) {
      if (error instanceof Error) {
        onError(error);
      } else {
        onError(new Error('Unbekannter Fehler beim Speichern der Strategie.'));
      }
    }
  }, [selectedStrategy, isFormEnabled, loadStrategies]);

  // Delete a strategy
  const deleteStrategy = useCallback(async (
    onSuccess: () => void,
    onError: (error: Error) => void
  ) => {
    if (!selectedStrategy?.id) {
      onError(new Error('Keine Strategie zum Löschen ausgewählt.'));
      return;
    }

    try {
      await StrategyService.deleteStrategy(selectedStrategy.id);

      // Refresh the strategies list
      await loadStrategies();

      // Setze die ausgewählte Strategie zurück
      setSelectedStrategy(null);
      setIsNewStrategy(true);
      setIsFormEnabled(false);

      onSuccess();
    } catch (error) {
      if (error instanceof Error) {
        onError(error);
      } else {
        onError(new Error('Unbekannter Fehler beim Löschen der Strategie.'));
      }
    }
  }, [selectedStrategy, loadStrategies]);

  return {
    strategies,
    selectedStrategy,
    isNewStrategy,
    isLoadingStrategies,
    isFormEnabled,
    loadStrategies,
    handleSelectStrategy,
    createStrategy,
    updateStrategy,
    deleteStrategy,
    setIsFormEnabled
  };
};
