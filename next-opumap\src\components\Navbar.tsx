'use client';
// Import useState and useEffect from React
import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
// Assuming useAuth is correctly imported from your context path
import { useAuth } from '@/contexts/AuthContext';
// Assuming DarkModeToggle is correctly imported
import DarkModeToggle from '@/components/ui/DarkModeToggle';
// Import necessary components from shadcn/ui - Added Header, Title, Description, Close
import {
  Sheet,
  SheetTrigger,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetClose, // Assuming this is now exported correctly after update
} from './ui/sheet';
import { Button } from './ui/button';
// Import useTheme hook
import { useTheme } from 'next-themes';
import { CheckCircle, Zap, Map, Microscope, User, Home, Menu, X, Sparkles } from 'lucide-react';
// Import AuthStatus component
import AuthStatus from '@/components/auth/AuthStatus';
// Import ClientOnly components
import { ClientOnlySVG, ClientOnlyIcon } from '@/components/ui/client-only';
import ClientOnlyImage from '@/components/ui/client-only/ClientOnlyImage';

// Particle system for the floating navbar
const ParticleSystem: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (!isActive) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      life: number;
      maxLife: number;
      size: number;
    }> = [];
    
    const createParticle = (x: number, y: number) => {
      particles.push({
        x,
        y,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        life: 60,
        maxLife: 60,
        size: Math.random() * 2 + 1,
      });
    };
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Update and draw particles
      for (let i = particles.length - 1; i >= 0; i--) {
        const p = particles[i];
        p.x += p.vx;
        p.y += p.vy;
        p.life--;
        
        if (p.life <= 0) {
          particles.splice(i, 1);
          continue;
        }
        
        const alpha = p.life / p.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha * 0.6;
        ctx.fillStyle = '#3b82f6';
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      }
      
      // Randomly create new particles
      if (Math.random() < 0.1 && particles.length < 20) {
        createParticle(
          Math.random() * canvas.width,
          Math.random() * canvas.height
        );
      }
      
      requestAnimationFrame(animate);
    };
    
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    animate();
    
    const handleResize = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isActive]);
  
  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none opacity-20"
      style={{ width: '100%', height: '100%' }}
    />
  );
};

// Morphing navigation link component
const MorphLink: React.FC<{
  href: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick?: () => void;
}> = ({ href, icon, label, isActive, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <Link
      href={href}
      onClick={onClick}
      className={`
        group relative flex items-center gap-3 px-4 py-3 rounded-2xl
        transition-all duration-500 ease-out transform-gpu
        ${isActive 
          ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-600 dark:text-blue-400 scale-105' 
          : 'hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 hover:scale-105'
        }
        ${isHovered ? 'shadow-lg shadow-blue-500/25' : ''}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated background blob */}
      <div className={`
        absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-purple-500/5
        transition-all duration-700 ease-out transform-gpu
        ${isHovered ? 'scale-110 opacity-100' : 'scale-95 opacity-0'}
      `} />
      
      {/* Icon container with morphing effect */}
      <div className={`
        relative z-10 flex items-center justify-center w-8 h-8 rounded-xl
        transition-all duration-500 ease-out transform-gpu
        ${isActive ? 'bg-blue-500/20 text-blue-600' : 'text-muted-foreground'}
        ${isHovered ? 'rotate-12 scale-110' : ''}
      `}>
        {icon}
        {isHovered && (
          <div className="absolute inset-0 bg-blue-500/10 rounded-xl animate-ping" />
        )}
      </div>
      
      {/* Label with sliding effect */}
      <span className={`
        relative z-10 font-medium transition-all duration-500 ease-out
        ${isActive ? 'text-blue-600 dark:text-blue-400' : 'text-foreground'}
        ${isHovered ? 'translate-x-1' : ''}
      `}>
        {label}
        {isHovered && (
          <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
        )}
      </span>
      
      {/* Sparkle effect on hover */}
      {isHovered && (
        <div className="absolute top-1 right-1 text-blue-500 animate-bounce">
          <Sparkles className="w-3 h-3" />
        </div>
      )}
    </Link>
  );
};

// Floating Command Center Navbar
const Navbar: React.FC = () => {
  const { user, isLoading, initialAuthDone } = useAuth();
  const isAuthenticated = !!user && !isLoading && initialAuthDone;
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const navRef = useRef<HTMLElement>(null);

  // Navigation items configuration
  const navItems = [
    { href: '/', icon: <Home className="w-4 h-4" />, label: 'Home' },
    { href: '/profile', icon: <User className="w-4 h-4" />, label: 'Profil' },
    { href: '/opuscanner', icon: <Map className="w-4 h-4" />, label: 'Opuscanner' },
    { href: '/opulab', icon: <Microscope className="w-4 h-4" />, label: 'Opulab' },
  ];

  // Scroll detection for morphing effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Mouse tracking for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (navRef.current) {
        const rect = navRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    const nav = navRef.current;
    if (nav) {
      nav.addEventListener('mousemove', handleMouseMove);
      return () => nav.removeEventListener('mousemove', handleMouseMove);
    }
    
    return () => {}; // Ensure all code paths return a cleanup function
  }, []);

  return (
    <>
      {/* Desktop Floating Command Center */}
      <header
        ref={navRef}
        className={`
          fixed top-4 left-1/2 transform -translate-x-1/2 z-50
          transition-all duration-700 ease-out transform-gpu
          ${isScrolled 
            ? 'scale-95 translate-y-0' 
            : 'scale-100 translate-y-0'
          }
          ${isHovered ? 'scale-105' : ''}
          hidden lg:block
        `}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Main floating container */}
        <div className={`
          relative overflow-hidden rounded-3xl border border-white/20 dark:border-gray-800/50
          quantum-backdrop bg-white/80 dark:bg-gray-900/80
          shadow-quantum ${isHovered ? 'shadow-quantum-lg quantum-glow' : ''}
          transition-all duration-700 ease-out transform-gpu morph-container
          ${isHovered ? 'neural-network' : ''}
        `}>
          {/* Particle system background */}
          <ParticleSystem isActive={isHovered} />
          
          {/* Interactive gradient overlay */}
          <div 
            className="absolute inset-0 opacity-30 pointer-events-none"
            style={{
              background: `radial-gradient(300px circle at ${mousePosition.x}px ${mousePosition.y}px, 
                rgba(59, 130, 246, 0.1) 0%, 
                rgba(147, 51, 234, 0.05) 50%, 
                transparent 100%)`
            }}
          />
          
          {/* Navigation content */}
          <nav className="relative z-10 flex items-center gap-2 p-3">
                         {/* Logo section */}
             <div className="flex items-center gap-3 px-3 py-2 rounded-2xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 hover-zone">
               <Logo className="h-8 w-auto transition-transform duration-300 hover:scale-110 glitch-effect" />
               {isAuthenticated && (
                 <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-green-500/20 border border-green-500/30 hologram-flicker">
                   <div className="w-2 h-2 bg-green-500 rounded-full quantum-pulse" />
                   <span className="text-xs font-medium text-green-600 dark:text-green-400 quantum-text">AKTIV</span>
                 </div>
               )}
             </div>
            
            {/* Separator with animated line */}
            <div className="w-px h-8 bg-gradient-to-b from-transparent via-border to-transparent mx-2" />
            
            {/* Navigation links */}
            <div className="flex items-center gap-3">
              {navItems.map((item) => (
                <MorphLink
                  key={item.href}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  isActive={pathname === item.href}
                />
              ))}
            </div>
            
            {/* Separator */}
            <div className="w-px h-8 bg-gradient-to-b from-transparent via-border to-transparent mx-2" />
            
            {/* Action section */}
            <div className="flex items-center gap-2">
              <AuthStatus />
              <div className="p-2 rounded-xl hover:bg-white/50 dark:hover:bg-gray-800/50 transition-colors duration-300">
                <DarkModeToggle />
              </div>
            </div>
          </nav>
          
          {/* Bottom glow effect */}
          <div className={`
            absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-3/4 h-1
            bg-gradient-to-r from-transparent via-blue-500/50 to-transparent
            transition-opacity duration-500
            ${isHovered ? 'opacity-100' : 'opacity-0'}
          `} />
        </div>
      </header>

      {/* Mobile Quantum Menu */}
      <header className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-white/20 dark:border-gray-800/50">
        <div className="flex items-center justify-between p-4">
          {/* Mobile logo */}
          <Link href="/" className="flex items-center gap-2">
            <Logo className="h-8 w-auto" />
            {isAuthenticated && (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            )}
      </Link>

          {/* Quantum menu trigger */}
        <Sheet>
          <SheetTrigger asChild>
                             <Button 
                 variant="outline" 
                 size="icon" 
                 className="relative overflow-hidden rounded-2xl border-2 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:scale-110 quantum-border hover-zone"
               >
                 <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 neural-network" />
                 <Menu className="h-5 w-5 relative z-10 glitch-effect" />
                 <span className="sr-only">Quantum Menu</span>
            </Button>
          </SheetTrigger>
            
            <SheetContent 
              side="left" 
              className="w-80 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-r border-white/20 dark:border-gray-800/50"
            >
              <SheetHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Logo className="h-10 w-auto" />
                    <div>
                      <SheetTitle className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Command Center
                      </SheetTitle>
                      <SheetDescription className="text-sm text-muted-foreground">
                        Quantum Navigation Interface
                      </SheetDescription>
                    </div>
                  </div>
              </div>
            </SheetHeader>

              {/* Mobile navigation */}
              <div className="space-y-2 pb-6">
                {navItems.map((item) => (
                  <SheetClose key={item.href} asChild>
                    <MorphLink
                      href={item.href}
                      icon={item.icon}
                      label={item.label}
                      isActive={pathname === item.href}
                    />
                 </SheetClose>
                ))}
                </div>

              {/* Mobile auth section */}
              <div className="border-t border-white/20 dark:border-gray-800/50 pt-6 space-y-4">
                <AuthStatus />
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Quantum Theme</span>
                  <DarkModeToggle />
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>

      {/* Spacer for mobile and desktop */}
      <div className="h-20 lg:h-24" />
    </>
  );
};

// Enhanced Logo Component with quantum effects
function Logo(props: React.ImgHTMLAttributes<HTMLImageElement>) {
  const [isMounted, setIsMounted] = useState(false);
  const { theme, resolvedTheme } = useTheme();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const currentTheme = isMounted ? resolvedTheme || theme : 'light';
  const src = currentTheme === 'dark'
    ? '/weiss-ohne-bg-svg.svg'
    : '/schwarz-ohne-bg-svg.svg';

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = 'https://placehold.co/100x40/cccccc/ffffff?text=OPUS';
    e.currentTarget.alt = 'OPUS Logo';
  };

  return (
    <div className="relative group">
      {/* Quantum glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
    <ClientOnlyImage
      src={src}
        alt="OPUS Command Center"
        width={200}
        height={80}
        className={`${props.className} object-contain relative z-10 transition-all duration-300 group-hover:brightness-110`}
      onError={handleError}
      priority
    />
    </div>
  );
}

export default Navbar;
