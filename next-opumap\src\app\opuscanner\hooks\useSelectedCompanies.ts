import { useState, useEffect, useCallback } from 'react';
import { OpuscannerService } from '../apiService';
import { SelectedCompany } from '../types';

interface UseSelectedCompaniesProps {
  onCompanyRemoved?: () => void; // Callback für die Aktualisierung der Scanergebnisse
}

interface UseSelectedCompaniesResult {
  companies: SelectedCompany[];
  isLoading: boolean;
  error: string | null;
  loadCompanies: () => Promise<void>;
  handleRemoveCompany: (company: SelectedCompany) => Promise<void>;
  setError: (message: string | null) => void;
}

export const useSelectedCompanies = ({ onCompanyRemoved }: UseSelectedCompaniesProps = {}): UseSelectedCompaniesResult => {
  const [companies, setCompanies] = useState<SelectedCompany[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Function to load companies
  const loadCompanies = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('Loading selected companies...');
      const loadedCompanies = await OpuscannerService.loadSelectedCompanies();
      console.log(`Loaded ${loadedCompanies.length} companies`);

      // Validate the loaded data
      const validCompanies = loadedCompanies.filter(company => {
        // Check for required fields
        if (!company.id || !company.place_id) {
          console.warn(`Skipping invalid company data:`, company);
          return false;
        }
        return true;
      });

      if (validCompanies.length < loadedCompanies.length) {
        console.warn(`Filtered out ${loadedCompanies.length - validCompanies.length} invalid companies`);
      }

      setCompanies(validCompanies);
    } catch (err) {
      console.error('Error in loadCompanies:', err);
      const errorMsg = err instanceof Error ? `Fehler beim Laden der Unternehmen: ${err.message}` : 'Unbekannter Fehler beim Laden der Unternehmen';
      setError(errorMsg);
      setCompanies([]); // Clear companies on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial load of companies with retry mechanism
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 2000; // 2 seconds

    const loadWithRetry = async () => {
      try {
        await loadCompanies();
      } catch (err) {
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`Retrying load companies (${retryCount}/${maxRetries}) after ${retryDelay}ms...`);
          setTimeout(loadWithRetry, retryDelay);
        } else {
          console.error(`Failed to load companies after ${maxRetries} retries`);
        }
      }
    };

    loadWithRetry();
  }, [loadCompanies]);

  // Handler for removing a company
  const handleRemoveCompany = useCallback(async (companyToRemove: SelectedCompany) => {
    setError(null); // Clear previous errors
    try {
      await OpuscannerService.removeCompany(companyToRemove.place_id);
      setCompanies(prev => prev.filter((c) => c.id !== companyToRemove.id));

      // Rufe den Callback auf, um die Scanergebnisse zu aktualisieren
      if (onCompanyRemoved) {
        onCompanyRemoved();
      }

      // Optionally return a success message or handle it via setError(null)
    } catch (err) {
      const errorMsg = err instanceof Error ? `Fehler beim Entfernen des Unternehmens: ${err.message}` : 'Unbekannter Fehler beim Entfernen';
      setError(errorMsg);
    }
  }, [onCompanyRemoved]);

  return {
    companies,
    isLoading,
    error,
    loadCompanies,
    handleRemoveCompany,
    setError,
  };
};