// <PERSON>ript to apply the user_id_mappings table migration
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

async function applyMigration() {
  // Create Supabase client
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase URL or service role key. Please check your .env.local file.');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('Applying user_id_mappings table migration...');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'migrations', 'create_user_id_mappings_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration SQL
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      console.error('Error applying migration:', error);
      process.exit(1);
    }

    console.log('Migration applied successfully!');

    // Create mappings for existing users
    console.log('Creating mappings for existing users...');
    
    // Get all users from Supabase Auth
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      process.exit(1);
    }

    // Create mappings for each user
    for (const user of users.users) {
      console.log(`Creating mapping for user ${user.email} (${user.id})...`);
      
      // Check if mapping already exists
      const { data: existingMapping } = await supabase
        .from('user_id_mappings')
        .select('numeric_id')
        .eq('uuid', user.id)
        .single();
      
      if (existingMapping) {
        console.log(`  Mapping already exists: ${existingMapping.numeric_id}`);
        continue;
      }
      
      // Insert new mapping
      const { data: newMapping, error: insertError } = await supabase
        .from('user_id_mappings')
        .insert({
          uuid: user.id,
          user_email: user.email || 'unknown',
        })
        .select('numeric_id')
        .single();
      
      if (insertError) {
        console.error(`  Error creating mapping for user ${user.email}:`, insertError);
        continue;
      }
      
      console.log(`  Created mapping: ${newMapping.numeric_id}`);
    }

    console.log('Migration process completed successfully!');
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

applyMigration().catch(console.error);
