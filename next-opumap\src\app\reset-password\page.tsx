'use client';

import { useState, useEffect, useMemo } from 'react'; // Added useMemo
import { useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import type { User, AuthError } from '@supabase/supabase-js'; // Added import

export default function ResetPasswordPage() {
  // Constants
  const MAX_SESSION_CHECK_ATTEMPTS = 4;
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [checkAttempts, setCheckAttempts] = useState(0);
  const [passwordStrength, setPasswordStrength] = useState(0); // 0-3 for password strength
  const [isProcessing, setIsProcessing] = useState(false); // Track if we're in the middle of processing
  const [isPasswordResetFlow, setIsPasswordResetFlow] = useState(false);
  const searchParams = useSearchParams();
  const supabase = useMemo(() => createClient(), []); // Wrapped createClient in useMemo

  // Calculate password strength when password changes
  useEffect(() => {
    if (!password) {
      setPasswordStrength(0);
      return;
    }

    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 1;

    // Complexity checks
    if (/[A-Z]/.test(password)) strength += 1; // Has uppercase
    if (/[0-9]/.test(password) || /[^A-Za-z0-9]/.test(password)) strength += 1; // Has number or special char

    setPasswordStrength(strength);

    // Clear error message if user is typing a new password
    if (error && (error.includes('Passwort') || error.includes('unterscheiden'))) {
      setError('');
    }
  }, [password, error]);

  // Clear error message when confirm password changes
  useEffect(() => {
    if (confirmPassword && error && error.includes('stimmen nicht überein')) {
      setError('');
    }
  }, [confirmPassword, error]);

  // Get URL parameters that might be passed from the auth callback
  const sessionValid = searchParams.get('session_valid');
  const sessionError = searchParams.get('session_error');
  const authEvent = searchParams.get('auth_event');
  // Handle auth state changes for password recovery
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, _session) => {
      console.log('Auth state changed:', event);
      
      if (event === 'PASSWORD_RECOVERY') {
        console.log('Password recovery flow detected');
        setIsPasswordResetFlow(true);
        setIsLoading(false);
      } else if (event === 'SIGNED_OUT') {
        // This is expected during password reset, don't show error
        if (!isPasswordResetFlow) {
          setError('Ihre Sitzung ist abgelaufen. Bitte fordern Sie einen neuen Passwort-Reset an.');
        }
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [isPasswordResetFlow, supabase.auth]);

  // Check session with improved handling for password reset flow
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Log URL parameters for debugging
        console.log('Reset password page URL params:', {
          sessionValid,
          sessionError,
          authEvent,
          allParams: Object.fromEntries(searchParams.entries())
        });

        // Skip session validation if we're in the middle of processing the password update
        if (isProcessing || isUpdating || isPasswordResetFlow) {
          console.log('Skipping session check during password update');
          setIsLoading(false);
          return;
        }

        // If we have explicit session validation from the auth callback
        if (sessionValid === 'true' && authEvent === 'recovery') {
          console.log('Reset password page: Valid session confirmed by auth callback');
          setIsLoading(false);
          return;
        }

        // If we have explicit session error from the auth callback
        if (sessionError && authEvent === 'recovery') {
          console.log('Reset password page: Session error from auth callback:', sessionError);
          setError('Ungültige oder abgelaufene Sitzung. Bitte fordern Sie einen neuen Passwort-Reset an.');
          setIsLoading(false);
          return;
        }

        console.log(`Reset password page: Checking session (attempt ${checkAttempts + 1}/${MAX_SESSION_CHECK_ATTEMPTS})...`);

        // Try to refresh the session first
        console.log('Reset password page: Attempting session refresh...');
        await supabase.auth.refreshSession();

        // Wait a moment for refresh to complete
        await new Promise(resolve => setTimeout(resolve, 500));

        // Now check for session
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();        if (error) {
          console.error('Error getting session:', error);

          // Retry a few times in case the session is still being set
          if (checkAttempts < MAX_SESSION_CHECK_ATTEMPTS - 1) {
            console.log('Retrying session check in 1 second...');
            setTimeout(() => {
              setCheckAttempts(prev => prev + 1);
            }, 1000);
            return;
          }

          setError('Fehler beim Abrufen der Sitzung. Bitte fordern Sie einen neuen Passwort-Reset an.');
          setIsLoading(false);
          return;
        }

        console.log('Reset password page: Session check result:', currentSession ? 'Session found' : 'No session');

        if (!currentSession) {
          // Try to get user directly as fallback
          console.log('Reset password page: No session, trying direct user check...');
          const { data: { user }, error: userError } = await supabase.auth.getUser();

          if (user && !userError) {
            console.log('Reset password page: User found via direct check:', user.id);
            setIsLoading(false);
            return;
          }          // Retry a few times in case the session is still being set
          if (checkAttempts < MAX_SESSION_CHECK_ATTEMPTS - 1) {
            console.log('No session found, retrying in 1 second...');
            setTimeout(() => {
              setCheckAttempts(prev => prev + 1);
            }, 1000);
            return;
          }

          console.log('Reset password page: No session found after retries, showing error');
          setError('Ungültige oder abgelaufene Sitzung. Bitte fordern Sie einen neuen Passwort-Reset an.');
          setIsLoading(false);
        } else {
          console.log('Reset password page: Valid session found, user ID:', currentSession.user.id);
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Unexpected error checking session:', err);

        // Retry a few times in case it's a temporary error
        if (checkAttempts < 3) {
          console.log('Error occurred, retrying in 1 second...');
          setTimeout(() => {
            setCheckAttempts(prev => prev + 1);
          }, 1000);
          return;
        }

        setError('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        setIsLoading(false);
      }
    };    checkSession();
  }, [checkAttempts, supabase.auth, sessionValid, sessionError, authEvent, searchParams, isPasswordResetFlow, isProcessing, isUpdating]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');
    setIsProcessing(true);
    setIsPasswordResetFlow(true);

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 6) {
      setError('Das Passwort muss mindestens 6 Zeichen lang sein.');
      return;
    }

    // Recommend stronger password but don't block submission
    if (passwordStrength < 2) {
      const confirmWeakPassword = window.confirm(
        'Ihr Passwort ist relativ schwach und könnte leicht erraten werden. ' +
        'Wir empfehlen ein stärkeres Passwort mit mindestens 8 Zeichen, ' +
        'einem Großbuchstaben und einer Zahl oder einem Sonderzeichen. ' +
        'Möchten Sie trotzdem fortfahren?'
      );

      if (!confirmWeakPassword) {
        return;
      }
    }

    setIsUpdating(true);
    try {
      console.log('Starting password update with enhanced session validation...');

      // Enhanced session validation with multiple attempts
      let sessionValidated = false;
      let attemptCount = 0;
      const maxAttempts = 3;

      while (!sessionValidated && attemptCount < maxAttempts) {
        attemptCount++;
        console.log(`Session validation attempt ${attemptCount}/${maxAttempts}...`);

        try {
          // Force session refresh
          await supabase.auth.refreshSession();
          await new Promise(resolve => setTimeout(resolve, 200));

          // Check for valid session
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.warn(`Session validation attempt ${attemptCount} error:`, sessionError);
            if (attemptCount < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
              continue;
            }
            throw new Error(`Session validation failed: ${sessionError.message}`);
          }

          if (!sessionData.session) {
            console.warn(`Session validation attempt ${attemptCount}: No session found`);
            if (attemptCount < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
              continue;
            }
            throw new Error('No valid session found after multiple attempts');
          }

          console.log(`Session validation attempt ${attemptCount} successful:`, sessionData.session.user.id);
          sessionValidated = true;

        } catch (validationError) {
          console.error(`Session validation attempt ${attemptCount} failed:`, validationError);
          if (attemptCount >= maxAttempts) {
            throw validationError;
          }
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (!sessionValidated) {
        throw new Error('Failed to validate session after multiple attempts');
      }

      console.log('Session validated successfully, proceeding with password update...');

      let updateAttempts = 0;
      const maxUpdateAttempts = 2;
      let updateSuccess = false;

      while (!updateSuccess && updateAttempts < maxUpdateAttempts) {
        updateAttempts++;
        console.log(`Password update attempt ${updateAttempts}/${maxUpdateAttempts}...`);

        try {
          // Create timeout promise
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Password update timeout')), 15000);
          });

          // Create update promise
          const updatePromise = supabase.auth.updateUser({
            password: password,
          });

          // Race between update and timeout
          const result = await Promise.race([
            updatePromise,
            timeoutPromise
          ]);
          
          // Type assertion to access properties
          const { data: _updateData, error: updateError } = result as { data: { user: User | null }, error: AuthError | null };

          if (updateError) {
            console.error(`Password update attempt ${updateAttempts} error:`, updateError);

            // Handle specific Supabase errors
            if (updateError.message === 'New password should be different from the old password.') {
              setError('Das neue Passwort muss sich vom aktuellen Passwort unterscheiden. Bitte wählen Sie ein anderes Passwort.');
              setPassword('');
              setConfirmPassword('');
              setIsUpdating(false);
              return;
            } else if (updateError.message === 'Password should be at least 6 characters.') {
              setError('Das Passwort muss mindestens 6 Zeichen lang sein.');
              setIsUpdating(false);
              return;
            } else if (updateError.message?.includes('session')) {
              // Session error - retry if attempts remaining
              if (updateAttempts < maxUpdateAttempts) {
                console.log('Session error detected, retrying after session refresh...');
                await supabase.auth.refreshSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                continue;
              }
              setError('Ihre Sitzung ist abgelaufen. Bitte fordern Sie einen neuen Passwort-Reset an.');
              setIsUpdating(false);
              return;
            } else {
              // Other errors
              if (updateAttempts < maxUpdateAttempts) {
                console.log(`Update failed, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                continue;
              }
              setError(`Passwort konnte nicht aktualisiert werden: ${updateError.message}`);
              setIsUpdating(false);
              return;
            }
          }

          // Success
          console.log(`Password update attempt ${updateAttempts} successful`);
          updateSuccess = true;

        } catch (updateCatchError) {
          console.error(`Password update attempt ${updateAttempts} exception:`, updateCatchError);
          
          if (updateCatchError instanceof Error && updateCatchError.message === 'Password update timeout') {
            if (updateAttempts < maxUpdateAttempts) {
              console.log('Update timeout, retrying...');
              continue;
            }
            setError('Das Aktualisieren des Passworts dauert zu lange. Bitte versuchen Sie es später erneut.');
            setIsUpdating(false);
            return;
          }

          if (updateAttempts >= maxUpdateAttempts) {
            setError('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
            setIsUpdating(false);
            return;
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (!updateSuccess) {
        setError('Passwort konnte nach mehreren Versuchen nicht aktualisiert werden. Bitte versuchen Sie es später erneut.');
        setIsUpdating(false);
        return;
      }

      // Password update was successful
      console.log('Password updated successfully, preparing to redirect...');
      setMessage('Ihr Passwort wurde erfolgreich aktualisiert. Sie werden zur Anmeldeseite weitergeleitet...');      // Ensure we're not in an updating state before proceeding
      setIsUpdating(false);

      console.log('Password update successful, preparing redirect...');
      
      // Small delay to show success message before redirect
      setTimeout(() => {
        // Sign out and redirect
        supabase.auth.signOut().finally(() => {
          window.location.href = '/login?message=password-updated';
        });
      }, 1500);

    } catch (error) {
      console.error('Unexpected error updating password:', error);

      // Handle unexpected errors
      let errorMessage = 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';

      if (error instanceof Error) {
        if (error.message.includes('Session validation failed') || error.message.includes('No valid session')) {
          errorMessage = 'Ihre Sitzung ist ungültig oder abgelaufen. Bitte fordern Sie einen neuen Passwort-Reset an.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Netzwerkfehler. Bitte überprüfen Sie Ihre Internetverbindung.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Die Anfrage dauerte zu lange. Bitte versuchen Sie es später erneut.';
        }
      }

      setError(errorMessage);
      setIsUpdating(false);
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">
            Sitzung wird überprüft...
            {checkAttempts > 0 && (
              <span className="block text-sm text-[var(--color-muted-foreground)] mt-1">
                Versuch {checkAttempts + 1} von 4
              </span>
            )}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center justify-start pt-16 bg-[var(--color-background)] min-h-screen transition-colors duration-200`}>
      <div className="w-full max-w-md p-8 space-y-6 bg-[var(--color-card)] rounded-lg shadow-lg border border-[var(--color-border)]">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-[var(--color-card-foreground)] mb-2">
            Neues Passwort vergeben
          </h1>
          <p className="text-sm text-[var(--color-muted-foreground)]">
            Bitte geben Sie ein neues Passwort für Ihr Konto ein.
          </p>
        </div>

        {error && (
          <div className="p-4 text-sm rounded-md bg-[var(--color-destructive)] text-[var(--color-destructive-foreground)]" role="alert">
            <span>{error}</span>
            {(error.includes('Ungültige') || error.includes('abgelaufen')) && (
              <div className="mt-2">
                <Link
                  href="/forgot-password"
                  className="font-medium text-[var(--color-destructive-foreground)] hover:underline"
                >
                  Neuen Passwort-Reset anfordern
                </Link>
              </div>
            )}
            {error.includes('vom aktuellen Passwort unterscheiden') && (
              <div className="mt-2">
                <p className="text-[var(--color-destructive-foreground)]">
                  Bitte geben Sie ein neues Passwort ein, das sich von Ihrem aktuellen Passwort unterscheidet.
                </p>
              </div>
            )}
          </div>
        )}

        {message ? (
          <div className="p-4 text-sm rounded-md bg-[var(--color-success)] text-[var(--color-success-foreground)]">
            <span>{message}</span>
          </div>
        ) : (
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-[var(--color-card-foreground)] mb-1"
                >
                  Neues Passwort
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  className={`w-full px-4 py-2 rounded-md border ${
                    error && error.includes('Passwort') ? 'border-[var(--color-destructive)]' : 'border-[var(--color-border)]'
                  } bg-[var(--color-input)] text-[var(--color-foreground)] placeholder-[var(--color-muted-foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent`}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                  <div className="mt-2">
                    <div className="flex items-center space-x-1 mb-1">
                      <div className={`h-1 flex-1 rounded-full ${passwordStrength >= 1 ? 'bg-red-500' : 'bg-gray-300'}`}></div>
                      <div className={`h-1 flex-1 rounded-full ${passwordStrength >= 2 ? 'bg-yellow-500' : 'bg-gray-300'}`}></div>
                      <div className={`h-1 flex-1 rounded-full ${passwordStrength >= 3 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    </div>
                    <p className="text-xs text-[var(--color-muted-foreground)]">
                      {passwordStrength === 0 && 'Mindestens 6 Zeichen erforderlich'}
                      {passwordStrength === 1 && 'Schwaches Passwort'}
                      {passwordStrength === 2 && 'Mittleres Passwort'}
                      {passwordStrength === 3 && 'Starkes Passwort'}
                    </p>
                  </div>
                  <div className="mt-2">
                    <p className="text-xs text-[var(--color-muted-foreground)]">
                      Für ein sicheres Passwort empfehlen wir:
                    </p>
                    <ul className="list-disc pl-5 text-xs text-[var(--color-muted-foreground)]">
                      <li className={password.length >= 8 ? 'text-green-500' : ''}>Mindestens 8 Zeichen</li>
                      <li className={/[A-Z]/.test(password) ? 'text-green-500' : ''}>Mindestens ein Großbuchstabe</li>
                      <li className={/[0-9]/.test(password) || /[^A-Za-z0-9]/.test(password) ? 'text-green-500' : ''}>Mindestens eine Zahl oder ein Sonderzeichen</li>
                    </ul>
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="confirm-password"
                    className="block text-sm font-medium text-[var(--color-card-foreground)] mb-1"
                  >
                    Passwort bestätigen
                  </label>
                  <input
                    id="confirm-password"
                    name="confirm-password"
                    type="password"
                    autoComplete="new-password"
                    required
                    className={`w-full px-4 py-2 rounded-md border ${
                      error && error.includes('stimmen nicht überein') ? 'border-[var(--color-destructive)]' : 'border-[var(--color-border)]'
                    } bg-[var(--color-input)] text-[var(--color-foreground)] placeholder-[var(--color-muted-foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent`}
                    placeholder="••••••••"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  {password && confirmPassword && password !== confirmPassword && (
                    <p className="mt-1 text-xs text-[var(--color-destructive)]">
                      Die Passwörter stimmen nicht überein.
                    </p>
                  )}
                </div>
              </div>

              <Button
                type="submit"
                className="w-full py-2 px-4 bg-[var(--color-primary)] text-[var(--color-primary-foreground)] hover:bg-[var(--color-primary-hover)] focus:ring-2 focus:ring-offset-2 focus:ring-[var(--color-primary)] transition-colors duration-200"
                disabled={isUpdating || !password || !confirmPassword}
              >
                                {isUpdating ? 'Wird aktualisiert...' : 'Passwort aktualisieren'}              </Button>            </form>        )}        <div className="text-center text-sm pt-4 border-t border-[var(--color-border)]">          <Link            href="/login"            className="font-medium text-[var(--color-primary)] hover:underline hover:text-[var(--color-primary-hover)] transition-colors"          >            ← Zurück zur Anmeldung          </Link>        </div>      </div>    </div>  );}
