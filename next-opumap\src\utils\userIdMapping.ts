/**
 * This function used to map Supabase UUIDs to numeric IDs for database tables.
 * After the database refactoring, we now use UUIDs directly in all tables.
 * This function is kept for backward compatibility but now simply returns the UUID.
 *
 * @param uuid The UUID from Supabase Auth
 * @returns The same UUID to be used directly in database tables
 */

export async function mapUuidToNumericId(uuid: string): Promise<string> {
  // Simply return the UUID as is - all tables now use UUIDs directly
  return uuid;
}
