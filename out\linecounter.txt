===============================================================================
EXTENSION NAME : linecounter
EXTENSION VERSION : 0.2.7
-------------------------------------------------------------------------------
count time : 2025-05-23 03:16:49
count workspace : c:\Users\<USER>\Documents\VS-Code\OpuMap-nextJS
total files : 701
total code lines : 537984
total comment lines : 82960
total blank lines : 11577

    statistics
   |      extension|     total code|  total comment|    total blank|percent|
   -------------------------------------------------------------------------
   |            .md|           2492|             23|            224|   0.46|
   |            .py|            102|             29|             19|  0.019|
   |               |             78|             37|             16|  0.014|
   |           .mjs|             34|             11|              6| 0.0063|
   |            .ts|           6618|           1277|           1009|    1.2|
   |          .yaml|             51|              0|             10| 0.0095|
   |          .json|          13850|              3|            209|    2.6|
   |            .js|         488043|          80852|           6605|     91|
   |           .sql|             18|              5|              4| 0.0033|
   |           .map|           7050|              0|              0|    1.3|
   |           .svg|            143|              0|              1|  0.027|
   |           .tsx|           8176|            653|            985|    1.5|
   |         .local|             35|              0|             10| 0.0065|
   |           .css|          11226|             58|           2465|    2.1|
   |          .html|             13|              0|              0| 0.0024|
   |           .yml|             24|              0|              2| 0.0045|
   |            .sh|             31|             12|             12| 0.0058|
   -------------------------------------------------------------------------
.github\workflows\backup.yaml, code is 51, comment is 0, blank is 10.
er_diagram_full.md, code is 189, comment is 0, blank is 2.
generate_hierarchy.py, code is 41, comment is 4, blank is 5.
hierarchy.md, code is 1569, comment is 0, blank is 0.
next-opumap\.env.local, code is 35, comment is 0, blank is 10.
next-opumap\.gitignore, code is 6, comment is 36, blank is 1.
next-opumap\.next\app-build-manifest.json, code is 195, comment is 0, blank is 9.
next-opumap\.next\build-manifest.json, code is 21, comment is 0, blank is 3.
next-opumap\.next\build\chunks\[root-of-the-server]__04d7a048._.js, code is 460, comment is 29, blank is 11.
next-opumap\.next\build\chunks\[root-of-the-server]__04d7a048._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\build\chunks\[root-of-the-server]__05f88b00._.js, code is 158, comment is 3, blank is 12.
next-opumap\.next\build\chunks\[root-of-the-server]__05f88b00._.js.map, code is 7, comment is 0, blank is 0.
next-opumap\.next\build\chunks\[turbopack]_runtime.js, code is 570, comment is 123, blank is 0.
next-opumap\.next\build\chunks\[turbopack]_runtime.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\build\chunks\node_modules_b5d1def4._.js, code is 5174, comment is 999, blank is 44.
next-opumap\.next\build\chunks\node_modules_b5d1def4._.js.map, code is 47, comment is 0, blank is 0.
next-opumap\.next\build\chunks\postcss_config_mjs_transform_ts_f0ffbaad._.js, code is 13, comment is 0, blank is 4.
next-opumap\.next\build\chunks\postcss_config_mjs_transform_ts_f0ffbaad._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\cache\.rscinfo, code is 1, comment is 0, blank is 0.
next-opumap\.next\cache\images\2z4aI8WEVbvE9xqISQ9nlb4Unh8YSiv1M7hNVIA6vxI\86400.1748046130313.zbg6j3WeAzqxnFuTexFMJPhoNt5vh2WHBnuLMT5Orzo.InYxNDQi.webp, it is a binary file.
next-opumap\.next\cache\images\LBMU382nMc7e4OgToM9wS9vifdiDvQZKZXoE0jWJ6jE\86400.1748046125990.EUfEK9b9BlS1AWEs2QRBn2u3wT6Dyli782bjpKO1Uz0.InYwIg.webp, it is a binary file.
next-opumap\.next\cache\images\UWnsBdFBffiqzQXF7W_pyhpEsI4SNzZNszp_CP-q5nI\86400.1748046122146.zbg6j3WeAzqxnFuTexFMJPhoNt5vh2WHBnuLMT5Orzo.InYxNDQi.webp, it is a binary file.
next-opumap\.next\cache\images\X6hoL2n_MoX5qorCUfBdNBV99wAyVZTrtDlqEVSPF7A\86400.1748046058751.zbg6j3WeAzqxnFuTexFMJPhoNt5vh2WHBnuLMT5Orzo.InYxNDQi.webp, it is a binary file.
next-opumap\.next\fallback-build-manifest.json, code is 13, comment is 0, blank is 1.
next-opumap\.next\package.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app-paths-manifest.json, code is 17, comment is 0, blank is 0.
next-opumap\.next\server\app\_not-found\page.js, code is 14, comment is 0, blank is 0.
next-opumap\.next\server\app\_not-found\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\_not-found\page\app-build-manifest.json, code is 20, comment is 0, blank is 1.
next-opumap\.next\server\app\_not-found\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\_not-found\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\_not-found\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\_not-found\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\_not-found\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\_not-found\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route.js, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route\app-build-manifest.json, code is 12, comment is 0, blank is 1.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route\next-font-manifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\deep\[placeId]\route_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route.js, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route\app-build-manifest.json, code is 12, comment is 0, blank is 1.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route\next-font-manifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\api\analyses\quick\[placeId]\route_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route.js, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route\app-build-manifest.json, code is 12, comment is 0, blank is 1.
next-opumap\.next\server\app\api\selected-companies\route\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\api\selected-companies\route\next-font-manifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\api\selected-companies\route_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route.js, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route\app-build-manifest.json, code is 12, comment is 0, blank is 1.
next-opumap\.next\server\app\api\strategies\route\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\api\strategies\route\next-font-manifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategies\route_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route.js, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route\app-build-manifest.json, code is 12, comment is 0, blank is 1.
next-opumap\.next\server\app\api\strategy-scan-results\route\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\api\strategy-scan-results\route\next-font-manifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\api\strategy-scan-results\route_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route.js, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route\app-build-manifest.json, code is 12, comment is 0, blank is 1.
next-opumap\.next\server\app\auth\callback\route\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\auth\callback\route\next-font-manifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\auth\callback\route_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\forgot-password\page.js, code is 15, comment is 0, blank is 0.
next-opumap\.next\server\app\forgot-password\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\forgot-password\page\app-build-manifest.json, code is 22, comment is 0, blank is 1.
next-opumap\.next\server\app\forgot-password\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\forgot-password\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\forgot-password\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\forgot-password\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\forgot-password\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\forgot-password\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\legal\impressum\page.js, code is 15, comment is 0, blank is 0.
next-opumap\.next\server\app\legal\impressum\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\legal\impressum\page\app-build-manifest.json, code is 20, comment is 0, blank is 1.
next-opumap\.next\server\app\legal\impressum\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\legal\impressum\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\legal\impressum\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\legal\impressum\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\legal\impressum\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\legal\impressum\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\login\page.js, code is 12, comment is 0, blank is 0.
next-opumap\.next\server\app\login\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\login\page\app-build-manifest.json, code is 23, comment is 0, blank is 1.
next-opumap\.next\server\app\login\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\login\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\login\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\login\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\login\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\login\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\opulab\page.js, code is 15, comment is 0, blank is 0.
next-opumap\.next\server\app\opulab\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\opulab\page\app-build-manifest.json, code is 32, comment is 0, blank is 1.
next-opumap\.next\server\app\opulab\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\opulab\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\opulab\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\opulab\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\opulab\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\opulab\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\opuscanner\page.js, code is 15, comment is 0, blank is 0.
next-opumap\.next\server\app\opuscanner\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\opuscanner\page\app-build-manifest.json, code is 32, comment is 0, blank is 1.
next-opumap\.next\server\app\opuscanner\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\opuscanner\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\opuscanner\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\opuscanner\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\opuscanner\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\opuscanner\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\page.js, code is 15, comment is 0, blank is 0.
next-opumap\.next\server\app\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\page\app-build-manifest.json, code is 26, comment is 0, blank is 1.
next-opumap\.next\server\app\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\profile\page.js, code is 20, comment is 0, blank is 0.
next-opumap\.next\server\app\profile\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\profile\page\app-build-manifest.json, code is 30, comment is 0, blank is 1.
next-opumap\.next\server\app\profile\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\profile\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\profile\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\profile\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\profile\page\server-reference-manifest.json, code is 16, comment is 0, blank is 0.
next-opumap\.next\server\app\profile\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\app\reset-password\page.js, code is 12, comment is 0, blank is 0.
next-opumap\.next\server\app\reset-password\page.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\app\reset-password\page\app-build-manifest.json, code is 22, comment is 0, blank is 1.
next-opumap\.next\server\app\reset-password\page\app-paths-manifest.json, code is 3, comment is 0, blank is 0.
next-opumap\.next\server\app\reset-password\page\build-manifest.json, code is 17, comment is 0, blank is 2.
next-opumap\.next\server\app\reset-password\page\next-font-manifest.json, code is 9, comment is 0, blank is 1.
next-opumap\.next\server\app\reset-password\page\react-loadable-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\app\reset-password\page\server-reference-manifest.json, code is 4, comment is 0, blank is 0.
next-opumap\.next\server\app\reset-password\page_client-reference-manifest.js, code is 2, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__185aa1bf._.js, code is 420, comment is 37, blank is 31.
next-opumap\.next\server\chunks\[root-of-the-server]__185aa1bf._.js.map, code is 9, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__395a2fa8._.js, code is 169, comment is 12, blank is 30.
next-opumap\.next\server\chunks\[root-of-the-server]__395a2fa8._.js.map, code is 8, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__4512b88f._.js, code is 171, comment is 11, blank is 30.
next-opumap\.next\server\chunks\[root-of-the-server]__4512b88f._.js.map, code is 8, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__4f95f7eb._.js, code is 50, comment is 0, blank is 18.
next-opumap\.next\server\chunks\[root-of-the-server]__4f95f7eb._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__813b5ba3._.js, code is 171, comment is 10, blank is 30.
next-opumap\.next\server\chunks\[root-of-the-server]__813b5ba3._.js.map, code is 8, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__9905feae._.js, code is 316, comment is 34, blank is 34.
next-opumap\.next\server\chunks\[root-of-the-server]__9905feae._.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[root-of-the-server]__d236f8d6._.js, code is 370, comment is 29, blank is 31.
next-opumap\.next\server\chunks\[root-of-the-server]__d236f8d6._.js.map, code is 9, comment is 0, blank is 0.
next-opumap\.next\server\chunks\[turbopack]_runtime.js, code is 570, comment is 123, blank is 0.
next-opumap\.next\server\chunks\[turbopack]_runtime.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_01232cb3._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\node_modules_01232cb3._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_115f3870._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\node_modules_115f3870._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_2ba84061._.js, code is 7541, comment is 1761, blank is 68.
next-opumap\.next\server\chunks\node_modules_2ba84061._.js.map, code is 62, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_675e50bc._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\node_modules_675e50bc._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_6c50d3dd._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\node_modules_6c50d3dd._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_9410cdcc._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\node_modules_9410cdcc._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_@supabase_auth-js_dist_module_fbd19a5c._.js, code is 3459, comment is 495, blank is 23.
next-opumap\.next\server\chunks\node_modules_@supabase_auth-js_dist_module_fbd19a5c._.js.map, code is 20, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_c86992ea._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\node_modules_c86992ea._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_fce9e076._.js, code is 2122, comment is 844, blank is 18.
next-opumap\.next\server\chunks\node_modules_fce9e076._.js.map, code is 23, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_next_51402897._.js, code is 3159, comment is 360, blank is 27.
next-opumap\.next\server\chunks\node_modules_next_51402897._.js.map, code is 103, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_next_63cebe7e._.js, code is 3643, comment is 406, blank is 34.
next-opumap\.next\server\chunks\node_modules_next_63cebe7e._.js.map, code is 103, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_next_7e6fa797._.js, code is 3159, comment is 360, blank is 27.
next-opumap\.next\server\chunks\node_modules_next_7e6fa797._.js.map, code is 103, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_next_e6934dd0._.js, code is 3643, comment is 406, blank is 34.
next-opumap\.next\server\chunks\node_modules_next_e6934dd0._.js.map, code is 103, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_next_e9a8f822._.js, code is 3643, comment is 406, blank is 34.
next-opumap\.next\server\chunks\node_modules_next_e9a8f822._.js.map, code is 103, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_next_f6ac1afe._.js, code is 3159, comment is 360, blank is 27.
next-opumap\.next\server\chunks\node_modules_next_f6ac1afe._.js.map, code is 103, comment is 0, blank is 0.
next-opumap\.next\server\chunks\node_modules_tr46_816df9d9._.js, code is 167, comment is 3, blank is 5.
next-opumap\.next\server\chunks\node_modules_tr46_816df9d9._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__03669266._.js, code is 766, comment is 39, blank is 30.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__03669266._.js.map, code is 31, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__122c207c._.js, code is 3204, comment is 259, blank is 66.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__122c207c._.js.map, code is 29, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__1cc4b4b0._.js, code is 32, comment is 0, blank is 7.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__1cc4b4b0._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__4f95f7eb._.js, code is 50, comment is 0, blank is 18.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__4f95f7eb._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__50a7c09c._.js, code is 44, comment is 0, blank is 16.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__50a7c09c._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__5c0b48c9._.js, code is 5879, comment is 498, blank is 135.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__5c0b48c9._.js.map, code is 35, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__6c51c0f7._.js, code is 44, comment is 0, blank is 16.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__6c51c0f7._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__6cd1a4b9._.js, code is 418, comment is 22, blank is 25.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__6cd1a4b9._.js.map, code is 27, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__6e363dbc._.js, code is 692, comment is 38, blank is 23.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__6e363dbc._.js.map, code is 24, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__8230e97a._.js, code is 1685, comment is 152, blank is 40.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__8230e97a._.js.map, code is 19, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__875dd8b7._.js, code is 5281, comment is 491, blank is 115.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__875dd8b7._.js.map, code is 37, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__a9674d6c._.js, code is 215, comment is 11, blank is 26.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__a9674d6c._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__abd0681f._.js, code is 1279, comment is 103, blank is 33.
next-opumap\.next\server\chunks\ssr\[root-of-the-server]__abd0681f._.js.map, code is 17, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js, code is 436, comment is 25, blank is 7.
next-opumap\.next\server\chunks\ssr\[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\[turbopack]_runtime.js, code is 570, comment is 123, blank is 0.
next-opumap\.next\server\chunks\ssr\[turbopack]_runtime.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_15f62e72._.js, code is 288, comment is 6, blank is 27.
next-opumap\.next\server\chunks\ssr\_15f62e72._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_51152af7._.js, code is 315, comment is 7, blank is 28.
next-opumap\.next\server\chunks\ssr\_51152af7._.js.map, code is 12, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_56082b36._.js, code is 296, comment is 6, blank is 27.
next-opumap\.next\server\chunks\ssr\_56082b36._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_84796e78._.js, code is 296, comment is 6, blank is 27.
next-opumap\.next\server\chunks\ssr\_84796e78._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_a7eca4fb._.js, code is 283, comment is 7, blank is 24.
next-opumap\.next\server\chunks\ssr\_a7eca4fb._.js.map, code is 9, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_e06965b1._.js, code is 296, comment is 6, blank is 27.
next-opumap\.next\server\chunks\ssr\_e06965b1._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_e50bd33a._.js, code is 546, comment is 37, blank is 26.
next-opumap\.next\server\chunks\ssr\_e50bd33a._.js.map, code is 9, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\_f18ae84b._.js, code is 296, comment is 6, blank is 27.
next-opumap\.next\server\chunks\ssr\_f18ae84b._.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_261f6ba5._.js, code is 13904, comment is 4481, blank is 201.
next-opumap\.next\server\chunks\ssr\node_modules_261f6ba5._.js.map, code is 288, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_4e19923f._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\ssr\node_modules_4e19923f._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_96715ba7._.js, code is 129, comment is 22, blank is 6.
next-opumap\.next\server\chunks\ssr\node_modules_96715ba7._.js.map, code is 8, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_9e5d50fc._.js, code is 3962, comment is 304, blank is 58.
next-opumap\.next\server\chunks\ssr\node_modules_9e5d50fc._.js.map, code is 57, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_@floating-ui_90d70670._.js, code is 2154, comment is 291, blank is 25.
next-opumap\.next\server\chunks\ssr\node_modules_@floating-ui_90d70670._.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_@radix-ui_a5d9b822._.js, code is 3776, comment is 79, blank is 41.
next-opumap\.next\server\chunks\ssr\node_modules_@radix-ui_a5d9b822._.js.map, code is 30, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_@react-google-maps_api_dist_esm_52df33fe.js, code is 9329, comment is 886, blank is 14.
next-opumap\.next\server\chunks\ssr\node_modules_@react-google-maps_api_dist_esm_52df33fe.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_@supabase_auth-js_dist_module_5c23cb0d._.js, code is 3478, comment is 498, blank is 23.
next-opumap\.next\server\chunks\ssr\node_modules_@supabase_auth-js_dist_module_5c23cb0d._.js.map, code is 20, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_@supabase_auth-js_dist_module_f390e684._.js, code is 3459, comment is 495, blank is 23.
next-opumap\.next\server\chunks\ssr\node_modules_@supabase_auth-js_dist_module_f390e684._.js.map, code is 20, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_@uiw_react-md-editor_esm_b843d204._.js, code is 2839, comment is 90, blank is 47.
next-opumap\.next\server\chunks\ssr\node_modules_@uiw_react-md-editor_esm_b843d204._.js.map, code is 44, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_be063d58._.js, code is 72, comment is 2, blank is 10.
next-opumap\.next\server\chunks\ssr\node_modules_be063d58._.js.map, code is 12, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_d4373687._.js, code is 13909, comment is 4464, blank is 200.
next-opumap\.next\server\chunks\ssr\node_modules_d4373687._.js.map, code is 287, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_d536aa3e._.js, code is 5273, comment is 2227, blank is 63.
next-opumap\.next\server\chunks\ssr\node_modules_d536aa3e._.js.map, code is 132, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_dcebf7b7._.js, code is 13698, comment is 2038, blank is 157.
next-opumap\.next\server\chunks\ssr\node_modules_dcebf7b7._.js.map, code is 129, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_dfaf511b._.js, code is 2122, comment is 844, blank is 18.
next-opumap\.next\server\chunks\ssr\node_modules_dfaf511b._.js.map, code is 23, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_e3d59f58._.js, code is 7541, comment is 1761, blank is 68.
next-opumap\.next\server\chunks\ssr\node_modules_e3d59f58._.js.map, code is 62, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_eb781f2a._.js, code is 25315, comment is 5714, blank is 439.
next-opumap\.next\server\chunks\ssr\node_modules_eb781f2a._.js.map, code is 447, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_f4190f3f._.js, code is 2122, comment is 844, blank is 18.
next-opumap\.next\server\chunks\ssr\node_modules_f4190f3f._.js.map, code is 23, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_f55c08a1._.js, code is 22, comment is 0, blank is 5.
next-opumap\.next\server\chunks\ssr\node_modules_f55c08a1._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_lodash_b1d46715._.js, code is 2287, comment is 2179, blank is 160.
next-opumap\.next\server\chunks\ssr\node_modules_lodash_b1d46715._.js.map, code is 196, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_micromark-core-commonmark_dev_lib_fbbb9cae._.js, code is 2852, comment is 2602, blank is 25.
next-opumap\.next\server\chunks\ssr\node_modules_micromark-core-commonmark_dev_lib_fbbb9cae._.js.map, code is 27, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_53d0be67._.js, code is 12, comment is 1, blank is 5.
next-opumap\.next\server\chunks\ssr\node_modules_next_53d0be67._.js.map, code is 7, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_b941f186._.js, code is 989, comment is 181, blank is 17.
next-opumap\.next\server\chunks\ssr\node_modules_next_b941f186._.js.map, code is 80, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_7f77008b._.js, code is 2563, comment is 318, blank is 36.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_7f77008b._.js.map, code is 66, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_a17f26a9._.js, code is 9408, comment is 923, blank is 131.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_a17f26a9._.js.map, code is 141, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_client_ac4c1404._.js, code is 10949, comment is 1088, blank is 164.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_client_ac4c1404._.js.map, code is 194, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_client_components_forbidden-error_ea7ea172.js, code is 31, comment is 2, blank is 4.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_client_components_forbidden-error_ea7ea172.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_client_components_unauthorized-error_c8949b27.js, code is 31, comment is 2, blank is 4.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_client_components_unauthorized-error_c8949b27.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_esm_3657da74._.js, code is 3693, comment is 760, blank is 79.
next-opumap\.next\server\chunks\ssr\node_modules_next_dist_esm_3657da74._.js.map, code is 76, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_parse5_dist_2032ba6e._.js, code is 8652, comment is 430, blank is 27.
next-opumap\.next\server\chunks\ssr\node_modules_parse5_dist_2032ba6e._.js.map, code is 19, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_react-icons_fa_index_mjs_a1beb003._.js, code is 30620, comment is 2, blank is 4.
next-opumap\.next\server\chunks\ssr\node_modules_react-icons_fa_index_mjs_a1beb003._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_react-icons_lib_b806b5fe._.js, code is 150, comment is 1, blank is 6.
next-opumap\.next\server\chunks\ssr\node_modules_react-icons_lib_b806b5fe._.js.map, code is 7, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_recharts_es6_b96164d7._.js, code is 15398, comment is 584, blank is 109.
next-opumap\.next\server\chunks\ssr\node_modules_recharts_es6_b96164d7._.js.map, code is 69, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_refractor_lang_9819fb3a._.js, code is 8202, comment is 1259, blank is 168.
next-opumap\.next\server\chunks\ssr\node_modules_refractor_lang_9819fb3a._.js.map, code is 302, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_refractor_lib_3e6ffb2f._.js, code is 1537, comment is 491, blank is 9.
next-opumap\.next\server\chunks\ssr\node_modules_refractor_lib_3e6ffb2f._.js.map, code is 9, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js, code is 817, comment is 82, blank is 19.
next-opumap\.next\server\chunks\ssr\node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_tr46_1a859af0._.js, code is 167, comment is 3, blank is 5.
next-opumap\.next\server\chunks\ssr\node_modules_tr46_1a859af0._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\node_modules_tr46_3d1b2cf9._.js, code is 167, comment is 3, blank is 5.
next-opumap\.next\server\chunks\ssr\node_modules_tr46_3d1b2cf9._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\src_app_67607b76._.js, code is 543, comment is 44, blank is 12.
next-opumap\.next\server\chunks\ssr\src_app_67607b76._.js.map, code is 8, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\src_app_forgot-password_page_tsx_83245f1a._.js, code is 282, comment is 20, blank is 4.
next-opumap\.next\server\chunks\ssr\src_app_forgot-password_page_tsx_83245f1a._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\chunks\ssr\src_app_reset-password_page_tsx_9e7531ef._.js, code is 718, comment is 74, blank is 10.
next-opumap\.next\server\chunks\ssr\src_app_reset-password_page_tsx_9e7531ef._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\server\edge\chunks\[root-of-the-server]__98fe5a60._.js, code is 104, comment is 29, blank is 9.
next-opumap\.next\server\edge\chunks\[root-of-the-server]__98fe5a60._.js.map, code is 7, comment is 0, blank is 0.
next-opumap\.next\server\edge\chunks\_061ca3cf._.js, code is 17938, comment is 2527, blank is 258.
next-opumap\.next\server\edge\chunks\_061ca3cf._.js.map, code is 162, comment is 0, blank is 0.
next-opumap\.next\server\edge\chunks\edge-wrapper_f1596fbc.js, code is 1307, comment is 353, blank is 3.
next-opumap\.next\server\edge\chunks\edge-wrapper_f1596fbc.js.map, code is 11, comment is 0, blank is 0.
next-opumap\.next\server\interception-route-rewrite-manifest.js, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\middleware-build-manifest.js, code is 23, comment is 0, blank is 3.
next-opumap\.next\server\middleware-manifest.json, code is 31, comment is 0, blank is 2.
next-opumap\.next\server\middleware\middleware-manifest.json, code is 30, comment is 0, blank is 1.
next-opumap\.next\server\next-font-manifest.js, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\next-font-manifest.json, code is 25, comment is 0, blank is 9.
next-opumap\.next\server\pages-manifest.json, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\server-reference-manifest.js, code is 1, comment is 0, blank is 0.
next-opumap\.next\server\server-reference-manifest.json, code is 17, comment is 0, blank is 0.
next-opumap\.next\static\chunks\[next]_internal_font_google_inter_59dee874_module_css_f9ee138c._.single.css, code is 68, comment is 2, blank is 9.
next-opumap\.next\static\chunks\[next]_internal_font_google_inter_59dee874_module_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\[root-of-the-server]__9a168d68._.css, code is 2945, comment is 2, blank is 740.
next-opumap\.next\static\chunks\[root-of-the-server]__9a168d68._.css.map, code is 7, comment is 0, blank is 0.
next-opumap\.next\static\chunks\[turbopack]_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js, code is 436, comment is 25, blank is 6.
next-opumap\.next\static\chunks\[turbopack]_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\[turbopack]_browser_dev_hmr-client_hmr-client_ts_66796270._.js, code is 6, comment is 0, blank is 1.
next-opumap\.next\static\chunks\[turbopack]_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js, code is 13, comment is 0, blank is 3.
next-opumap\.next\static\chunks\[turbopack]_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\static\chunks\_93808211._.js, code is 1310, comment is 349, blank is 3.
next-opumap\.next\static\chunks\_93808211._.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\static\chunks\_e69f0d32._.js, code is 10, comment is 0, blank is 1.
next-opumap\.next\static\chunks\node_modules_0195e1ca._.js, code is 13183, comment is 5044, blank is 229.
next-opumap\.next\static\chunks\node_modules_0195e1ca._.js.map, code is 285, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_17e71297._.js, code is 3962, comment is 304, blank is 57.
next-opumap\.next\static\chunks\node_modules_17e71297._.js.map, code is 57, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_4640ed63._.js, code is 11029, comment is 1625, blank is 144.
next-opumap\.next\static\chunks\node_modules_4640ed63._.js.map, code is 118, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_5330ec2d._.js, code is 5779, comment is 1398, blank is 64.
next-opumap\.next\static\chunks\node_modules_5330ec2d._.js.map, code is 130, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_8caebb89._.js, code is 22, comment is 0, blank is 4.
next-opumap\.next\static\chunks\node_modules_8caebb89._.js.map, code is 5, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_9b7f117b._.js, code is 24738, comment is 6388, blank is 469.
next-opumap\.next\static\chunks\node_modules_9b7f117b._.js.map, code is 445, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@floating-ui_9ec1fa39._.js, code is 2172, comment is 291, blank is 24.
next-opumap\.next\static\chunks\node_modules_@floating-ui_9ec1fa39._.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@radix-ui_b220bb54._.js, code is 4070, comment is 79, blank is 40.
next-opumap\.next\static\chunks\node_modules_@radix-ui_b220bb54._.js.map, code is 30, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@react-google-maps_api_dist_esm_d50285f1.js, code is 9822, comment is 886, blank is 13.
next-opumap\.next\static\chunks\node_modules_@react-google-maps_api_dist_esm_d50285f1.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@supabase_auth-js_dist_module_33324aae._.js, code is 3478, comment is 498, blank is 22.
next-opumap\.next\static\chunks\node_modules_@supabase_auth-js_dist_module_33324aae._.js.map, code is 20, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@swc_helpers_cjs_00636ac3._.js, code is 82, comment is 1, blank is 7.
next-opumap\.next\static\chunks\node_modules_@swc_helpers_cjs_00636ac3._.js.map, code is 10, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_0631b38e._.css, code is 2504, comment is 9, blank is 474.
next-opumap\.next\static\chunks\node_modules_@uiw_0631b38e._.css.map, code is 13, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-markdown-preview_esm_styles_markdown_css_f9ee138c._.single.css, code is 929, comment is 2, blank is 178.
next-opumap\.next\static\chunks\node_modules_@uiw_react-markdown-preview_esm_styles_markdown_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-markdown-preview_markdown_css_f9ee138c._.single.css, code is 929, comment is 2, blank is 178.
next-opumap\.next\static\chunks\node_modules_@uiw_react-markdown-preview_markdown_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_DragBar_index_css_f9ee138c._.single.css, code is 18, comment is 2, blank is 2.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_DragBar_index_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_TextArea_index_css_f9ee138c._.single.css, code is 111, comment is 2, blank is 18.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_TextArea_index_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_Toolbar_Child_css_f9ee138c._.single.css, code is 26, comment is 2, blank is 5.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_Toolbar_Child_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_Toolbar_index_css_f9ee138c._.single.css, code is 74, comment is 2, blank is 12.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_components_Toolbar_index_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_d1dc9583._.js, code is 2899, comment is 90, blank is 46.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_d1dc9583._.js.map, code is 44, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_index_css_f9ee138c._.single.css, code is 94, comment is 2, blank is 18.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_esm_index_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_markdown-editor_css_f9ee138c._.single.css, code is 323, comment is 2, blank is 55.
next-opumap\.next\static\chunks\node_modules_@uiw_react-md-editor_markdown-editor_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_f08ff31a._.js, code is 13177, comment is 5061, blank is 230.
next-opumap\.next\static\chunks\node_modules_f08ff31a._.js.map, code is 286, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_lodash_20634561._.js, code is 2287, comment is 2179, blank is 159.
next-opumap\.next\static\chunks\node_modules_lodash_20634561._.js.map, code is 196, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js, code is 2852, comment is 2602, blank is 24.
next-opumap\.next\static\chunks\node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js.map, code is 27, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_next_d8bd0c66._.js, code is 5450, comment is 556, blank is 47.
next-opumap\.next\static\chunks\node_modules_next_d8bd0c66._.js.map, code is 55, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_next_dist_2ecbf5fa._.js, code is 926, comment is 124, blank is 17.
next-opumap\.next\static\chunks\node_modules_next_dist_2ecbf5fa._.js.map, code is 47, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_next_dist_build_polyfills_polyfill-nomodule.js, code is 1, comment is 1, blank is 0.
next-opumap\.next\static\chunks\node_modules_next_dist_client_8f19e6fb._.js, code is 9759, comment is 894, blank is 152.
next-opumap\.next\static\chunks\node_modules_next_dist_client_8f19e6fb._.js.map, code is 204, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_next_dist_compiled_2ce9398a._.js, code is 17131, comment is 250, blank is 31.
next-opumap\.next\static\chunks\node_modules_next_dist_compiled_2ce9398a._.js.map, code is 26, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_parse5_dist_afbcc6e3._.js, code is 8652, comment is 430, blank is 26.
next-opumap\.next\static\chunks\node_modules_parse5_dist_afbcc6e3._.js.map, code is 19, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_recharts_es6_7bff9f4c._.js, code is 15423, comment is 584, blank is 108.
next-opumap\.next\static\chunks\node_modules_recharts_es6_7bff9f4c._.js.map, code is 69, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_refractor_lang_b98ac696._.js, code is 8202, comment is 1259, blank is 168.
next-opumap\.next\static\chunks\node_modules_refractor_lang_b98ac696._.js.map, code is 302, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_refractor_lib_3179224d._.js, code is 1537, comment is 491, blank is 8.
next-opumap\.next\static\chunks\node_modules_refractor_lib_3179224d._.js.map, code is 9, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js, code is 817, comment is 82, blank is 19.
next-opumap\.next\static\chunks\node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\node_modules_ws_browser_a129af95.js, code is 6, comment is 0, blank is 1.
next-opumap\.next\static\chunks\node_modules_ws_browser_bdb30e1f.js, code is 10, comment is 1, blank is 3.
next-opumap\.next\static\chunks\node_modules_ws_browser_bdb30e1f.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_40259d33._.js, code is 5497, comment is 491, blank is 99.
next-opumap\.next\static\chunks\src_40259d33._.js.map, code is 37, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_77298010._.js, code is 6206, comment is 500, blank is 119.
next-opumap\.next\static\chunks\src_77298010._.js.map, code is 35, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_9ced2248._.js, code is 3457, comment is 261, blank is 50.
next-opumap\.next\static\chunks\src_9ced2248._.js.map, code is 29, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_app_aa0acdfb._.js, code is 564, comment is 44, blank is 11.
next-opumap\.next\static\chunks\src_app_aa0acdfb._.js.map, code is 8, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_app_forgot-password_page_tsx_4f716532._.js, code is 6, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_forgot-password_page_tsx_94ca8e3d._.js, code is 295, comment is 20, blank is 3.
next-opumap\.next\static\chunks\src_app_forgot-password_page_tsx_94ca8e3d._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_app_globals_css_f9ee138c._.single.css, code is 2877, comment is 1, blank is 730.
next-opumap\.next\static\chunks\src_app_globals_css_f9ee138c._.single.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_app_layout_tsx_c0237562._.js, code is 12, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_login_page_tsx_4f716532._.js, code is 7, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_opulab_page_tsx_4f716532._.js, code is 16, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_opuscanner_page_tsx_4f716532._.js, code is 16, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_page_tsx_4f716532._.js, code is 10, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_profile_page_tsx_4f716532._.js, code is 14, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_reset-password_page_tsx_4f716532._.js, code is 6, comment is 0, blank is 1.
next-opumap\.next\static\chunks\src_app_reset-password_page_tsx_9679dc2a._.js, code is 761, comment is 74, blank is 9.
next-opumap\.next\static\chunks\src_app_reset-password_page_tsx_9679dc2a._.js.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_app_styles_transitions_module_d00a5443.css, code is 65, comment is 2, blank is 15.
next-opumap\.next\static\chunks\src_app_styles_transitions_module_d00a5443.css.map, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_c0286e96._.js, code is 1332, comment is 103, blank is 17.
next-opumap\.next\static\chunks\src_c0286e96._.js.map, code is 17, comment is 0, blank is 0.
next-opumap\.next\static\chunks\src_ed97f6fd._.js, code is 1838, comment is 153, blank is 17.
next-opumap\.next\static\chunks\src_ed97f6fd._.js.map, code is 19, comment is 0, blank is 0.
next-opumap\.next\static\development\_buildManifest.js, code is 1, comment is 0, blank is 0.
next-opumap\.next\static\development\_clientMiddlewareManifest.json, code is 6, comment is 0, blank is 0.
next-opumap\.next\static\development\_ssgManifest.js, code is 1, comment is 0, blank is 0.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2, it is a binary file.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2, it is a binary file.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.7b3669ea.woff2, it is a binary file.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.761a717c.woff2, it is a binary file.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2, it is a binary file.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2, it is a binary file.
next-opumap\.next\static\media\UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2, it is a binary file.
next-opumap\.next\trace, code is 27, comment is 0, blank is 0.
next-opumap\.next\transform.js, code is 7, comment is 0, blank is 0.
next-opumap\.next\transform.js.map, code is 5, comment is 0, blank is 0.
next-opumap\components.json, code is 21, comment is 0, blank is 0.
next-opumap\db-optimize.js, code is 109, comment is 28, blank is 31.
next-opumap\deploy.sh, code is 31, comment is 12, blank is 12.
next-opumap\DEPLOYMENT.md, code is 74, comment is 3, blank is 36.
next-opumap\docker-compose.yml, code is 24, comment is 0, blank is 2.
next-opumap\Dockerfile, code is 44, comment is 1, blank is 15.
next-opumap\ecosystem.config.js, code is 22, comment is 6, blank is 0.
next-opumap\eslint.config.mjs, code is 17, comment is 0, blank is 4.
next-opumap\middleware.ts, code is 18, comment is 10, blank is 3.
next-opumap\migrations\create_user_id_mappings_table.sql, code is 18, comment is 5, blank is 4.
next-opumap\next-env.d.ts, code is 0, comment is 4, blank is 1.
next-opumap\next.config.mjs, code is 13, comment is 11, blank is 1.
next-opumap\next.config.ts, code is 76, comment is 23, blank is 9.
next-opumap\OPTIMIZATION.md, code is 49, comment is 5, blank is 23.
next-opumap\optimize-dev.js, code is 103, comment is 18, blank is 25.
next-opumap\package-lock.json, code is 12529, comment is 0, blank is 128.
next-opumap\package.json, code is 90, comment is 0, blank is 0.
next-opumap\postcss.config.mjs, code is 4, comment is 0, blank is 1.
next-opumap\public\file.svg, code is 1, comment is 0, blank is 0.
next-opumap\public\globe.svg, code is 1, comment is 0, blank is 0.
next-opumap\public\index.html, code is 13, comment is 0, blank is 0.
next-opumap\public\next.svg, code is 1, comment is 0, blank is 0.
next-opumap\public\reagaenzglas-svg.svg, code is 78, comment is 0, blank is 0.
next-opumap\public\schwarz-ohne-bg-svg.svg, code is 16, comment is 0, blank is 1.
next-opumap\public\vercel.svg, code is 1, comment is 0, blank is 0.
next-opumap\public\weiss-ohne-bg-svg.svg, code is 44, comment is 0, blank is 0.
next-opumap\public\window.svg, code is 1, comment is 0, blank is 0.
next-opumap\README.md, code is 111, comment is 2, blank is 36.
next-opumap\scripts\add-selected-companies-table.ts, code is 51, comment is 6, blank is 10.
next-opumap\scripts\add-slider-values-to-strategies.ts, code is 41, comment is 5, blank is 9.
next-opumap\scripts\apply-user-id-mappings-migration.js, code is 60, comment is 10, blank is 20.
next-opumap\scripts\setup-db.ts, code is 114, comment is 10, blank is 16.
next-opumap\src\api\opulab-chancen-scan.ts, code is 38, comment is 22, blank is 16.
next-opumap\src\api\opulab-gesamt-analyse.ts, code is 24, comment is 14, blank is 3.
next-opumap\src\api\opulab-persoenliche-zielsetzung.ts, code is 35, comment is 7, blank is 3.
next-opumap\src\api\opulab-priorisierung.ts, code is 58, comment is 1, blank is 3.
next-opumap\src\api\opulab-strategie-briefing.ts, code is 40, comment is 8, blank is 3.
next-opumap\src\api\relevante-aspekte-analyse.ts, code is 41, comment is 1, blank is 7.
next-opumap\src\api\schnelle-analyse.ts, code is 158, comment is 55, blank is 19.
next-opumap\src\api\tiefe-analyse.ts, code is 158, comment is 42, blank is 19.
next-opumap\src\api\unternehmensanalyse.ts, code is 56, comment is 20, blank is 6.
next-opumap\src\app\api\analyses\deep\[placeId]\route.ts, code is 59, comment is 9, blank is 12.
next-opumap\src\app\api\analyses\quick\[placeId]\route.ts, code is 54, comment is 9, blank is 11.
next-opumap\src\app\api\analyses\route.ts, code is 86, comment is 7, blank is 12.
next-opumap\src\app\api\auth\callback\route.ts, code is 22, comment is 6, blank is 5.
next-opumap\src\app\api\auth\login\route.ts, code is 60, comment is 17, blank is 12.
next-opumap\src\app\api\auth\register\route.ts, code is 49, comment is 18, blank is 10.
next-opumap\src\app\api\health\route.ts, code is 24, comment is 6, blank is 2.
next-opumap\src\app\api\lab-files\route.ts, code is 95, comment is 14, blank is 5.
next-opumap\src\app\api\opulab-gesamt-analyse\route.ts, code is 77, comment is 10, blank is 11.
next-opumap\src\app\api\opulab-persoenliche-zielsetzung\route.ts, code is 71, comment is 15, blank is 12.
next-opumap\src\app\api\opulab-priorisierung\route.ts, code is 74, comment is 16, blank is 12.
next-opumap\src\app\api\opulab-strategie-briefing\route.ts, code is 74, comment is 15, blank is 12.
next-opumap\src\app\api\profile\route.ts, code is 107, comment is 17, blank is 22.
next-opumap\src\app\api\related-strategies\route.ts, code is 64, comment is 9, blank is 16.
next-opumap\src\app\api\relevante-aspekte-analyse\route.ts, code is 65, comment is 16, blank is 12.
next-opumap\src\app\api\scan-chancen\route.ts, code is 65, comment is 12, blank is 11.
next-opumap\src\app\api\scan-results\route.ts, code is 88, comment is 11, blank is 16.
next-opumap\src\app\api\scan-status\route.ts, code is 106, comment is 23, blank is 17.
next-opumap\src\app\api\schnelle-analyse\[placeId]\route.ts, code is 44, comment is 20, blank is 10.
next-opumap\src\app\api\schnelle-analyse\all\[placeId]\route.ts, code is 46, comment is 11, blank is 7.
next-opumap\src\app\api\schnelle-analyse\route.ts, code is 78, comment is 13, blank is 9.
next-opumap\src\app\api\selected-companies\route.ts, code is 175, comment is 21, blank is 24.
next-opumap\src\app\api\strategies\route.ts, code is 222, comment is 25, blank is 39.
next-opumap\src\app\api\strategy-scan-results\route.ts, code is 90, comment is 9, blank is 14.
next-opumap\src\app\api\tiefe-analyse\[placeId]\route.ts, code is 44, comment is 11, blank is 10.
next-opumap\src\app\api\tiefe-analyse\all\[placeId]\route.ts, code is 48, comment is 10, blank is 7.
next-opumap\src\app\api\tiefe-analyse\route.ts, code is 78, comment is 9, blank is 8.
next-opumap\src\app\auth\auth-code-error\page.tsx, code is 44, comment is 0, blank is 6.
next-opumap\src\app\auth\callback\route.ts, code is 52, comment is 8, blank is 15.
next-opumap\src\app\auth\confirm\page.tsx, code is 93, comment is 21, blank is 15.
next-opumap\src\app\auth\signout\route.ts, code is 12, comment is 3, blank is 6.
next-opumap\src\app\forgot-password\page.tsx, code is 138, comment is 3, blank is 17.
next-opumap\src\app\globals.css, code is 198, comment is 20, blank is 22.
next-opumap\src\app\Homepage\components\AdvancedMarker.tsx, code is 146, comment is 25, blank is 44.
next-opumap\src\app\Homepage\components\AnalysisModal.tsx, code is 67, comment is 4, blank is 4.
next-opumap\src\app\Homepage\components\AnalysisSection.tsx, code is 117, comment is 7, blank is 4.
next-opumap\src\app\Homepage\components\BusinessCard.tsx, code is 124, comment is 8, blank is 4.
next-opumap\src\app\Homepage\components\CustomSwitch.tsx, code is 238, comment is 2, blank is 47.
next-opumap\src\app\Homepage\components\index.ts, code is 5, comment is 0, blank is 0.
next-opumap\src\app\Homepage\components\OpuScannerSection.tsx, code is 78, comment is 3, blank is 5.
next-opumap\src\app\Homepage\hooks\index.ts, code is 4, comment is 0, blank is 0.
next-opumap\src\app\Homepage\hooks\useAnalysis.ts, code is 142, comment is 30, blank is 18.
next-opumap\src\app\Homepage\hooks\useBusinessSelection.ts, code is 282, comment is 42, blank is 41.
next-opumap\src\app\Homepage\hooks\useMapState.ts, code is 92, comment is 22, blank is 21.
next-opumap\src\app\Homepage\hooks\useOpuScannerIntegration.ts, code is 161, comment is 34, blank is 31.
next-opumap\src\app\Homepage\index.tsx, code is 1, comment is 0, blank is 0.
next-opumap\src\app\Homepage\MapComponent.tsx, code is 193, comment is 21, blank is 19.
next-opumap\src\app\Homepage\README.md, code is 22, comment is 0, blank is 10.
next-opumap\src\app\Homepage\styles\analysisStyles.ts, code is 31, comment is 4, blank is 3.
next-opumap\src\app\Homepage\styles\businessCardStyles.ts, code is 130, comment is 11, blank is 3.
next-opumap\src\app\Homepage\styles\index.ts, code is 4, comment is 1, blank is 0.
next-opumap\src\app\Homepage\styles\mapStyles.ts, code is 137, comment is 13, blank is 7.
next-opumap\src\app\Homepage\styles\opuScannerStyles.ts, code is 13, comment is 4, blank is 0.
next-opumap\src\app\Landingpage\index.tsx, code is 1, comment is 0, blank is 0.
next-opumap\src\app\Landingpage\LandingPage.tsx, code is 46, comment is 13, blank is 9.
next-opumap\src\app\Landingpage\Loader.tsx, code is 225, comment is 2, blank is 16.
next-opumap\src\app\Landingpage\README.md, code is 13, comment is 0, blank is 8.
next-opumap\src\app\layout.tsx, code is 61, comment is 16, blank is 3.
next-opumap\src\app\legal\impressum\page.tsx, code is 48, comment is 1, blank is 9.
next-opumap\src\app\login\components\GoogleSignInButton.tsx, code is 63, comment is 5, blank is 9.
next-opumap\src\app\login\index.tsx, code is 1, comment is 0, blank is 0.
next-opumap\src\app\login\Login.tsx, code is 219, comment is 20, blank is 19.
next-opumap\src\app\login\page.tsx, code is 4, comment is 1, blank is 1.
next-opumap\src\app\login\README.md, code is 21, comment is 0, blank is 10.
next-opumap\src\app\opulab\components\index.ts, code is 7, comment is 2, blank is 1.
next-opumap\src\app\opulab\components\InfoIconWithPopup.tsx, code is 46, comment is 0, blank is 4.
next-opumap\src\app\opulab\components\RelevantAspects.tsx, code is 57, comment is 1, blank is 6.
next-opumap\src\app\opulab\components\SaveSettingsSection.tsx, code is 136, comment is 2, blank is 15.
next-opumap\src\app\opulab\components\StrategieAnalyse.tsx, code is 291, comment is 12, blank is 23.
next-opumap\src\app\opulab\components\StrategieBriefing.tsx, code is 64, comment is 1, blank is 5.
next-opumap\src\app\opulab\components\StrategieInfo.tsx, code is 116, comment is 1, blank is 6.
next-opumap\src\app\opulab\components\StrategieList.tsx, code is 111, comment is 2, blank is 6.
next-opumap\src\app\opulab\components\ui\AnalysisStatusIndicator.tsx, code is 47, comment is 4, blank is 7.
next-opumap\src\app\opulab\components\ui\Checkbox.tsx, code is 379, comment is 8, blank is 41.
next-opumap\src\app\opulab\components\ui\index.ts, code is 4, comment is 0, blank is 0.
next-opumap\src\app\opulab\components\ui\StartLaborButton.tsx, code is 181, comment is 0, blank is 20.
next-opumap\src\app\opulab\components\ui\TouchStartLaborButton.tsx, code is 194, comment is 0, blank is 21.
next-opumap\src\app\opulab\hooks\index.ts, code is 1, comment is 0, blank is 0.
next-opumap\src\app\opulab\hooks\opulabApi.ts, code is 104, comment is 18, blank is 18.
next-opumap\src\app\opulab\hooks\useOpulabStrategie.ts, code is 338, comment is 95, blank is 55.
next-opumap\src\app\opulab\hooks\useOpulabStrategieData.ts, code is 199, comment is 41, blank is 30.
next-opumap\src\app\opulab\hooks\useOpulabUI.ts, code is 53, comment is 10, blank is 7.
next-opumap\src\app\opulab\hooks\useStrategies.ts, code is 152, comment is 35, blank is 26.
next-opumap\src\app\opulab\index.tsx, code is 1, comment is 0, blank is 0.
next-opumap\src\app\opulab\OpulabStrategiePage.tsx, code is 294, comment is 34, blank is 19.
next-opumap\src\app\opulab\page.tsx, code is 33, comment is 6, blank is 8.
next-opumap\src\app\opulab\README.md, code is 34, comment is 0, blank is 13.
next-opumap\src\app\opulab\strategyService.ts, code is 140, comment is 9, blank is 23.
next-opumap\src\app\opulab\styles\index.ts, code is 1, comment is 0, blank is 0.
next-opumap\src\app\opulab\styles\opulabStyles.ts, code is 76, comment is 12, blank is 11.
next-opumap\src\app\opuscanner\apiService.ts, code is 300, comment is 44, blank is 56.
next-opumap\src\app\opuscanner\components\CompanyInfoEditor.tsx, code is 57, comment is 2, blank is 5.
next-opumap\src\app\opuscanner\components\DeleteButton.tsx, code is 76, comment is 0, blank is 13.
next-opumap\src\app\opuscanner\components\FooterScan.tsx, code is 72, comment is 13, blank is 5.
next-opumap\src\app\opuscanner\components\GlitchButton.tsx, code is 310, comment is 14, blank is 42.
next-opumap\src\app\opuscanner\components\index.ts, code is 7, comment is 0, blank is 0.
next-opumap\src\app\opuscanner\components\RecognizedChancesSection.tsx, code is 69, comment is 5, blank is 4.
next-opumap\src\app\opuscanner\components\ScanButton.tsx, code is 202, comment is 19, blank is 13.
next-opumap\src\app\opuscanner\components\ScanResultModal.tsx, code is 80, comment is 7, blank is 9.
next-opumap\src\app\opuscanner\components\SelectedCompaniesList.tsx, code is 35, comment is 0, blank is 3.
next-opumap\src\app\opuscanner\components\StrategyForm.tsx, code is 66, comment is 0, blank is 6.
next-opumap\src\app\opuscanner\components\StrategySelection.tsx, code is 152, comment is 3, blank is 5.
next-opumap\src\app\opuscanner\components\ui\ScannerStatusIndicator.tsx, code is 48, comment is 4, blank is 8.
next-opumap\src\app\opuscanner\hooks\useCompanyInfo.ts, code is 81, comment is 24, blank is 12.
next-opumap\src\app\opuscanner\hooks\useScanProcess.ts, code is 220, comment is 7, blank is 19.
next-opumap\src\app\opuscanner\hooks\useSelectedCompanies.ts, code is 86, comment is 11, blank is 14.
next-opumap\src\app\opuscanner\hooks\useStrategies.ts, code is 80, comment is 9, blank is 9.
next-opumap\src\app\opuscanner\page.tsx, code is 249, comment is 36, blank is 31.
next-opumap\src\app\opuscanner\README.md, code is 24, comment is 0, blank is 10.
next-opumap\src\app\opuscanner\types.ts, code is 41, comment is 16, blank is 7.
next-opumap\src\app\opuscanner\utils\supabaseClient.ts, code is 0, comment is 0, blank is 0.
next-opumap\src\app\opuscanner\utils\supabaseHelpers.ts, code is 0, comment is 0, blank is 0.
next-opumap\src\app\page.tsx, code is 28, comment is 12, blank is 7.
next-opumap\src\app\profile\actions\update-profile.ts, code is 44, comment is 6, blank is 13.
next-opumap\src\app\profile\index.tsx, code is 1, comment is 0, blank is 0.
next-opumap\src\app\profile\NotificationState.ts, code is 16, comment is 0, blank is 2.
next-opumap\src\app\profile\page.tsx, code is 10, comment is 1, blank is 2.
next-opumap\src\app\profile\Profile.tsx, code is 261, comment is 26, blank is 24.
next-opumap\src\app\profile\README.md, code is 31, comment is 0, blank is 12.
next-opumap\src\app\reset-password\page.tsx, code is 443, comment is 45, blank is 72.
next-opumap\src\app\styles\transitions.module.css, code is 65, comment is 6, blank is 9.
next-opumap\src\components\auth\AuthStatus.tsx, code is 45, comment is 2, blank is 6.
next-opumap\src\components\auth\LogoutButton.tsx, code is 38, comment is 1, blank is 5.
next-opumap\src\components\auth\ProtectedRoute.tsx, code is 35, comment is 6, blank is 7.
next-opumap\src\components\Footer.tsx, code is 120, comment is 0, blank is 9.
next-opumap\src\components\MarkdownEditor.tsx, code is 26, comment is 4, blank is 3.
next-opumap\src\components\MorphTransition.tsx, code is 98, comment is 14, blank is 16.
next-opumap\src\components\Navbar.tsx, code is 166, comment is 22, blank is 16.
next-opumap\src\components\PageTransitionContainer.tsx, code is 22, comment is 6, blank is 6.
next-opumap\src\components\PageTransitionWrapper.tsx, code is 78, comment is 16, blank is 20.
next-opumap\src\components\SessionExpiredModal.tsx, code is 82, comment is 9, blank is 12.
next-opumap\src\components\ui\avatar.tsx, code is 45, comment is 0, blank is 4.
next-opumap\src\components\ui\badge.tsx, code is 31, comment is 1, blank is 5.
next-opumap\src\components\ui\button.tsx, code is 36, comment is 20, blank is 7.
next-opumap\src\components\ui\card.tsx, code is 71, comment is 0, blank is 10.
next-opumap\src\components\ui\checkbox.tsx, code is 25, comment is 0, blank is 5.
next-opumap\src\components\ui\client-only.tsx, code is 51, comment is 14, blank is 15.
next-opumap\src\components\ui\client-only\ClientOnly.tsx, code is 16, comment is 6, blank is 6.
next-opumap\src\components\ui\client-only\ClientOnlyIcon.tsx, code is 31, comment is 6, blank is 4.
next-opumap\src\components\ui\client-only\ClientOnlyImage.tsx, code is 26, comment is 6, blank is 5.
next-opumap\src\components\ui\client-only\ClientOnlySVG.tsx, code is 18, comment is 5, blank is 3.
next-opumap\src\components\ui\client-only\index.ts, code is 4, comment is 0, blank is 0.
next-opumap\src\components\ui\DarkModeToggle.tsx, code is 24, comment is 0, blank is 5.
next-opumap\src\components\ui\dialog.tsx, code is 109, comment is 0, blank is 9.
next-opumap\src\components\ui\input.tsx, code is 20, comment is 0, blank is 6.
next-opumap\src\components\ui\label.tsx, code is 18, comment is 0, blank is 5.
next-opumap\src\components\ui\Loader.tsx, code is 123, comment is 32, blank is 13.
next-opumap\src\components\ui\notification.tsx, code is 39, comment is 0, blank is 4.
next-opumap\src\components\ui\progress.tsx, code is 26, comment is 0, blank is 5.
next-opumap\src\components\ui\radio-group.tsx, code is 38, comment is 0, blank is 4.
next-opumap\src\components\ui\scroll-area.tsx, code is 52, comment is 0, blank is 6.
next-opumap\src\components\ui\sheet.tsx, code is 128, comment is 0, blank is 14.
next-opumap\src\components\ui\slider.tsx, code is 27, comment is 0, blank is 5.
next-opumap\src\components\ui\switch.tsx, code is 23, comment is 0, blank is 6.
next-opumap\src\components\ui\tabs.tsx, code is 58, comment is 0, blank is 8.
next-opumap\src\components\ui\textarea.tsx, code is 19, comment is 0, blank is 6.
next-opumap\src\components\ui\tooltip.tsx, code is 22, comment is 0, blank is 6.
next-opumap\src\contexts\AuthContext.tsx, code is 357, comment is 71, blank is 52.
next-opumap\src\lib\apiClient.ts, code is 37, comment is 18, blank is 9.
next-opumap\src\lib\auth.ts, code is 75, comment is 31, blank is 15.
next-opumap\src\lib\db-utils.ts, code is 16, comment is 10, blank is 4.
next-opumap\src\lib\db.ts, code is 61, comment is 22, blank is 12.
next-opumap\src\lib\opulabAIClient.ts, code is 9, comment is 0, blank is 2.
next-opumap\src\lib\registry.tsx, code is 17, comment is 2, blank is 6.
next-opumap\src\lib\supabaseAuth.ts, code is 66, comment is 23, blank is 18.
next-opumap\src\lib\supabaseClient.ts, code is 25, comment is 11, blank is 6.
next-opumap\src\lib\utils.ts, code is 5, comment is 0, blank is 1.
next-opumap\src\styles\iconPaths.ts, code is 15, comment is 1, blank is 0.
next-opumap\src\styles\index.ts, code is 1, comment is 0, blank is 0.
next-opumap\src\styles\README.md, code is 8, comment is 0, blank is 5.
next-opumap\src\types\index.ts, code is 53, comment is 19, blank is 7.
next-opumap\src\types\supabase.ts, code is 104, comment is 0, blank is 1.
next-opumap\src\utils\formatters.ts, code is 62, comment is 13, blank is 4.
next-opumap\src\utils\index.ts, code is 1, comment is 0, blank is 0.
next-opumap\src\utils\README.md, code is 7, comment is 0, blank is 5.
next-opumap\src\utils\supabase\client.ts, code is 8, comment is 0, blank is 2.
next-opumap\src\utils\supabase\middleware.ts, code is 61, comment is 19, blank is 12.
next-opumap\src\utils\supabase\server.ts, code is 24, comment is 3, blank is 2.
next-opumap\src\utils\userIdMapping.ts, code is 3, comment is 9, blank is 1.
next-opumap\tailwind.config.js, code is 14, comment is 1, blank is 1.
next-opumap\tsconfig.json, code is 50, comment is 3, blank is 2.
README.md, code is 79, comment is 5, blank is 8.
security_checklist.md, code is 41, comment is 1, blank is 8.
start_terminals.py, code is 61, comment is 25, blank is 14.
TechstackCanvas.md, code is 220, comment is 7, blank is 38.
===============================================================================
