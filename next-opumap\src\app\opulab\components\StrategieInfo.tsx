'use client';

import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import MarkdownEditor from '@/components/MarkdownEditor';

// Import styles
import { formStyles, cardStyles, spacingStyles } from '../styles';

interface StrategieInfoProps {
  companyInfo: string;
  personalGoal: string;
  onCompanyInfoChange: (value: string) => void;
  onPersonalGoalChange: (value: string) => void;
  onSave: () => void;
  showCompanyInfo?: boolean;
  showPersonalGoal?: boolean;
  isSaving?: boolean;
  message?: string;
  disabled?: boolean;
}

const StrategieInfo: React.FC<StrategieInfoProps> = ({
  companyInfo,
  personalGoal,
  onCompanyInfoChange,
  onPersonalGoalChange,
  onSave,
  showCompanyInfo = true,
  showPersonalGoal = true,
  isSaving = false,
  message = '',
  disabled = false
}) => {
  return (
    <div className={`grid grid-cols-1 ${showCompanyInfo && showPersonalGoal ? 'md:grid-cols-2' : 'md:grid-cols-1'} ${spacingStyles.cardGap}`}>
      {/* Unternehmensinformationen Card */}
      {showCompanyInfo && (
        <div className={`min-h-[350px]`}>
          <CardHeader className={cardStyles.header}>
            <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>
              <span className="inline-block">Unternehmensinformationen</span>
              <span className="inline-block">/</span>
              <span className="inline-block">Unternehmensanalyse</span>
            </CardTitle>
            <p className={cardStyles.subtitle}>Beschreiben Sie Ihr Unternehmen und aktuelle Herausforderungen</p>
          </CardHeader>
          <CardContent className={`${spacingStyles.betweenItems} ${cardStyles.content}`}>
            <label htmlFor="company-info" className={formStyles.label}>
              Wichtige Informationen zu Ihrem Unternehmen
            </label>
            <div className="min-h-[250px]">
              <MarkdownEditor
                value={companyInfo}
                onChange={(value) => onCompanyInfoChange(value || '')}
                height={250}
                preview="edit"
              />
            </div>
            <p className={formStyles.helperText}>
              Diese Informationen helfen bei der Erstellung einer maßgeschneiderten Strategie für Ihr Unternehmen.
            </p>
            {message && (
              <p className={`text-sm ${message.includes('Fehler') ? 'text-destructive' : 'text-green-600 dark:text-green-500'}`}>
                {message}
              </p>
            )}
          </CardContent>
          {!showPersonalGoal && (
            <CardFooter className={cardStyles.footer}>
              <Button
                size="sm"
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
                onClick={onSave}
                disabled={isSaving || disabled}
              >
                {isSaving ? 'Speichern...' : 'Speichern'}
              </Button>
            </CardFooter>
          )}
        </div>
      )}

      {/* Persönliche Zielsetzung & Kontext Card */}
      {showPersonalGoal && (
        <div className="h-full flex flex-col">
          <CardHeader className={cardStyles.header}>
            <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>
              <span className="inline-block">Persönliche Zielsetzung</span>
              <span className="inline-block">&nbsp;&</span>
              <span className="inline-block">Kontext</span>
            </CardTitle>
            <p className={cardStyles.subtitle}>Definieren Sie Ihre individuellen Ziele <span className="text-muted-foreground">(optional)</span></p>
          </CardHeader>
          <CardContent className={`${spacingStyles.betweenItems} flex-grow flex flex-col ${cardStyles.content}`}>
            <label htmlFor="goal" className={formStyles.label}>
              Was möchtest du mit dieser Strategie erreichen?
            </label>
            <div className="flex-grow min-h-[200px]">
              <MarkdownEditor
                value={personalGoal}
                onChange={(value) => onPersonalGoalChange(value || '')}
                height={200}
                preview="edit"
              />
            </div>
            <p className={formStyles.helperText}>
              KI-Unterstützung: Individuelle und zielgerichtete Vorschläge basierend auf Ihren spezifischen Zielen.
            </p>
          </CardContent>
        </div>
      )}
    </div>
  );
};

export default StrategieInfo;
