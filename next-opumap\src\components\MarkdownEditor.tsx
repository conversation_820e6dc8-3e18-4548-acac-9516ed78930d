'use client';
import React from 'react';
import '@uiw/react-md-editor/markdown-editor.css';
import '@uiw/react-markdown-preview/markdown.css';
import MdEditor from '@uiw/react-md-editor';
import { useTheme } from 'next-themes';  // Corrected import based on codebase search

interface MarkdownEditorProps {
  value: string;
  onChange: (value?: string) => void;
  height?: number;
  preview?: 'edit' | 'preview' | 'live';
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({ value, onChange, height = 400, preview = 'edit' }) => {
  const { theme } = useTheme();  // Get the current theme from next-themes
  const editorClass = theme === 'dark' ? 'dark-theme' : 'light-theme';  // Dynamically set a class for styling
  return (
    <MdEditor
      value={value}
      onChange={onChange}
      height={height}
      preview={preview}
      className={editorClass}  // Apply class for theme-based styling; ensure corresponding CSS is defined
    />
  );
};

export default MarkdownEditor;
