export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          name: string | null
          company_name: string | null
          address: string | null
          phone: string | null
          website: string | null
          employee_count: string | null
          company_info_points: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          name?: string | null
          company_name?: string | null
          address?: string | null
          phone?: string | null
          website?: string | null
          employee_count?: string | null
          company_info_points?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string | null
          company_name?: string | null
          address?: string | null
          phone?: string | null
          website?: string | null
          employee_count?: string | null
          company_info_points?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      selected_companies: {
        Row: {
          id: string
          place_id: string
          company_name: string | null
          address: string | null
          is_deleted: boolean
          deleted_at: string | null
          created_at: string
          user_id: string
          name: string | null
          updated_at: string
          company_id: string | null
        }
        Insert: {
          id?: string
          place_id: string
          company_name?: string | null
          address?: string | null
          is_deleted?: boolean
          deleted_at?: string | null
          created_at?: string
          user_id: string
          name?: string | null
          updated_at?: string
          company_id?: string | null
        }
        Update: {
          id?: string
          place_id?: string
          company_name?: string | null
          address?: string | null
          is_deleted?: boolean
          deleted_at?: string | null
          created_at?: string
          user_id?: string
          name?: string | null
          updated_at?: string
          company_id?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
