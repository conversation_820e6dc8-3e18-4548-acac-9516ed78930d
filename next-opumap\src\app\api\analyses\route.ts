import { NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { verifySupabaseAuth, createAuthResponse } from '@/lib/supabaseAuth';

// Define the request payload type
interface RequestPayload {
    businessData: {
        placeId: string;
        name: string;
        address: string;
        phone?: string;
        website?: string;
        lat?: number;
        lng?: number;
    };
    analysisContent: string;
    analysisDate: string;
    type: 'quick' | 'deep';
}

// POST /api/analyses - Save or update analysis
export async function POST(request: NextRequest) {
    try {
        // Verify authentication using Supabase Auth
        const authResult = await verifySupabaseAuth();
        if (!authResult || !authResult.user) {
            return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
        }
        

        const payload: RequestPayload = await request.json();
        const { businessData, analysisContent, analysisDate, type } = payload;
        const { placeId, name, address, phone, website, lat, lng } = businessData;

        // Validate required fields
        if (!placeId || !analysisContent || !analysisDate || !type) {
            return createAuthResponse({ 
                error: 'Place ID, Analyseinhalt, Analysedatum und Typ sind erforderlich.' 
            }, 400);
        }

        // Create a Supabase client
        const supabase = await createClient();

        // First, ensure the company exists in the companies table
        const { data: companyData, error: companyError } = await supabase
            .from('companies')
.upsert({
                place_id: placeId,
                name: name,
                address: address,
                phone: phone || null,
                website: website || null,
                lat: lat || null,
                lng: lng || null,
                is_deleted: false
            }, { onConflict: 'place_id' })
            .select()
            .single();

        if (companyError) {
            console.error('Fehler beim Speichern des Unternehmens:', companyError);
            return createAuthResponse({ 
                error: 'Fehler beim Speichern des Unternehmens', 
                details: companyError.message 
            }, 500);
        }

        // Now save the analysis
        const { data: analysisData, error: analysisError } = await supabase
            .from('analyses')
            .insert({
                company_id: companyData.id,
                type: type,
                content: analysisContent,
                analysis_date: analysisDate
            })
            .select()
            .single();

        if (analysisError) {
            console.error('Fehler beim Speichern der Analyse:', analysisError);
            return createAuthResponse({ 
                error: 'Fehler beim Speichern der Analyse', 
                details: analysisError.message 
            }, 500);
        }

        return createAuthResponse({
            message: 'Analyse erfolgreich gespeichert.',
            data: analysisData
        }, 201);

    } catch (error: unknown) {
        console.error('Fehler beim Speichern der Analyse:', error);
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        if (error instanceof SyntaxError) {
            return createAuthResponse({ error: 'Ungültige Anfrage-Daten.' }, 400);
        }
        return createAuthResponse({
            error: 'Serverfehler beim Speichern der Analyse',
            details: message
        }, 500);
    }
}
