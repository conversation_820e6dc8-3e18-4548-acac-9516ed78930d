/**
 * Next.js Development Environment Optimizer
 * 
 * This script helps optimize the Next.js development environment by:
 * 1. Clearing unnecessary cache files
 * 2. Setting optimal environment variables
 * 3. Providing performance recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(`${colors.bright}${colors.blue}=== Next.js Development Environment Optimizer ===${colors.reset}\n`);

// Get system information
const totalMemoryGB = Math.round(os.totalmem() / (1024 * 1024 * 1024));
const freeMemoryGB = Math.round(os.freemem() / (1024 * 1024 * 1024));
const cpuCount = os.cpus().length;

console.log(`${colors.cyan}System Information:${colors.reset}`);
console.log(`- CPU Cores: ${cpuCount}`);
console.log(`- Total Memory: ${totalMemoryGB}GB`);
console.log(`- Free Memory: ${freeMemoryGB}GB`);
console.log(`- Platform: ${os.platform()} ${os.release()}\n`);

// Clean up Next.js cache
console.log(`${colors.cyan}Cleaning up Next.js cache...${colors.reset}`);

const nextCacheDir = path.join(__dirname, '.next');
const nodeModulesCacheDir = path.join(__dirname, 'node_modules', '.cache');

try {
  if (fs.existsSync(nextCacheDir)) {
    console.log(`- Removing ${nextCacheDir}`);
    fs.rmSync(nextCacheDir, { recursive: true, force: true });
  } else {
    console.log(`- ${nextCacheDir} does not exist, skipping`);
  }
} catch (error) {
  console.error(`${colors.red}Error removing .next directory:${colors.reset}`, error.message);
}

try {
  if (fs.existsSync(nodeModulesCacheDir)) {
    console.log(`- Removing ${nodeModulesCacheDir}`);
    fs.rmSync(nodeModulesCacheDir, { recursive: true, force: true });
  } else {
    console.log(`- ${nodeModulesCacheDir} does not exist, skipping`);
  }
} catch (error) {
  console.error(`${colors.red}Error removing node_modules/.cache directory:${colors.reset}`, error.message);
}

// Determine optimal Node.js memory limit based on system memory
let recommendedMemoryLimit = 4096; // Default 4GB

if (totalMemoryGB >= 16) {
  recommendedMemoryLimit = 8192; // 8GB for systems with 16GB+ RAM
} else if (totalMemoryGB >= 8) {
  recommendedMemoryLimit = 4096; // 4GB for systems with 8GB+ RAM
} else {
  recommendedMemoryLimit = 2048; // 2GB for systems with less RAM
}

console.log(`\n${colors.cyan}Recommended Node.js settings:${colors.reset}`);
console.log(`- Memory Limit: ${recommendedMemoryLimit}MB`);
console.log(`- Set NODE_OPTIONS="--max-old-space-size=${recommendedMemoryLimit}"`);

// Create a .env.local file with optimized settings if it doesn't exist
const envLocalPath = path.join(__dirname, '.env.local');
let envLocalExists = fs.existsSync(envLocalPath);

console.log(`\n${colors.cyan}Environment configuration:${colors.reset}`);

if (!envLocalExists) {
  console.log(`- Creating .env.local file with optimized settings`);
  const envContent = `# Optimized Next.js settings
# Added by optimize-dev.js

# Node.js memory settings
NODE_OPTIONS=--max-old-space-size=${recommendedMemoryLimit}

# Next.js build optimizations
NEXT_TELEMETRY_DISABLED=1
`;

  fs.writeFileSync(envLocalPath, envContent);
  console.log(`${colors.green}- Created .env.local with optimized settings${colors.reset}`);
} else {
  console.log(`- .env.local already exists, checking for optimization settings`);
  
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  let updated = false;
  let newContent = envContent;
  
  if (!envContent.includes('NODE_OPTIONS=')) {
    newContent += `\n# Node.js memory settings\nNODE_OPTIONS=--max-old-space-size=${recommendedMemoryLimit}\n`;
    updated = true;
  }
  
  if (!envContent.includes('NEXT_TELEMETRY_DISABLED=')) {
    newContent += `\n# Next.js build optimizations\nNEXT_TELEMETRY_DISABLED=1\n`;
    updated = true;
  }
  
  if (updated) {
    fs.writeFileSync(envLocalPath, newContent);
    console.log(`${colors.green}- Updated .env.local with optimized settings${colors.reset}`);
  } else {
    console.log(`- .env.local already has optimization settings`);
  }
}

// Performance recommendations
console.log(`\n${colors.cyan}Performance recommendations:${colors.reset}`);
console.log(`1. ${colors.yellow}Use "npm run dev" for normal development${colors.reset}`);
console.log(`2. ${colors.yellow}Run this optimizer script periodically to clean cache${colors.reset}`);
console.log(`3. ${colors.yellow}Consider using a production build for testing large changes${colors.reset}`);

if (freeMemoryGB < 2) {
  console.log(`\n${colors.red}Warning: Your system has less than 2GB of free memory.${colors.reset}`);
  console.log(`${colors.red}This may cause performance issues during development.${colors.reset}`);
  console.log(`${colors.red}Consider closing other applications to free up memory.${colors.reset}`);
}

console.log(`\n${colors.bright}${colors.green}Optimization complete!${colors.reset}`);
console.log(`${colors.bright}${colors.green}You can now start your development server with "npm run dev"${colors.reset}`);
