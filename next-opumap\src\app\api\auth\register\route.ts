import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
// import db from '@/lib/db'; // Placeholder for actual DB functions
import { UserProfile } from '@/types'; // Import shared User type

// Placeholder for database functions - replace with actual imports later
const db = {
    findUserByEmail: async (email: string): Promise<UserProfile | null> => {
        console.log(`Database: Checking for user with email ${email}`);
        // Simulate finding user
        if (email === "<EMAIL>") {
            return { id: "existing-user-id", name: "Existing User", email: email };
        }
        return null;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    addUser: async (name: string, email: string, passwordHash: string): Promise<UserProfile> => {
        console.log(`Database: Adding user ${name} (${email})`);
        // Simulate adding user and returning the new user object (without password hash)
        const newUser: UserProfile = { id: `new-user-${Date.now()}`, name, email };
        return newUser;
    }
};

const saltRounds = 10; // Cost factor for bcrypt hashing

// POST /api/auth/register - Handle user registration
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { name, email, password } = body;

        // Validate input
        if (!name || !email || !password) {
            return NextResponse.json({ error: "Name, E-Mail und Passwort sind erforderlich." }, { status: 400 });
        }

        // Check if user already exists
        const existingUser = await db.findUserByEmail(email);
        if (existingUser) {
            return NextResponse.json({ error: "Benutzer mit dieser E-Mail existiert bereits." }, { status: 400 });
        }

        // Hash password
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Add user to database (passwordHash is used internally by db.addUser)
        const newUser = await db.addUser(name, email, passwordHash);

        // Return success response (don't return password hash)
        // Ensure the returned user object matches what the frontend expects (e.g., from AuthContext)
        const responseUser = {
             id: newUser.id,
             name: newUser.name,
             email: newUser.email
             // Include other non-sensitive fields if needed
        };

        return NextResponse.json(responseUser, { status: 201 }); // 201 Created

    } catch (error) { // Use unknown type for error
        console.error('Fehler bei der Registrierung:', error);
         if (error instanceof SyntaxError) {
            return NextResponse.json({ error: 'Ungültige Anfrage-Daten.' }, { status: 400 });
        }
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({
            error: 'Interner Serverfehler bei der Registrierung.',
            details: message // Optionally include details in dev mode
        }, { status: 500 });
    }
}
