import React from "react";
import { BusinessData } from "@/types";
import {
  analysisCardStyles,
  analysisLayoutStyles,
  analysisAdditionalStyles
} from "../styles";
import { iconPaths } from "@/styles";
import { formatAnalysisDate } from "@/utils";

interface AnalysisSectionProps {
  selectedBusiness: BusinessData | null;
  quickAnalysisData: string | null;
  quickAnalysisDate: string | null;
  isQuickAnalysisLoading: boolean;
  deepAnalysisData: string | null;
  deepAnalysisDate: string | null;
  isDeepAnalysisLoading: boolean;
  handleCreateQuickAnalysis: (business: BusinessData) => void;
  handleViewQuickAnalysis: () => void;
  handleCreateDeepAnalysis: (business: BusinessData) => void;
  handleViewDeepAnalysis: () => void;
}

/**
 * Analysis section component with quick and deep analysis cards
 */
const AnalysisSection: React.FC<AnalysisSectionProps> = ({
  selectedBusiness,
  quickAnalysisData,
  quickAnalysisDate,
  isQuickAnalysisLoading,
  deepAnalysisData,
  deepAnalysisDate,
  isDeepAnalysisLoading,
  handleCreateQuickAnalysis,
  handleViewQuickAnalysis,
  handleCreateDeepAnalysis,
  handleViewDeepAnalysis
}) => {
  return (
    <div className={analysisLayoutStyles.analysisCardsWrapper} id="analysis-cards">
      {/* Quick Analysis Card */}
      <div className={analysisCardStyles.container}>
        <div className={analysisCardStyles.header}>
          <h2 className={analysisCardStyles.title}>Schnelle Analyse</h2>
          {/* Button to create new quick analysis */}
          <button
            onClick={() => selectedBusiness && handleCreateQuickAnalysis(selectedBusiness)}
            disabled={!selectedBusiness || isQuickAnalysisLoading} // Disable if no business or loading
            className={analysisCardStyles.newButton(!selectedBusiness || isQuickAnalysisLoading)}
            title="Neue Schnellanalyse erstellen"
          >
            {/* Show loading indicator or icon */}
            <span>{isQuickAnalysisLoading ? "..." : "Neu"}</span>
            {!isQuickAnalysisLoading && (
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d={iconPaths.refresh.path1}></path>
                <path d={iconPaths.refresh.path2}></path>
                <path d={iconPaths.refresh.path3}></path>
                <path d={iconPaths.refresh.path4}></path>
              </svg>
            )}
          </button>
        </div>
        <div className={analysisCardStyles.dateSection}>
          <p className={analysisCardStyles.dateLabel}>Daten vom:</p>
          <p className={analysisCardStyles.dateValue}>
            {quickAnalysisDate ? formatAnalysisDate(quickAnalysisDate) : "Keine Daten"}
          </p>
        </div>
        {/* Button to view existing quick analysis */}
        <button
          onClick={handleViewQuickAnalysis}
          disabled={!quickAnalysisData || isQuickAnalysisLoading} // Disable if no data or loading
          className={analysisCardStyles.viewButton}
        >
          Ansehen <span className={analysisAdditionalStyles.arrowSpan}>→</span>
        </button>
      </div>

      {/* Deep Analysis Card */}
      <div className={analysisCardStyles.container}>
        <div className={analysisCardStyles.header}>
          <h2 className={analysisCardStyles.title}>Tiefe Analyse</h2>
          {/* Button to create new deep analysis */}
          <button
            onClick={() => selectedBusiness && handleCreateDeepAnalysis(selectedBusiness)}
            disabled={!selectedBusiness || isDeepAnalysisLoading} // Disable if no business or loading
            className={analysisCardStyles.newButton(!selectedBusiness || isDeepAnalysisLoading)}
            title="Neue Tiefenanalyse erstellen"
          >
            {/* Show loading indicator or icon */}
            <span>{isDeepAnalysisLoading ? "..." : "Neu"}</span>
            {!isDeepAnalysisLoading && (
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d={iconPaths.refresh.path1}></path>
                <path d={iconPaths.refresh.path2}></path>
                <path d={iconPaths.refresh.path3}></path>
                <path d={iconPaths.refresh.path4}></path>
              </svg>
            )}
          </button>
        </div>
        <div className={analysisCardStyles.dateSection}>
          <p className={analysisCardStyles.dateLabel}>Daten vom:</p>
          <p className={analysisCardStyles.dateValue}>
            {deepAnalysisDate ? formatAnalysisDate(deepAnalysisDate) : "Keine Daten"}
          </p>
        </div>
        {/* Button to view existing deep analysis */}
        <button
          onClick={handleViewDeepAnalysis}
          disabled={!deepAnalysisData || isDeepAnalysisLoading} // Disable if no data or loading
          className={analysisCardStyles.viewButton}
        >
          Ansehen <span className={analysisAdditionalStyles.arrowSpan}>→</span>
        </button>
      </div>
    </div>
  );
};

export default AnalysisSection;
