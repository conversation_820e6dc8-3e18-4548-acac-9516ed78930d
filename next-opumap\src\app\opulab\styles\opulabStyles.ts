// Styles für die Opulab-Komponenten

// Hauptcontainer-Styles
export const containerStyles = {
  wrapper: 'max-w-screen-xl mx-auto px-6 py-4 md:py-8 bg-background text-foreground',
  content: 'flex flex-col lg:flex-row gap-8',
  mainContent: 'flex-1 space-y-6',
  sidebar: 'w-full lg:w-1/3 xl:w-1/4 space-y-6',
  columnContainer: 'grid grid-cols-1 lg:grid-cols-[30%,50%,20%] gap-y-8 gap-x-6',
  column: 'space-y-6',
};

// Header-Styles
export const headerStyles = {
  container: 'text-center mb-8',
  title: 'text-2xl md:text-3xl font-bold text-primary flex flex-col md:flex-row items-center justify-center md:justify-start',
  titleSpan: 'opacity-75 mb-1 md:mb-0 md:mr-2',
  subtitle: 'text-xl md:text-2xl font-semibold mt-3 text-foreground',
  icon: 'ml-2 h-8 w-auto',
  subtitleContainer: 'flex items-center justify-center mt-1 md:mt-0',
};

// Card-Styles für einheitliches Aussehen
export const cardStyles = {
  default: 'bg-card border border-border rounded-lg shadow-md dark:shadow-none',
  elevated: 'bg-card border border-border rounded-lg shadow-lg dark:shadow-md',
  contrast: 'bg-gray-50 dark:bg-gray-800/50 border border-border rounded-lg shadow-md dark:shadow-none',
  header: 'px-8 py-5 border-b border-border',
  title: 'text-xl font-semibold mb-2',
  subtitle: 'text-sm text-muted-foreground',
  content: 'px-8 py-6',
  footer: 'px-8 py-5 border-t border-border bg-muted/30',
};

// Spacing-Utilities für konsistente Abstände
export const spacingStyles = {
  section: 'mb-8',
  betweenItems: 'space-y-6',
  betweenSections: 'space-y-10',
  cardPadding: 'px-8 py-6',
  cardGap: 'gap-8',
};

// Button-Styles
export const buttonStyles = {
  primary: 'bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-md transition-colors',
  secondary: 'bg-secondary hover:bg-secondary/90 text-secondary-foreground font-medium rounded-md transition-colors',
  outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md transition-colors',
  ghost: 'hover:bg-accent hover:text-accent-foreground rounded-md transition-colors',
  kiStrategie: 'flex justify-center items-center',
  icon: 'ml-2 h-4 w-4',
};

// Form-Styles für einheitliche Eingabefelder
export const formStyles = {
  label: 'block text-sm font-medium mb-1.5 text-foreground',
  helperText: 'text-xs text-muted-foreground mt-1.5',
  textarea: 'min-h-[120px] resize-none placeholder:text-muted-foreground/50 placeholder:italic',
};

// Strategie-Info-Styles
export const strategieInfoStyles = {
  grid: 'grid grid-cols-1 md:grid-cols-2 gap-6',
  container: 'flex flex-col h-full',
};

// Analyse-Styles
export const analyseStyles = {
  graph: 'w-full h-40 border border-dashed rounded-md flex items-center justify-center text-muted-foreground bg-muted',
  result: 'font-semibold text-lg p-2 border rounded-md inline-block min-w-[100px] ml-2 border-border',
  container: 'flex flex-col items-center justify-center text-center',
};

// Strategie-List-Styles
export const strategieListStyles = {
  container: 'w-full',
  list: 'flex flex-wrap justify-center gap-2 mb-2',
  item: (isSelected: boolean) => `p-2 rounded-md cursor-pointer hover:bg-muted text-center w-full ${
    isSelected ? 'bg-primary/10 border border-primary' : ''
  }`,
  itemText: (isSelected: boolean) => isSelected ? 'font-medium text-primary' : '',
  buttonContainer: 'flex flex-wrap justify-center gap-2 mt-2',
  button: 'w-full',
};

// Relevant-Aspects-Styles
export const relevantAspectsStyles = {
  container: 'w-full',
  checkbox: 'h-4 w-4 rounded-sm border border-primary text-primary focus:ring-primary',
  label: 'text-sm font-medium leading-none',
  item: 'flex items-center space-x-3 py-2',
};

// Collapsible-Panel-Styles
export const collapsibleStyles = {
  trigger: 'flex w-full items-center justify-between px-4 py-2 font-medium transition-all hover:bg-muted/50 [&[data-state=open]>svg]:rotate-180',
  content: 'overflow-hidden data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down',
  icon: 'h-4 w-4 shrink-0 transition-transform duration-200',
};
