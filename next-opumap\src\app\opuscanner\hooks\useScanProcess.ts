import { useState, useCallback } from 'react';
import { ScanProgressStatus } from '../components/ui/ScannerStatusIndicator';
import { ScanResult, DisplayScanResult } from '../types';

interface UseScanProcessProps {
  selectedOption: string;
  companyIds: number[] | { id: number; place_id: string; company_id?: number }[];
  onScanStartSuccess?: () => void;
  onScanStartError?: (errorMessage: string) => void;
  onPollUpdate?: (message: string) => void;
  onPollComplete?: () => void;
  onPollError?: (errorMessage: string) => void;
  onResultsLoadError?: (errorMessage: string) => void;
  onResultsLoadSuccess?: () => void;
  reloadDisplayedResults?: (strategyId: number) => Promise<void>;
}

interface UseScanProcessResult {
  isScanning: boolean;
  scanResults: ScanResult[];
  selectedScanResult: ScanResult | DisplayScanResult | null;
  isModalOpen: boolean;
  scanStatus: ScanProgressStatus;
  processedCount: number;
  totalCompanies: number;
  pollingError: string | null;
  startScan: () => Promise<void>;
  handleOpenModal: (result: ScanResult | DisplayScanResult) => void;
  handleCloseModal: () => void;
  clearPollingState: () => void;
}

// Define interfaces for API responses
interface ScanResultItem {
  place_id: string;
  company_name: string;
  result_text: string;
}

interface StreamMessage {
  type: 'init' | 'progress' | 'result' | 'done' | 'error';
  total?: number;
  processedCount?: number;
  result?: ScanResultItem;
  results?: ScanResultItem[];
  message?: string;
}

export const useScanProcess = ({
  selectedOption,
  companyIds,
  onScanStartSuccess,
  onScanStartError,
  onPollUpdate,
  onPollComplete,
  onPollError,
  onResultsLoadError,
  onResultsLoadSuccess,
  reloadDisplayedResults,
}: UseScanProcessProps): UseScanProcessResult => {
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [selectedScanResult, setSelectedScanResult] = useState<ScanResult | DisplayScanResult | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [scanStatus, setScanStatus] = useState<ScanProgressStatus>('idle');
  const [processedCount, setProcessedCount] = useState<number>(0);
  const [totalCompanies, setTotalCompanies] = useState<number>(0);
  const [pollingError, setPollingError] = useState<string | null>(null);

  const isScanning = scanStatus === 'loading';

  const clearPollingState = useCallback(() => {
    setScanStatus('idle');
    setPollingError(null);
    setProcessedCount(0);
    setTotalCompanies(0);
  }, []);

  const startScan = useCallback(async () => {
    // Validate strategy selection
    if (!selectedOption || !selectedOption.startsWith('opulab_')) {
      onScanStartError?.("Bitte wählen Sie zuerst eine gültige OpuLab Strategie aus.");
      return;
    }
    // Validate companies
    if (!companyIds || companyIds.length === 0) {
      onScanStartError?.("Bitte wählen Sie Unternehmen für den Scan aus (im OpuFinder).");
      return;
    }
    // Extract place_id and company_id if available, otherwise use numeric IDs
    const placeIds = companyIds
      .map((company) => (typeof company === 'object' && 'place_id' in company ? company.place_id : null))
      .filter((placeId) => placeId !== null) as string[];

    // Extract company_ids if available
    const companyIdsFromObjects = companyIds
      .map((company) => (typeof company === 'object' && 'company_id' in company && company.company_id ? company.company_id : null))
      .filter((id) => id !== null) as number[];

    const selectedCompanyIds = companyIds
      .map((company) => (typeof company === 'object' && 'id' in company ? company.id : company))
      .filter((id) => typeof id === 'number') as number[];

    if (placeIds.length === 0 && companyIdsFromObjects.length === 0) {
      console.warn("No place_id or company_id values found in companyIds. Falling back to numeric IDs. Update the calling component to pass SelectedCompany objects with place_id and company_id for accurate mapping.");
    }
    const idPart = selectedOption.split('_')[1];
    const strategyId = parseInt(idPart, 10);
    if (isNaN(strategyId)) {
      onScanStartError?.("Fehler: Ungültige OpuLab Strategie-ID ausgewählt.");
      return;
    }

    // Initialize state
    setScanStatus('loading');
    setProcessedCount(0);
    setTotalCompanies(companyIds.length);
    setPollingError(null);
    setScanResults([]);
    onPollUpdate?.('Scan wird gestartet...');

    try {
      onScanStartSuccess?.();

      const token = localStorage.getItem('token');
      let payload;

      // Prioritize different identifiers based on availability
      if (placeIds.length > 0) {
        payload = { strategyId, placeIds };
      } else if (companyIdsFromObjects.length > 0) {
        payload = { strategyId, companyIds: companyIdsFromObjects, isCompanyIds: true };
      } else {
        payload = { strategyId, selectedCompanyIds };
      }

      console.log('Sending scan request with payload:', payload);
      const response = await fetch(`${window.location.origin}/api/scan-chancen`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify(payload)
      });
      if (!response.body) throw new Error('Kein Antwortstream');
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      const aggregated: ScanResult[] = [];
      onPollUpdate?.('Scan gestartet...');
      while (true) {
        const { done, value } = await reader.read();
        if (value) {
          buffer += decoder.decode(value, { stream: !done });
          const parts = buffer.split('\n\n');
          buffer = parts.pop() || '';
          for (const part of parts) {
            const dataLine = part.startsWith('data:') ? part.slice(5) : null;
            if (!dataLine) continue;
            const msg = JSON.parse(dataLine) as StreamMessage;
            if (msg.type === 'init') {
              setTotalCompanies(msg.total || 0);
            } else if (msg.type === 'progress') {
              setProcessedCount(msg.processedCount || 0);
              onPollUpdate?.(`${msg.processedCount} von ${msg.total} analysiert`);
            } else if (msg.type === 'result') {
              if (msg.result) {
                const it = msg.result;
                const scanRes: ScanResult = {
                  place_id: it.place_id,
                  companyName: it.company_name,
                  result_text: it.result_text
                };
                aggregated.push(scanRes);
                setScanResults([...aggregated]);
              }
            } else if (msg.type === 'done') {
              setScanStatus('completed');
              setProcessedCount(msg.total || 0);
              if (msg.results) {
                setScanResults(msg.results.map((it: ScanResultItem) => ({
                  place_id: it.place_id,
                  companyName: it.company_name,
                  result_text: it.result_text
                })));
              }
              onPollUpdate?.('Scan abgeschlossen.');
              onPollComplete?.();
              onResultsLoadSuccess?.();
              if (reloadDisplayedResults) {
                await reloadDisplayedResults(strategyId);
                console.log(`Reloaded displayed results for strategy ${strategyId} after scan completion.`);
              }
              return;
            } else if (msg.type === 'error') {
              throw new Error(msg.message || 'Fehler beim Scan');
            }
          }
        }
        if (done) break;
      }
    } catch (error: unknown) {
      const msg = error instanceof Error ? error.message : "Unbekannter Fehler beim Scannen.";
      setScanStatus('error');
      setPollingError(msg);
      onPollError?.(msg);
      onResultsLoadError?.(msg);
    }
  }, [
    selectedOption,
    companyIds,
    onScanStartSuccess,
    onScanStartError,
    onPollUpdate,
    onPollComplete,
    onPollError,
    onResultsLoadError,
    onResultsLoadSuccess,
    reloadDisplayedResults,
  ]);

  const handleOpenModal = useCallback((result: ScanResult | DisplayScanResult) => {
    setSelectedScanResult(result);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedScanResult(null);
  }, []);

  return {
    isScanning,
    scanResults,
    selectedScanResult,
    isModalOpen,
    scanStatus,
    processedCount,
    totalCompanies,
    pollingError,
    startScan,
    handleOpenModal,
    handleCloseModal,
    clearPollingState,
  };
};
