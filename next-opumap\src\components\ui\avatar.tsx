"use client";
import * as React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  src?: string;
  alt?: string;
  initials?: string;
  size?: "sm" | "md" | "lg";
}

export const Avatar: React.FC<AvatarProps> = ({ src, alt, initials, size = "md", className, ...props }) => {
  const sizeClasses =
    size === "sm"
      ? "w-8 h-8 text-base"
      : size === "lg"
      ? "w-20 h-20 text-3xl"
      : "w-16 h-16 text-xl";
  
  const dimensions = size === "sm" 
    ? { width: 32, height: 32 } 
    : size === "lg" 
      ? { width: 80, height: 80 } 
      : { width: 64, height: 64 };
      
  return (
    <div
      className={cn(
        "rounded-full bg-muted flex items-center justify-center font-bold text-muted-foreground overflow-hidden",
        sizeClasses,
        className
      )}
      {...props}
    >
      {src ? (
        <Image 
          src={src} 
          alt={alt || ""} 
          width={dimensions.width}
          height={dimensions.height}
          className="object-cover w-full h-full rounded-full"
        />
      ) : (
        <span>{initials}</span>
      )}
    </div>
  );
};
