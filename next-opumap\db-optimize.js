/**
 * Database Query Optimization Script
 *
 * This script analyzes and optimizes database queries by:
 * 1. Analyzing table structures
 * 2. Providing optimization recommendations
 * 3. Suggesting performance improvements
 *
 * Run this script periodically to keep your database performance optimal.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(`${colors.bright}${colors.blue}=== Supabase Database Query Optimizer ===${colors.reset}\n`);

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error(`${colors.red}Error: Supabase credentials not found in .env.local${colors.reset}`);
  console.log('Make sure you have NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY defined');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Main function
async function optimizeDatabase() {
  try {
    console.log(`${colors.cyan}Connecting to Supabase at ${supabaseUrl}${colors.reset}`);

    // Directly query the tables we know exist in our application
    // This is more reliable than trying to access system tables which may be restricted
    console.log(`\n${colors.cyan}Analyzing application tables...${colors.reset}`);

    // List of tables we know exist in our application
    const knownTables = [
      'profiles',
      'user_strategies',
      'selected_companies',
      'users'
    ];

    console.log(`Analyzing ${knownTables.length} known application tables:`);

    // For each table, analyze its structure and usage patterns
    for (const tableName of knownTables) {
      console.log(`\n${colors.cyan}Analyzing table: ${tableName}${colors.reset}`);

      // Check if the table exists by trying to count rows
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (countError) {
        console.log(`${colors.yellow}Table ${tableName} could not be accessed: ${countError.message}${colors.reset}`);
        continue;
      }

      console.log(`- Table ${tableName} exists with approximately ${count} rows`);

      // Get a sample row to analyze structure
      const { data: sampleData, error: sampleError } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (sampleError || !sampleData || sampleData.length === 0) {
        console.log(`${colors.yellow}Could not fetch sample data from ${tableName}: ${sampleError?.message || 'No data'}${colors.reset}`);
        continue;
      }

      // Analyze the table structure
      const sampleRow = sampleData[0];
      const columns = Object.keys(sampleRow);

      console.log(`- Table has ${columns.length} columns: ${columns.join(', ')}`);

      // Check for common columns that should be indexed
      const commonIndexColumns = ['id', 'user_id', 'created_at', 'updated_at'];
      const tableColumns = columns.map(col => col.toLowerCase());

      const potentialIndexColumns = commonIndexColumns.filter(col =>
        tableColumns.includes(col.toLowerCase())
      );

      if (potentialIndexColumns.length > 0) {
        console.log(`${colors.yellow}Table ${tableName} might benefit from indexes on:${colors.reset}`);
        potentialIndexColumns.forEach(col => {
          console.log(`  - Column: ${col}`);
          console.log(`    Suggested SQL: CREATE INDEX IF NOT EXISTS idx_${tableName}_${col} ON public.${tableName} (${col});`);
        });
      }

      // Check for potential foreign keys
      const potentialForeignKeys = columns.filter(col =>
        col.toLowerCase().endsWith('_id') && col.toLowerCase() !== 'id'
      );

      if (potentialForeignKeys.length > 0) {
        console.log(`${colors.yellow}Potential foreign key relationships detected:${colors.reset}`);
        potentialForeignKeys.forEach(col => {
          const referencedTable = col.toLowerCase().replace('_id', '');
          console.log(`  - Column ${col} might reference table '${referencedTable}'`);
          console.log(`    Consider adding a foreign key constraint if appropriate`);
        });
      }

      // Check for timestamp columns
      const hasTimestamps = columns.some(col =>
        ['created_at', 'updated_at', 'deleted_at'].includes(col.toLowerCase())
      );

      if (!hasTimestamps) {
        console.log(`${colors.yellow}Table ${tableName} might benefit from timestamp columns:${colors.reset}`);
        console.log(`  - Consider adding created_at and updated_at columns for better auditing`);
      }
    }

    // Analyze query patterns based on our application knowledge
    console.log(`\n${colors.cyan}Analyzing common query patterns...${colors.reset}`);

    // Check user_strategies table queries
    if (knownTables.includes('user_strategies')) {
      console.log(`${colors.yellow}Optimizing user_strategies queries:${colors.reset}`);
      console.log(`  - Queries filtering by user_id should be efficient with an index`);
      console.log(`  - Consider adding a composite index on (user_id, created_at) for sorted queries`);
      console.log(`  - Suggested SQL: CREATE INDEX IF NOT EXISTS idx_user_strategies_user_id_created_at ON public.user_strategies (user_id, created_at);`);
    }

    // Check selected_companies table queries
    if (knownTables.includes('selected_companies')) {
      console.log(`${colors.yellow}Optimizing selected_companies queries:${colors.reset}`);
      console.log(`  - Queries filtering by user_id should be efficient with an index`);
      console.log(`  - Consider adding a composite index on (user_id, created_at) for sorted queries`);
      console.log(`  - Suggested SQL: CREATE INDEX IF NOT EXISTS idx_selected_companies_user_id_created_at ON public.selected_companies (user_id, created_at);`);
    }

    // Provide general optimization tips
    console.log(`\n${colors.cyan}General optimization recommendations:${colors.reset}`);
    console.log(`1. ${colors.yellow}Use parameterized queries to avoid SQL injection and improve query caching${colors.reset}`);
    console.log(`2. ${colors.yellow}Limit result sets with .limit() when possible${colors.reset}`);
    console.log(`3. ${colors.yellow}Only select the columns you need instead of using select('*')${colors.reset}`);
    console.log(`4. ${colors.yellow}Consider using materialized views for complex queries that run frequently${colors.reset}`);
    console.log(`5. ${colors.yellow}Use .range() for pagination instead of offset/limit for better performance${colors.reset}`);
    console.log(`6. ${colors.yellow}Add appropriate RLS (Row Level Security) policies for all tables${colors.reset}`);
    console.log(`7. ${colors.yellow}Consider using Supabase realtime features with care as they can impact performance${colors.reset}`);

    // Supabase-specific recommendations
    console.log(`\n${colors.cyan}Supabase-specific recommendations:${colors.reset}`);
    console.log(`1. ${colors.yellow}Use Supabase functions for complex operations instead of multiple round-trips${colors.reset}`);
    console.log(`2. ${colors.yellow}Consider using Supabase Edge Functions for compute-intensive operations${colors.reset}`);
    console.log(`3. ${colors.yellow}Monitor your project's usage in the Supabase dashboard${colors.reset}`);
    console.log(`4. ${colors.yellow}Use Supabase Storage with caution for large files${colors.reset}`);

    console.log(`\n${colors.bright}${colors.green}Database analysis complete!${colors.reset}`);

  } catch (error) {
    console.error(`${colors.red}Unexpected error:${colors.reset}`, error.message);
  }
}

// Run the optimization
optimizeDatabase().catch(err => {
  console.error(`${colors.red}Fatal error:${colors.reset}`, err);
  process.exit(1);
});
