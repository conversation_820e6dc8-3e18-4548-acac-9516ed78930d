'use server';

import { createClient } from '@/utils/supabase/server';
import { User } from '@/contexts/AuthContext';
import { revalidatePath } from 'next/cache';

export async function updateProfile(formData: FormData) {
  try {
    const supabase = await createClient();

    // Get the current authenticated user (more secure than getSession)
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Authentication error:', userError);
      return { error: 'Nicht authentifiziert' };
    }

    const userId = user.id;

    // Extract profile data from form
    const profileData: Partial<User> & { updated_at: string } = {
      name: formData.get('name') as string,
      company_name: formData.get('company_name') as string,
      address: formData.get('address') as string,
      phone: formData.get('phone') as string,
      website: formData.get('website') as string,
      employee_count: formData.get('employee_count') as string,
      company_info_points: formData.get('company_info_points') as string,
      updated_at: new Date().toISOString() // Add updated_at timestamp
    };

    console.log('Updating profile for user:', userId);
    console.log('Profile data:', profileData);

    // Update the profile in the database
    const { error, data } = await supabase
      .from('profiles')
      .update(profileData)
      .eq('id', userId)
      .select();

    if (error) {
      console.error('Error updating profile:', error);
      return { error: error.message };
    }

    console.log('Profile updated successfully:', data);

    // Revalidate the profile page to show updated data
    revalidatePath('/profile');

    // Force cache invalidation for the profile data
    revalidatePath('/', 'layout');

    return { success: true, data };
  } catch (error) {
    console.error('Unexpected error in updateProfile:', error);
    const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
    return { error: `Fehler beim Aktualisieren des Profils: ${message}` };
  }
}
