import { useEffect, useCallback, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { StrategyService, Strategy } from '../strategyService';
import { opulabApi } from './opulabApi';
import { useOpulabStrategieData } from './useOpulabStrategieData';
import { useOpulabUI } from './useOpulabUI';

// Define interfaces (consider moving to a types file if shared)
interface ProfileData {
    name: string;
    email: string;
    company_name?: string | null;
    address?: string | null;
    phone?: string | null;
    website?: string | null;
    employee_count?: number | string | null;
    company_info_points: string;
}

// Add type for step status
export type AnalysisStepStatus = 'pending' | 'loading' | 'completed' | 'error';
export interface AnalysisStepsStatus {
    aspects: AnalysisStepStatus;
    prioritization: AnalysisStepStatus;
    goalSetting: AnalysisStepStatus;
    briefing: AnalysisStepStatus;
    summary: AnalysisStepStatus;
}

export const useOpulabStrategie = () => {
    const { user, isLoading: isAuthLoading, refreshUserProfile } = useAuth();

    // Initialize Data Hook with company info from auth context
    const data = useOpulabStrategieData(user?.company_info_points ?? '');
    const ui = useOpulabUI();

    // --- Add State for Analysis Steps ---
    const [analysisStepsStatus, setAnalysisStepsStatus] = useState<AnalysisStepsStatus>({
        aspects: 'pending',
        prioritization: 'pending',
        goalSetting: 'pending',
        briefing: 'pending',
        summary: 'pending'
    });

    // Add a new state to control the animation for analysis results
    const [isAnalysisAnimationActive, setIsAnalysisAnimationActive] = useState(false);

    // Separate state for saving and analyzing to avoid overlap
    const [isSavingStrategy, setIsSavingStrategy] = useState(false);

    // Initial load of strategies

    const { loadStrategies } = data;
    const { showMessage } = ui;
    useEffect(() => {
        loadStrategies().catch(error => {
            showMessage(`Fehler beim initialen Laden der Strategien: ${error.message}`, false);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // --- Handler Functions ---

    const handleSaveCompanyInfo = useCallback(async () => {
        ui.showMessage('', true); // Clear previous messages, show in company info section
        ui.setIsSavingCompanyInfo(true); // Use specific saving state for company info
        try {
            if (!user || !user.name || !user.email) {
                throw new Error("Benutzerdaten nicht verfügbar.");
            }
            const profileData: ProfileData = {
                name: user.name, email: user.email,
                company_name: user.company_name, address: user.address,
                phone: user.phone, website: user.website,
                employee_count: user.employee_count,
                company_info_points: data.companyInfo
            };
            await opulabApi.saveCompanyInfo(profileData);
            ui.showMessage("Unternehmensinformationen erfolgreich gespeichert.", true);
            if (refreshUserProfile) {
                 await refreshUserProfile(); // Call refreshUserProfile to update auth context
            }
        } catch (error: Error | unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
            ui.showMessage(`Fehler: ${errorMessage}`, true);
        } finally {
            ui.setIsSavingCompanyInfo(false); // Reset specific saving state
        }
    }, [ui, data, user, refreshUserProfile]);

    const handleStartAnalysis = useCallback(async () => {
setIsAnalysisAnimationActive(true); // Activate animation when analysis starts
console.log('handleStartAnalysis invoked');
        data.setAnalysisResult(''); // Clear previous analysis result
        data.setShowResultButton(false); // Hide result button until new analysis completes
        if (!data.selectedStrategy?.id) {
            ui.showMessage('Keine Strategie ausgewählt oder gespeichert.', false, 5000);
            return;
        }
        ui.setIsAnalyzing(true); // Set analyzing state to true when starting analysis
        data.setShowResultButton(true); // Show result button optimistically

        // --- Reset Step Statuses ---
        setAnalysisStepsStatus({
            aspects: 'loading',
            prioritization: 'loading',
            goalSetting: 'loading',
            briefing: 'loading',
            summary: 'pending' // Summary starts later
        });

        const strategyId = data.selectedStrategy.id;
        const strategyBeforeAnalysis = { ...data.selectedStrategy }; // Store state before starting

        try {
            // Mark analysis as started in DB
await StrategyService.updateStrategy(strategyId, {
    ...strategyBeforeAnalysis, // Use stored state
    analysis_started: true
});
console.log('StrategyService.updateStrategy completed for', strategyId);

            // --- Perform parallel analyses with individual status updates ---
const aspectsToAnalyze = data.aspects.filter(a => a.checked).map(a => a.id);
console.log('Aspects to analyze:', aspectsToAnalyze);

            const goalPromise = opulabApi.startPersonalGoalAnalysis(strategyId, data.personalGoal)
                .then(goalData => {
                    setAnalysisStepsStatus(prev => ({ ...prev, goalSetting: 'completed' }));
                    data.setZielsetzungResult(goalData?.file?.content || "Pers. Ziel: Kein Ergebnis.");
                    data.setShowZielsetzungResult(true);
                    return goalData; // Pass data along if needed later
                })
                .catch(err => {
                    setAnalysisStepsStatus(prev => ({ ...prev, goalSetting: 'error' }));
                    console.error("Fehler Pers. Zielsetzung:", err);
                    throw err; // Re-throw to be caught by the main catch block
                });

            const aspectsPromise = opulabApi.startAspectsAnalysis(strategyId, aspectsToAnalyze)
                .then(aspectsData => {
                    setAnalysisStepsStatus(prev => ({ ...prev, aspects: 'completed' }));
                    // Potentially update UI with aspectsData if needed
                    return aspectsData;
                })
                .catch(err => {
                    setAnalysisStepsStatus(prev => ({ ...prev, aspects: 'error' }));
                    console.error("Fehler Relevante Aspekte:", err);
                    throw err;
                });

            const priorPromise = opulabApi.startPrioritizationAnalysis(strategyId, data.shortTermValue, data.longTermValue)
                .then(priorData => {
                    setAnalysisStepsStatus(prev => ({ ...prev, prioritization: 'completed' }));
                    data.setPriorisierungResult(priorData?.file?.content || "Priorisierung: Kein Ergebnis.");
                    data.setShowPriorisierungResult(true);
                    return priorData;
                })
                .catch(err => {
                    setAnalysisStepsStatus(prev => ({ ...prev, prioritization: 'error' }));
                    console.error("Fehler Priorisierung:", err);
                    throw err;
                });

            const briefingPromise = opulabApi.startBriefingAnalysis(strategyId, data.shortTermTasks, data.longTermGoals)
                .then(briefingData => {
                    setAnalysisStepsStatus(prev => ({ ...prev, briefing: 'completed' }));
                    // Potentially update UI with briefingData if needed
                    return briefingData;
                })
                .catch(err => {
                    setAnalysisStepsStatus(prev => ({ ...prev, briefing: 'error' }));
                    console.error("Fehler Strategie-Briefing:", err);
                    throw err;
                });

            // Wait for all parallel steps to finish (successfully or not)
            await Promise.all([goalPromise, aspectsPromise, priorPromise, briefingPromise]);

            // Check if any step failed before proceeding to summary
            const currentStatuses = analysisStepsStatus; // Read latest state after promises
            if (Object.values(currentStatuses).includes('error')) {
                 throw new Error("Ein oder mehrere Analyseschritte sind fehlgeschlagen.");
            }


            // Perform final summary analysis
            setAnalysisStepsStatus(prev => ({ ...prev, summary: 'loading' })); // Set summary loading
            const summaryData = await opulabApi.startTotalAnalysis(strategyId)
                 .catch(err => {
                     setAnalysisStepsStatus(prev => ({ ...prev, summary: 'error' }));
                     console.error("Fehler Gesamtanalyse:", err);
                     throw err; // Re-throw to be caught by the main catch block
                 });

            setAnalysisStepsStatus(prev => ({ ...prev, summary: 'completed' })); // Set summary completed
            const finalAnalysisContent = summaryData?.file?.analysis_result || "Gesamtanalyse fehlgeschlagen.";

            // Update strategy with final result in DB
            const finalUpdatedStrategy = await StrategyService.updateStrategy(strategyId, {
                ...strategyBeforeAnalysis, // Use stored state as base
                analysis_result: finalAnalysisContent,
                analysis_started: true // Ensure it's marked
            });

            // Update local data state fully
            data.updateSingleStrategyInList(finalUpdatedStrategy);
            ui.showMessage('Analyse erfolgreich abgeschlossen!', false, 5000);

        } catch (error: Error | unknown) {
            console.error('Fehler während der Analyse:', error);
            // Check which step failed based on the status state if possible
            // The error message might already indicate the failed step
            const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
            ui.showMessage(`Analysefehler: ${errorMessage}`, false);
            // Revert button based on whether previous result exists (before this failed attempt)
            data.setShowResultButton(strategyBeforeAnalysis.analysis_result ? true : false);
            // Reset statuses to pending on failure? Or leave error state visible? Let's leave errors visible.

            // Attempt to revert analysis_started state in DB using the stored state
            if (strategyBeforeAnalysis.id) {
                try {
                    await StrategyService.updateStrategy(strategyBeforeAnalysis.id, {
                        ...strategyBeforeAnalysis, // Restore previous state
                        analysis_started: false
                    });
                    // Also revert local state
                    data.updateSingleStrategyInList({ ...strategyBeforeAnalysis, analysis_started: false });
                } catch (revertError) {
                    console.error("Fehler beim Zurücksetzen des Analyse-Status in DB:", revertError);
                    // If DB revert fails, still revert local state for UI consistency
                    data.updateSingleStrategyInList({ ...strategyBeforeAnalysis, analysis_started: false });
                }
            }
        } finally {
            ui.setIsAnalyzing(false); // Set analyzing state to false when analysis is complete
            // Reset statuses to pending if analysis didn't complete?
            // Or maybe only if there wasn't an error?
            // For now, let's keep the completed/error states visible until next run.
        }
    }, [ui, data, analysisStepsStatus]);

    const handleStartPriorisierung = useCallback(async () => {
         if (!data.selectedStrategy?.id) {
             ui.showMessage('Keine Strategie ausgewählt.', false, 5000);
             return;
         }
         ui.showMessage('Priorisierung wird berechnet...', false, 0);
         data.setShowPriorisierungResult(false); // Hide old result
         setIsSavingStrategy(true); // Use specific saving state

         try {
             const priorData = await opulabApi.startPrioritizationAnalysis(
                 data.selectedStrategy.id,
                 data.shortTermValue,
                 data.longTermValue
             );
             data.setPriorisierungResult(priorData?.file?.content || 'Kein Ergebnis für Priorisierung.');
             data.setShowPriorisierungResult(true);
             ui.showMessage('Priorisierung abgeschlossen.', false);
         } catch (error: Error | unknown) {
             console.error('Fehler bei Priorisierung:', error);
             const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
             ui.showMessage(`Fehler bei Priorisierung: ${errorMessage}`, false);
             data.setPriorisierungResult('Fehler bei der Berechnung.');
             data.setShowPriorisierungResult(true); // Show error
         } finally {
             setIsSavingStrategy(false); // Reset specific saving state
         }
    }, [ui, data]);

    const handleSaveAnalysis = useCallback(async () => {
        if (!data.selectedStrategy?.id) {
            ui.showMessage('Keine Strategie zum Speichern der Analyse ausgewählt.', false, 5000);
            return;
        }
        ui.showMessage('Analyse wird gespeichert...', false, 0);
        setIsSavingStrategy(true); // Use separate saving state
        try {
            // Use StrategyService directly to update the strategy
            const updatedStrategy = await StrategyService.updateStrategy(
                data.selectedStrategy.id,
                { ...data.selectedStrategy, analysis_result: ui.analysisInput }
            );
            // Update local data state
            data.updateSingleStrategyInList(updatedStrategy);
            ui.showMessage('Analyse erfolgreich gespeichert.', false);
            ui.setShowLabModal(false); // Close modal on success
        } catch (error: Error | unknown) {
            console.error('Fehler beim Speichern der Analyse:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
            ui.showMessage(`Fehler: ${errorMessage}`, false);
        } finally {
            setIsSavingStrategy(false); // Reset saving state
        }
    }, [ui, data]);

    const handleAddNewStrategy = useCallback(async () => {
        ui.showMessage('', false); // Clear message
        setIsSavingStrategy(true); // Use specific saving state

        try {
            if (data.strategies.length >= 5) {
                throw new Error('Maximal 5 Strategien erlaubt.');
            }

            const newStrategyPayload: Omit<Strategy, 'id'> = {
                strategy_name: `Strategie ${data.strategies.length + 1}`,
                company_info: '', personal_goal: '', short_term_tasks: '',
                long_term_goals: '', relevant_aspects: [], short_term_value: 50,
                long_term_value: 50, analysis_started: false, analysis_result: ''
            };

            // Use StrategyService directly
            const savedStrategy = await StrategyService.createStrategy(newStrategyPayload);

            // Reload strategies and select the new one
            const updatedList = await data.loadStrategies(false); // Load without auto-selecting first

            // Find the newly added strategy
            const newlyAdded = updatedList.find(s => s.id === savedStrategy.id);

            if (newlyAdded) {
                // Select the new strategy
                data.updateStateFromStrategy(newlyAdded);
            } else {
                // Fallback: if not found (shouldn't happen), select first or reset
                data.updateStateFromStrategy(updatedList.length > 0 ? updatedList[0] : null);
            }

            data.setIsFormEnabled(true); // Enable form for the new strategy

            ui.showMessage('Neue Strategie erfolgreich erstellt!', false);
        } catch (error: Error | unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
            ui.showMessage(`Fehler beim Erstellen: ${errorMessage}`, false);
        } finally {
            setIsSavingStrategy(false); // Reset specific saving state
        }
    }, [ui, data]);

     const handleSaveSettings = useCallback(async (strategyName: string) => {
        ui.showMessage('', false);
        if (!data.selectedStrategy?.id || !data.isFormEnabled) {
             ui.showMessage('Keine gültige Strategie zum Speichern ausgewählt oder Formular deaktiviert.', false, 5000);
             return;
         }
        setIsSavingStrategy(true); // Use specific saving state

        try {
            const relevantAspectsArray = data.aspects
                .filter(aspect => aspect.checked)
                .map(aspect => aspect.id);

            const strategyData: Strategy = {
                ...data.selectedStrategy, // Keep existing fields like id
                strategy_name: strategyName,
                company_info: '', // Never save company info here
                personal_goal: data.personalGoal,
                short_term_tasks: data.shortTermTasks,
                long_term_goals: data.longTermGoals,
                relevant_aspects: relevantAspectsArray,
                short_term_value: data.shortTermValue,
                long_term_value: data.longTermValue,
                analysis_result: data.analysisResult, // Persist current analysis result
                analysis_started: data.showResultButton, // Persist current analysis state
            };

            // Use StrategyService directly
            const savedStrategy = await StrategyService.updateStrategy(data.selectedStrategy.id, strategyData);

            // Ensure the saved strategy has the correct strategy_name
            const updatedStrategy = {
                ...savedStrategy,
                strategy_name: strategyName // Ensure the name is preserved
            };

            // Update local data state
            data.updateSingleStrategyInList(updatedStrategy);

            // Update the selected strategy to ensure the name is displayed correctly
            data.updateStateFromStrategy(updatedStrategy);

            // Show message in the save settings section
            ui.showMessage('Strategie erfolgreich aktualisiert!', 'saveSettings');

        } catch (error: Error | unknown) {
             const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
             ui.showMessage(`Fehler beim Speichern: ${errorMessage}`, 'saveSettings');
        } finally {
            setIsSavingStrategy(false); // Reset specific saving state
        }
    }, [ui, data]);

    const handleDeleteStrategy = useCallback(async () => {
         ui.showMessage('', false);
        if (!data.selectedStrategy?.id) {
            ui.showMessage('Keine Strategie zum Löschen ausgewählt.', false, 5000);
            return;
        }
        setIsSavingStrategy(true); // Use specific saving state
        try {
            // Use StrategyService directly
            await StrategyService.deleteStrategy(data.selectedStrategy.id);
            ui.showMessage('Strategie erfolgreich gelöscht!', false);

            // Reset selection and reload strategies, selecting the first if available
            data.updateStateFromStrategy(null); // Reset form etc.
            await data.loadStrategies(true); // Reload and select first

        } catch (error: Error | unknown) {
             const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
             ui.showMessage(`Fehler beim Löschen: ${errorMessage}`, false);
        } finally {
            setIsSavingStrategy(false); // Reset specific saving state
        }
    }, [ui, data]);


    // --- Return combined state and handlers ---
    return {
        // Data state (spread from data hook)
        ...data,
        // UI state (spread from ui hook)
        ...ui,
        // --- Return new status state ---
        analysisStepsStatus,
        // Handler functions defined in this main hook
        handleSaveCompanyInfo,
        handleStartAnalysis,
        handleStartPriorisierung,
        handleSaveAnalysis,
        handleAddNewStrategy,
        handleSaveSettings,
        handleDeleteStrategy,
        // Auth state (passed through)
        isAuthLoading,
        isAnalysisAnimationActive,
        isSavingStrategy, // Expose separate saving state
    };
};
