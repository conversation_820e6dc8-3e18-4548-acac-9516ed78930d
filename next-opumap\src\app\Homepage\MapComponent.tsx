'use client';

import React, { useCallback } from "react";
import { GoogleMap, useLoadScript } from "@react-google-maps/api";
import { useAuth } from "@/contexts/AuthContext";
import { fetchWithAuth } from "@/lib/supabaseClient";
import {
  mapLayoutStyles,
  mapOptions,
  mapDefaults,
  statusStyles,
  businessCardAdditionalStyles,
  DEMO_MARKERS
} from "./styles";

import {
  useMapState,
  useBusinessSelection,
  useAnalysis,
  useOpuScannerIntegration
} from "./hooks";
import {
  BusinessCard,
  AnalysisSection,
  OpuScannerSection,
  AnalysisModal,
  AdvancedMarker
} from "./components";

/**
 * Main MapComponent that serves as the homepage when logged in
 */
const MapComponent: React.FC = () => {
  // Get authentication context
  const { user } = useAuth();

  // Custom hooks for state management
  const {
    mapCenter,
    mapZoom,
    isDarkMode,
    mapRef,
    saveMapState,
    loadMapState,
    resetMapToDefaults
  } = useMapState(user);

  const {
    quickAnalysisData,
    quickAnalysisDate,
    isQuickAnalysisLoading,
    deepAnalysisData,
    deepAnalysisDate,
    isDeepAnalysisLoading,
    showAnalysisModal,
    setShowAnalysisModal,
    currentAnalysisType,
    resetAnalysisState,
    checkExistingAnalysis,
    handleCreateQuickAnalysis,
    handleViewQuickAnalysis,
    handleCreateDeepAnalysis,
    handleViewDeepAnalysis
  } = useAnalysis(user);

  const {
    selectedBusiness,
    isFlipped,
    handleMarkerClick,
    handleMapClick,
    toggleFlip,
    lastSelectedBusinessRef
  } = useBusinessSelection(user, saveMapState, checkExistingAnalysis, resetAnalysisState);

  const {
    addToOpuScanner,
    setAddToOpuScanner,
    isAddingToOpuScanner
  } = useOpuScannerIntegration(
    user,
    selectedBusiness,
    fetchWithAuth,
    !!quickAnalysisData, // Pass quick analysis status
    !!deepAnalysisData // Pass deep analysis status
  );

  // Load Google Maps API
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
    libraries: mapDefaults.libraries,
  });

  // Map load handler
  const onMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;

    // Apply saved map state if available and user is logged in
    if (user) {
      const savedMapState = loadMapState();
      if (savedMapState) {
        console.log("Map loaded, applying saved state");
        map.panTo(savedMapState.center);
        map.setZoom(savedMapState.zoom);

        // Restore selected business if available
        if (savedMapState.selectedBusinessPlaceId && !selectedBusiness) {
          console.log("Restoring selected business from saved state:", savedMapState.selectedBusinessPlaceId);
          // We'll restore the business once the Places service is available
          lastSelectedBusinessRef.current = savedMapState.selectedBusinessPlaceId;
        }
      } else {
        // No saved state, use defaults
        resetMapToDefaults();
      }
    } else {
      // User not logged in, use defaults
      resetMapToDefaults();
    }

    // Add event listeners for map changes
    map.addListener('idle', () => {
      if (mapRef.current && user) {
        saveMapState(selectedBusiness?.place_id);
      }
    });
  }, [user, loadMapState, resetMapToDefaults, saveMapState, selectedBusiness, lastSelectedBusinessRef, mapRef]);

  // Render loading/error states for map
  if (loadError)
    return <div className={statusStyles.errorMessage}>Error loading maps</div>;
  if (!isLoaded)
    return <div className={statusStyles.loadingMessage}>Loading Maps...</div>;

  // Main component render
  return (
    <div className={mapLayoutStyles.container}>
      {/* Map Section */}
      <div className={mapLayoutStyles.mapSection}>
        <GoogleMap
          mapContainerClassName={mapLayoutStyles.mapContainer}
          center={mapCenter}
          zoom={mapZoom}
          options={mapOptions(isDarkMode)}
          onLoad={onMapLoad}
          onClick={handleMapClick} // Handle clicks on the map (including POIs)
        >
          {/* Render demo markers only if they're not the selected business */}
          {DEMO_MARKERS.map((marker) => (
            selectedBusiness?.place_id !== marker.place_id && (
              <AdvancedMarker
                key={marker.id}
                position={marker.position}
                onClick={() => handleMarkerClick(marker)} // Handle clicks on demo markers
              />
            )
          ))}
          {/* Render marker for the selected business */}
          {selectedBusiness && (
            <AdvancedMarker
              key={`selected-${selectedBusiness.place_id}`}
              position={
                "geometry" in selectedBusiness && selectedBusiness.geometry?.location
                  ? selectedBusiness.geometry.location
                  : "position" in selectedBusiness
                    ? selectedBusiness.position
                    : mapCenter // Fallback to map center if no position available
              }
            />
          )}
        </GoogleMap>
      </div>

      {/* Content Section: Business Info + Analysis */}
      <div className={mapLayoutStyles.contentSection}>
        {/* Business Info Card (Flip Card) */}
        <div className={businessCardAdditionalStyles.businessCardContainer}>
          <BusinessCard
            selectedBusiness={selectedBusiness}
            isFlipped={isFlipped}
            toggleFlip={toggleFlip}
          />
        </div>

        {/* Right side column with Analysis and OpuScanner */}
        <div className="flex flex-col w-full md:w-auto mt-4 md:mt-0">
          {/* Analysis Sections */}
          <AnalysisSection
            selectedBusiness={selectedBusiness}
            quickAnalysisData={quickAnalysisData}
            quickAnalysisDate={quickAnalysisDate}
            isQuickAnalysisLoading={isQuickAnalysisLoading}
            deepAnalysisData={deepAnalysisData}
            deepAnalysisDate={deepAnalysisDate}
            isDeepAnalysisLoading={isDeepAnalysisLoading}
            handleCreateQuickAnalysis={handleCreateQuickAnalysis}
            handleViewQuickAnalysis={handleViewQuickAnalysis}
            handleCreateDeepAnalysis={handleCreateDeepAnalysis}
            handleViewDeepAnalysis={handleViewDeepAnalysis}
          />

          {/* OpuScanner Integration */}
          <OpuScannerSection
            selectedBusiness={selectedBusiness}
            user={user}
            addToOpuScanner={addToOpuScanner}
            setAddToOpuScanner={setAddToOpuScanner}
            isAddingToOpuScanner={isAddingToOpuScanner}
            // Pass analysis status down
            hasQuickAnalysis={!!quickAnalysisData}
            hasDeepAnalysis={!!deepAnalysisData}
          />
        </div>
      </div>

      {/* Analysis Modal */}
      <AnalysisModal
        showModal={showAnalysisModal}
        setShowModal={setShowAnalysisModal}
        analysisType={currentAnalysisType}
        analysisData={currentAnalysisType === "schnell" ? quickAnalysisData : deepAnalysisData}
        analysisDate={currentAnalysisType === "schnell" ? quickAnalysisDate : deepAnalysisDate}
        selectedBusiness={selectedBusiness}
      />
    </div>
  );
};

export default MapComponent;
