'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';

// Import Hook
import { useOpulabStrategie } from './hooks/useOpulabStrategie';

// Import components
import {
  StrategieInfo,
  StrategieBriefing,
  StrategieAnalyse,
  StrategieList,
  RelevantAspects,
  SaveSettingsSection
} from './components';
import { Dialog, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import Loader from '@/components/ui/Loader'; // Korrigierter Importpfad für globalen Loader
import { Button } from '@/components/ui/button';
import { CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { TouchStartLaborButton } from './components/ui';
import AnalysisStatusIndicator from './components/ui/AnalysisStatusIndicator';
import { Rocket } from 'lucide-react';
import MarkdownEditor from '@/components/MarkdownEditor';

// Import styles
import {
  containerStyles,
  headerStyles,
  cardStyles,
  spacingStyles
} from './styles';

// Define the main component
export default function OpulabStrategiePage() {
  // Use the custom hook to manage state and logic
  const {
    // State
    strategies,
    selectedStrategy,
    isNewStrategy,
    isLoadingStrategies,
    isFormEnabled,
    companyInfo,
    personalGoal,
    shortTermTasks,
    longTermGoals,
    analysisResult,
    isSavingStrategy: isSaving,
    isAnalyzing,
    message,
    messageLocation,
    showMessageInCompanyInfo,
    showLabModal,
    analysisInput,
    // showResultButton, // Implicitly handled by analysisResult/selectedStrategy state
    priorisierungResult,
    // showPriorisierungResult, // Implicitly handled by priorisierungResult state
    // zielsetzungResult, // Not directly displayed, used in analysis
    // showZielsetzungResult, // Not directly displayed
    shortTermValue,
    longTermValue,
    aspects,
    analysisStepsStatus,
    // Setters
    setCompanyInfo,
    setPersonalGoal,
    setShortTermTasks,
    setLongTermGoals,
    setAnalysisInput,
    setShowLabModal,
    setShortTermValue,
    setLongTermValue,
    // Handlers
    handleSelectStrategy,
    handleSaveCompanyInfo,
    handleStartAnalysis,
    handleStartPriorisierung,
    handleSaveAnalysis,
    handleAddNewStrategy,
    handleAspectChange,
    handleSaveSettings,
    handleDeleteStrategy,
  } = useOpulabStrategie();

  // No need for handleCreateAIStrategy in the hook, keep it here if needed
  const handleCreateAIStrategy = () => {
    console.log('Creating AI strategy - Placeholder');
    // Add API call logic here if functionality exists
  };

  // Determine if the main analysis button should be visible/active
  // This logic could also reside within the hook if preferred
  const canStartAnalysis = isFormEnabled && selectedStrategy && !isSaving && !isAnalyzing;

  // Effect to fix scroll issues when modal is closed
  useEffect(() => {
    // When modal is closed, restore scroll functionality
    if (!showLabModal) {
      // Small timeout to ensure DOM is updated
      const timer = setTimeout(() => {
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
      }, 100);

      return () => clearTimeout(timer);
    }
    // Leere Bereinigungsfunktion für den anderen Pfad
    return () => {};
  }, [showLabModal]);

  return (
    <>
    <div className={containerStyles.wrapper}>
      {/* Header Section */}
      <div className={spacingStyles.section}>
        <div className="flex flex-col md:flex-row md:items-start">
          <header className={`${headerStyles.container} md:text-left w-full`}>
            <h1 className={headerStyles.title}>
              <span className={headerStyles.titleSpan}>Opulab</span>
              <div className={headerStyles.subtitleContainer}>
                <span className="font-light">Opportunity Laboratory</span>
                <Image 
                  src="/reagaenzglas-svg.svg" 
                  alt="Reagenzglas" 
                  className={headerStyles.icon} 
                  width={24} 
                  height={24}
                  priority
                />
              </div>
            </h1>
            <h2 className={headerStyles.subtitle}>Individuelle Strategie erstellen</h2>
          </header>
        </div>
      </div>

      {/* Three column layout */}
      <div className="grid grid-cols-1 lg:grid-cols-[20%,30%,50%] gap-x-6">
        {/* First Column - Strategy List, Company Info, Aspects */}
        <div className={`${containerStyles.column} order-1 lg:order-1`}>
          {/* Strategy List */}
          <div className="w-full">
            <StrategieList
              strategies={strategies}
              selectedStrategy={selectedStrategy}
              onSelectStrategy={handleSelectStrategy}
              onAddNewStrategy={handleAddNewStrategy}
              onCreateAIStrategy={handleCreateAIStrategy} // Keep placeholder or implement
              isLoading={isLoadingStrategies}
            />
          </div>

          {/* Company Info */}
          <div className={spacingStyles.section}>
            <div className={`${cardStyles.elevated} ${(!isFormEnabled || isSaving) ? 'opacity-50' : ''}`}>
              <StrategieInfo
                companyInfo={companyInfo}
                onCompanyInfoChange={setCompanyInfo} // Pass setter
                onSave={handleSaveCompanyInfo}
                isSaving={isSaving}
                message={showMessageInCompanyInfo ? message : ''}
                showPersonalGoal={false} // Configure component variant
                disabled={!isFormEnabled || isSaving}
                personalGoal={personalGoal} // Required prop, even if hidden
                onPersonalGoalChange={setPersonalGoal} // Required prop
              />
            </div>
          </div>

          {/* Relevant Aspects */}
          <div className={spacingStyles.section}>
            <div className={`${cardStyles.elevated} ${(!isFormEnabled || isSaving) ? 'opacity-50 pointer-events-none' : ''}`}>
              <RelevantAspects
                aspects={aspects}
                onAspectChange={handleAspectChange}
                disabled={!isFormEnabled || isSaving}
              />
            </div>
          </div>
        </div>

        {/* Second Column - Prioritization */}
        <div className={`${containerStyles.column} order-2 lg:order-2`}>
          <div className={spacingStyles.section}>
            <div className={`${cardStyles.default} ${(!isFormEnabled || isSaving) ? 'opacity-50 pointer-events-none' : ''}`}>
              <StrategieAnalyse
                result={priorisierungResult}
                onStartAnalysis={handleStartPriorisierung}
                showAnalysisResult={false}
                shortTermValue={shortTermValue}
                longTermValue={longTermValue}
                onShortTermChange={(value) => setShortTermValue(value[0])}
                onLongTermChange={(value) => setLongTermValue(value[0])}
              />
            </div>
          </div>
        </div>

        {/* Third Column - Personal Goals, Briefing, Save, Analysis */}
        <div className={`${containerStyles.column} order-3 lg:order-3`}>
          {/* Personal Goal & Briefing */}
          <div className={`flex flex-col ${spacingStyles.cardGap} ${spacingStyles.section}`}>
            {/* Personal Goal */}
          <div className="w-full min-h-[450px]">
            <div className={`${cardStyles.elevated} ${(!isFormEnabled || isSaving) ? 'opacity-50' : ''}`}>
                 <StrategieInfo
                   personalGoal={personalGoal}
                   onPersonalGoalChange={setPersonalGoal} // Pass setter
                   onSave={handleSaveCompanyInfo} // Saving company info saves all profile data
                   isSaving={isSaving}
                   message={!showMessageInCompanyInfo ? message : ''}
                   showCompanyInfo={false} // Configure component variant
                   disabled={!isFormEnabled || isSaving}
                   companyInfo={companyInfo} // Required prop, even if hidden
                   onCompanyInfoChange={setCompanyInfo} // Required prop
                 />
              </div>
            </div>

            {/* Strategy Briefing */}
            <div className="w-full min-h-[350px]">
              <div className={`${cardStyles.elevated} ${(!isFormEnabled || isSaving) ? 'opacity-50 pointer-events-none' : ''}`}>
                <StrategieBriefing
                  shortTermTasks={shortTermTasks}
                  longTermGoals={longTermGoals}
                  onShortTermTasksChange={setShortTermTasks}
                  onLongTermGoalsChange={setLongTermGoals}
                />
              </div>
            </div>
          </div>

          {/* Save Settings */}
          <div className={spacingStyles.section}>
            <div className={`${(!isFormEnabled || isSaving) ? 'opacity-50 pointer-events-none' : ''}`}>
              <SaveSettingsSection
                onSaveSettings={handleSaveSettings}
                onDeleteStrategy={handleDeleteStrategy}
                isSaving={isSaving}
                selectedStrategy={selectedStrategy?.strategy_name || ''}
                isNewStrategy={isNewStrategy}
                message={messageLocation === 'saveSettings' ? message : ''} // Only show message if it's for this section
                disabled={!isFormEnabled || isSaving}
              />
            </div>
          </div>

          {/* Analysis Result */}
          <div className={spacingStyles.section}>
            <div className={`${cardStyles.contrast} ${!isFormEnabled ? 'opacity-50 pointer-events-none' : ''}`}>
              <CardHeader className={cardStyles.header + ' text-center items-center'}>
                <CardTitle>Analyseergebnis</CardTitle>
                <CardDescription className="mb-2">KI-gestützte Analyse</CardDescription>
                 {message && messageLocation === 'analysis' && (
                    <p className={`text-sm ${message.startsWith('Fehler') ? 'text-red-500' : 'text-green-500'}`}>{message}</p>
                 )}
                 {/* Render Loader only when analyzing */}
                 {isAnalyzing && (
                   <div className="mt-2 flex flex-col items-center w-full">
                     <Loader />
                   </div>
                 )}
                 {/* Render Status Indicator only when analyzing */}
                 {isAnalyzing && analysisStepsStatus && (
                   <div className="mt-2 flex flex-col items-center w-full">
                     <AnalysisStatusIndicator status={analysisStepsStatus} />
                   </div>
                 )}
              </CardHeader>
              <CardContent className="flex flex-col items-center mt-4">
                <TouchStartLaborButton onClick={handleStartAnalysis} disabled={!canStartAnalysis}>
                    {isAnalyzing ? "Analysiere..." : "Labor starten"} <Rocket className={`inline-block ml-2 ${isAnalyzing ? 'animate-pulse' : ''}`} />
                </TouchStartLaborButton>
                {selectedStrategy?.id && analysisResult && (
                    <div className="mt-6 w-full max-w-md flex flex-col items-center">
                      <span className="text-base font-semibold text-foreground mb-2">Aktuelles Ergebnis:</span>
                      <Button variant="default" onClick={() => { setAnalysisInput(analysisResult); setShowLabModal(true); }} disabled={isSaving}>
                          {selectedStrategy?.strategy_name + " Analyse"}
                      </Button>
                    </div>
                )}
              </CardContent>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Analysis Edit Modal */}
    <Dialog
      open={showLabModal}
      onOpenChange={(open) => {
        setShowLabModal(open);
        // Immediately fix scroll when closing via the X button or clicking outside
        if (!open) {
          document.body.style.overflow = '';
          document.body.style.paddingRight = '';
        }
      }}
    >
      <DialogContent className="max-w-screen-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Analyse bearbeiten: {selectedStrategy?.strategy_name}</DialogTitle>
          <DialogDescription>Geben Sie Ihren Text ein oder bearbeiten Sie das Ergebnis:</DialogDescription>
        </DialogHeader>
        <div className="w-full mb-4">
          <MarkdownEditor
            value={analysisInput}
            onChange={(value) => setAnalysisInput(value || '')}
            height={500}
            preview="edit"
          />
        </div>
      <DialogFooter>
        <Button
          variant="outline"
          onClick={() => {
            setShowLabModal(false);
            // Fix scroll when using the button to close
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
          }}
          disabled={isSaving}
        >
          Abbrechen
        </Button>
        <Button onClick={handleSaveAnalysis} disabled={isSaving}>{isSaving ? 'Speichern...' : 'Speichern'}</Button>
      </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  );
}
