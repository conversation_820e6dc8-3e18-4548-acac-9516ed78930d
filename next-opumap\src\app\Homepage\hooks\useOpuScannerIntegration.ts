import { useState, useEffect, useCallback, useRef } from "react";
import { BusinessData } from "@/types";
import type { User } from "@/contexts/AuthContext";
import { toast } from 'react-toastify'; // Import toast for notifications
import { SelectedCompany } from "@/app/opuscanner/types"; // Add import for SelectedCompany type

/**
 * Custom hook to manage OpuScanner integration
 */
export const useOpuScannerIntegration = (
  user: User | null,
  selectedBusiness: BusinessData | null,
  fetchWithAuth: (url: string, options?: RequestInit) => Promise<Response>,
  hasQuickAnalysis: boolean, // Add parameter
  hasDeepAnalysis: boolean   // Add parameter
) => {
  // Helper function to handle 401 responses
  const handleUnauthorized = (response: Response): boolean => {
    if (response.status === 401) {
      window.location.href = '/login';
      return true;
    }
    return false;
  };

  // State for OpuScanner integration
  const [addToOpuScanner, setAddToOpuScanner] = useState<boolean>(false);
  const [isCompanySelected, setIsCompanySelected] = useState<boolean>(false);
  const [isAddingToOpuScanner, setIsAddingToOpuScanner] = useState<boolean>(false);
  const currentBusinessIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (selectedBusiness?.place_id) {
      currentBusinessIdRef.current = selectedBusiness.place_id;
    } else {
      currentBusinessIdRef.current = null;
    }
  }, [selectedBusiness?.place_id]);

  // Check if the company is already selected in OpuScanner
  const checkIfCompanyIsSelected = useCallback(async (placeId: string) => {
    if (!user) return;

    try {
      const response = await fetchWithAuth('/api/selected-companies');

        if (handleUnauthorized(response)) return;
      if (!response.ok) {
        throw new Error('Fehler beim Abrufen der ausgewählten Unternehmen');
      }

      const data = await response.json();
      if (currentBusinessIdRef.current !== placeId) {
        return;
      }
      const isSelected = data.companies.some((company: SelectedCompany) => company.place_id === placeId);

      setIsCompanySelected(isSelected);
      setAddToOpuScanner(isSelected); // Sync the toggle with the actual state

    } catch (error) {
      console.error('Fehler beim Prüfen des Unternehmensstatus:', error);
    }
  }, [user, fetchWithAuth]);

  // Add the current company to OpuScanner
  const addCompanyToOpuScanner = useCallback(async () => {
    if (!selectedBusiness || !selectedBusiness.place_id || !user) return;

    // --- Analysis Check ---
    if (!hasQuickAnalysis && !hasDeepAnalysis) {
      console.warn("Versuch, Unternehmen ohne Analyse zum OpuScanner hinzuzufügen.");
      toast.warn("Unternehmen kann nicht hinzugefügt werden: Mindestens eine Analyse (Schnell oder Tief) muss vorhanden sein.", {
        position: "bottom-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      setAddToOpuScanner(false); // Reset the toggle immediately
      return; // Stop execution
    }
    // --- End Analysis Check ---

    setIsAddingToOpuScanner(true);

    try {
      const companyData = {
        place_id: selectedBusiness.place_id,
        name: selectedBusiness.name || 'Unbekanntes Unternehmen',
        address: selectedBusiness.formatted_address || ''
      };

      const response = await fetchWithAuth('/api/selected-companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(companyData)
      });

        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
          throw new Error('Fehler beim Hinzufügen des Unternehmens zur Auswahl');
        }

      setIsCompanySelected(true);
      console.log('Unternehmen erfolgreich zum OpuScanner hinzugefügt');

    } catch (error) {
      console.error('Fehler beim Hinzufügen des Unternehmens:', error);
      setAddToOpuScanner(false); // Reset toggle on error
    } finally {
      setIsAddingToOpuScanner(false);
    }
  }, [selectedBusiness, user, fetchWithAuth, hasQuickAnalysis, hasDeepAnalysis]);

  // Remove the current company from OpuScanner
  const removeCompanyFromOpuScanner = useCallback(async () => {
    if (!selectedBusiness || !selectedBusiness.place_id || !user) return;

    setIsAddingToOpuScanner(true);

    try {
      const response = await fetchWithAuth(`/api/selected-companies?place_id=${selectedBusiness.place_id}`, {
        method: 'DELETE'
      });

        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
          throw new Error('Fehler beim Entfernen des Unternehmens aus der Auswahl');
        }

      setIsCompanySelected(false);
      console.log('Unternehmen erfolgreich aus dem OpuScanner entfernt');

    } catch (error) {
      console.error('Fehler beim Entfernen des Unternehmens:', error);
      setAddToOpuScanner(true); // Reset toggle on error
    } finally {
      setIsAddingToOpuScanner(false);
    }
  }, [selectedBusiness, user, fetchWithAuth]);

  // Update integration selection based on toggle and current selection
  useEffect(() => {
    // Only proceed if a business is selected and user exists
    if (!selectedBusiness?.place_id || !user) {
      return;
    }

    // Check analysis *before* deciding to add/remove
    const canBeAdded = hasQuickAnalysis || hasDeepAnalysis;

    if (addToOpuScanner && !isCompanySelected) {
      // Attempting to add
      if (canBeAdded) {
        // Analysis exists, proceed with adding
        addCompanyToOpuScanner();
      } else {
        // Analysis missing - this state ideally shouldn't be reached due to UI checks,
        // but as a safeguard, log and reset the toggle.
        console.warn(
          "Add attempt detected in useEffect despite missing analysis. Resetting toggle."
        );
        setAddToOpuScanner(false); // Force reset
      }
    } else if (!addToOpuScanner && isCompanySelected) {
      // Attempting to remove - no analysis check needed for removal
      removeCompanyFromOpuScanner();
    }
  }, [
    addToOpuScanner,
    selectedBusiness, // Depend on the whole object
    user,
    isCompanySelected,
    hasQuickAnalysis, // Add analysis status to dependencies
    hasDeepAnalysis, // Add analysis status to dependencies
    addCompanyToOpuScanner, // Add callback dependency
    removeCompanyFromOpuScanner, // Add callback dependency
  ]);

  // Check if the current business is already selected in OpuScanner
  useEffect(() => {
    setAddToOpuScanner(false);
    setIsCompanySelected(false);
    if (selectedBusiness?.place_id && user) {
      checkIfCompanyIsSelected(selectedBusiness.place_id);
    }
  }, [selectedBusiness?.place_id, user, checkIfCompanyIsSelected]);

  return {
    addToOpuScanner,
    setAddToOpuScanner,
    isCompanySelected,
    isAddingToOpuScanner,
    checkIfCompanyIsSelected,
    addCompanyToOpuScanner,
    removeCompanyFromOpuScanner
  };
};
