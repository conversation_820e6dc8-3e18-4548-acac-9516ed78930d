import { createClient } from '@/utils/supabase/server';
import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') ?? '/';
  const type = requestUrl.searchParams.get('type'); // Get the type parameter (recovery for password reset)

  console.log('Auth callback called with code:', code ? 'present' : 'missing', 'next:', next, 'type:', type || 'none');

  if (code) {
    try {
      const supabase = await createClient();

      console.log('Exchanging code for session...');
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(`${requestUrl.origin}/auth/auth-code-error?error=${encodeURIComponent(error.message)}`);
      }

      if (data.session) {
        console.log('Session exchange successful, user ID:', data.session.user.id);

                // Force multiple session refreshes to ensure proper cookie propagation        console.log('Performing additional session refresh...');        await supabase.auth.getSession();        // Wait longer for cookie setting to complete in production environments        await new Promise(resolve => setTimeout(resolve, 500)); // Increased timeout for production        // Multiple refreshes for better cookie propagation        await supabase.auth.getSession();        await new Promise(resolve => setTimeout(resolve, 300));                // Additional refresh cycle for better session establishment        await supabase.auth.getSession();        await new Promise(resolve => setTimeout(resolve, 200));
        
        // Final session check
        const { data: { session: finalSession } } = await supabase.auth.getSession();
        if (finalSession) {
          console.log('Final session confirmed, user ID:', finalSession.user.id);

          // For password reset flow, add a special parameter to indicate valid session
          if (type === 'recovery' && next.includes('/reset-password')) {
            console.log('Password reset flow detected, adding session validation parameter');
            return NextResponse.redirect(`${requestUrl.origin}${next}?session_valid=true&auth_event=recovery`);
          }
        } else {
          console.warn('Final session check failed');
        }
      } else {
        console.warn('Session exchange completed but no session returned');

        // Special handling for password reset with no session
        if (type === 'recovery' && next.includes('/reset-password')) {
          console.log('Password reset flow with no session, redirecting with error parameter');
          return NextResponse.redirect(`${requestUrl.origin}${next}?session_error=no_session&auth_event=recovery`);
        }
      }

    } catch (err) {
      console.error('Unexpected error during code exchange:', err);
      return NextResponse.redirect(`${requestUrl.origin}/auth/auth-code-error?error=unexpected_error`);
    }
  } else {
    console.warn('No code parameter provided to callback');
    return NextResponse.redirect(`${requestUrl.origin}/auth/auth-code-error?error=no_code`);
  }

  // Create response with enhanced headers for proper cookie propagation
  const response = NextResponse.redirect(`${requestUrl.origin}${next}`);

  // Add comprehensive cache control to prevent caching of this redirect
  response.headers.set('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');

  // Add a small delay header to help with cookie propagation
  response.headers.set('X-Session-Processed', 'true');

  console.log('Redirecting to:', `${requestUrl.origin}${next}`);
  return response;
}
