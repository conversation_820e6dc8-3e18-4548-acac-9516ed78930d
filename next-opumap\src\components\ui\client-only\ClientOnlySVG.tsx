'use client';

import { SVGProps } from 'react';
import ClientOnly from './ClientOnly';

/**
 * ClientOnlySVG Komponente, die sicherstellt, dass SVG-Elemente nur auf dem Client gerendert werden.
 * Dies verhindert Hydration-<PERSON>hler, die durch Browser-Erweiterungen wie Dark Reader verursacht werden können.
 */
export default function ClientOnlySVG(props: SVGProps<SVGSVGElement>) {
  // Einfacher Platzhalter für SVG während des Server-Renderings
  const fallback = (
    <div 
      style={{ 
        width: props.width || '24px', 
        height: props.height || '24px' 
      }} 
    />
  );

  return (
    <ClientOnly fallback={fallback}>
      <svg {...props} />
    </ClientOnly>
  );
}
