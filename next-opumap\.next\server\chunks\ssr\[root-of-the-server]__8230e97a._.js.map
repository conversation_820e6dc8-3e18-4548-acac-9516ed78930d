{"version": 3, "sources": [], "sections": [{"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/client.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createBrowserClient } from '@supabase/ssr';\r\n\r\nexport function createClient() {\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;AAFA;;AAIO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'; // Provider needs to be a client component to use hooks\r\n\r\nimport React, { createContext, useState, useEffect, useContext, ReactNode, useMemo } from 'react';\r\nimport { createClient } from '@/utils/supabase/client';\r\nimport { Session, User as SupabaseUser } from '@supabase/supabase-js';\r\n\r\n// Define the User type based on fields used in Profile.tsx\r\n// Ensure this matches the actual user data structure from your API\r\nexport interface User {\r\n  id?: string;\r\n  company_name?: string | null;\r\n  name?: string | null;\r\n  email?: string | null;\r\n  address?: string | null;\r\n  phone?: string | null;\r\n  website?: string | null;\r\n  employee_count?: number | string | null;\r\n  company_info_points?: string | null;\r\n  // Zusätzliche Felder für Profil-Statistiken und Darstellung\r\n  analysisCount?: number;\r\n  activityCount?: number;\r\n  createdAt?: string;\r\n  avatarUrl?: string;\r\n}\r\n\r\n// Define the shape of the context value\r\nexport interface AuthContextType { // Export the interface\r\n  user: User | null;\r\n  supabaseUser: SupabaseUser | null;\r\n  session: Session | null;\r\n  isLoading: boolean;\r\n  initialAuthDone: boolean;\r\n  isAuthReadyForDataFetch: boolean; // Add new state for data fetching readiness\r\n  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;\r\n  signUp: (email: string, password: string) => Promise<{ error: Error | null }>;\r\n  signOut: () => Promise<void>;\r\n  signInWithGoogle: () => Promise<{ error: Error | null }>;\r\n  resetPassword: (email: string) => Promise<{ error: Error | null }>;\r\n  updatePassword: (newPassword: string) => Promise<{ error: Error | null }>;\r\n  refreshSession: () => Promise<void>;\r\n  refreshUserProfile: () => Promise<void>;\r\n}\r\n\r\n// Create the context with a default value (can be null or a default object)\r\n// Using null requires careful checks in consumers, but is often cleaner.\r\nexport const AuthContext = createContext<AuthContextType | null>(null);\r\n\r\n// Define props for the provider\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);\r\n  const [session, setSession] = useState<Session | null>(null);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n  const [initialAuthDone, setInitialAuthDone] = useState<boolean>(false);\r\n  const [isPasswordResetFlow, setIsPasswordResetFlow] = useState<boolean>(false);\r\n\r\n  const supabase = useMemo(() => createClient(), []);\r\n\r\n  // Derived state to indicate if auth is ready for data fetching\r\n  const isAuthReadyForDataFetch = !isLoading && initialAuthDone && !!user;\r\n\r\n  // Handle auth state changes\r\n  useEffect(() => {\r\n    console.log(\"AuthContext: useEffect setting up auth listener. Initial state: isLoading=true, initialAuthDone=false\");\r\n    setIsLoading(true);\r\n    setInitialAuthDone(false);\r\n\r\n    const { data: authListener } = supabase.auth.onAuthStateChange(\r\n      (event, currentSession) => {\r\n        console.log(`AuthContext: onAuthStateChange triggered - event: ${event}`, \"session:\", currentSession ? 'present' : 'null');\r\n\r\n        // Handle password recovery flow\r\n        if (event === 'PASSWORD_RECOVERY') {\r\n          console.log('AuthContext: Password recovery flow detected');\r\n          setIsPasswordResetFlow(true);\r\n          return;\r\n        }\r\n\r\n        // If we're in password reset flow, don't update the user state\r\n        if (isPasswordResetFlow && (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED')) {\r\n          console.log('AuthContext: Suppressing auth state update during password reset flow');\r\n          return;\r\n        }\r\n\r\n        const currentSupabaseAuthUser = currentSession?.user || null;\r\n        setSupabaseUser(currentSupabaseAuthUser);\r\n        setSession(currentSession);\r\n\r\n        if (currentSupabaseAuthUser) {\r\n          // Set basic user information as soon as session is known\r\n          setUser({ id: currentSupabaseAuthUser.id, email: currentSupabaseAuthUser.email });\r\n          // Mark initial authentication (session check) as done\r\n          setInitialAuthDone(true);\r\n          \r\n          // DEADLOCK FIX: Move async operations out of onAuthStateChange using setTimeout\r\n          setTimeout(async () => {\r\n            try {\r\n              console.log(\"AuthContext: Fetching profile for user ID:\", currentSupabaseAuthUser.id);\r\n              const { data: profileData, error: profileError } = await supabase\r\n                .from('profiles')\r\n                .select('*')\r\n                .eq('id', currentSupabaseAuthUser.id)\r\n                .single();\r\n\r\n              if (profileError && profileError.code !== 'PGRST116') {\r\n                console.error(\"AuthContext: Error fetching user profile:\", profileError);\r\n                // User remains with basic info set above\r\n              } else if (profileData) {\r\n                console.log(\"AuthContext: Profile found:\", profileData);\r\n                const fullUser: User = {\r\n                  id: currentSupabaseAuthUser.id,\r\n                  email: currentSupabaseAuthUser.email,\r\n                  name: profileData.name,\r\n                  company_name: profileData.company_name,\r\n                  address: profileData.address,\r\n                  phone: profileData.phone,\r\n                  website: profileData.website,\r\n                  employee_count: profileData.employee_count,\r\n                  company_info_points: profileData.company_info_points,\r\n                  createdAt: profileData.created_at,\r\n                  avatarUrl: profileData.avatar_url\r\n                };\r\n                setUser(fullUser); // Update user with full profile data\r\n              } else {\r\n                console.log(\"AuthContext: No profile data found. User remains with basic info.\");\r\n                // User remains with basic info set above\r\n              }\r\n            } catch (profileCatchError) {\r\n              console.error(\"AuthContext: Error fetching profile:\", profileCatchError);\r\n              // User remains with basic info set above\r\n            }\r\n          }, 0);\r\n          \r\n        } else {\r\n          // No Supabase auth user in session - handle session refresh with setTimeout\r\n          console.log(\"AuthContext: No user in session, attempting session refresh...\");\r\n          \r\n          setTimeout(async () => {\r\n            try {\r\n              const { data: { session: refreshedSession } } = await supabase.auth.getSession();\r\n              if (refreshedSession?.user) {\r\n                console.log(\"AuthContext: Session refresh found user:\", refreshedSession.user.id);\r\n                // Don't recursively call, just update state directly\r\n                setSupabaseUser(refreshedSession.user);\r\n                setSession(refreshedSession);\r\n                setUser({ id: refreshedSession.user.id, email: refreshedSession.user.email });\r\n              } else {\r\n                console.log(\"AuthContext: Session refresh found no user\");\r\n                setUser(null);\r\n              }\r\n            } catch (refreshError) {\r\n              console.error(\"AuthContext: Error during session refresh:\", refreshError);\r\n              setUser(null);\r\n            }\r\n          }, 0);\r\n          \r\n          setInitialAuthDone(true); // Mark initial auth as done regardless\r\n        }\r\n\r\n        // Regardless of profile fetch outcome, initial loading (session check) is now complete\r\n        setIsLoading(false);\r\n        // Log final states for this event processing cycle\r\n        console.log(`AuthContext: Finished onAuthStateChange processing. Event: ${event}, User: ${!!currentSupabaseAuthUser}, InitialAuthDone: ${true}, IsLoading: ${false}`);\r\n      }\r\n    );\r\n\r\n    return () => {\r\n      console.log(\"AuthContext: Unsubscribing from onAuthStateChange.\");\r\n      authListener.subscription.unsubscribe();\r\n    };\r\n  }, [supabase]); // supabase is the correct and only dependency for setting up/tearing down the listener.\r\n\r\n  // Add logging for state changes\r\n  useEffect(() => {\r\n    console.log(\"AuthContext State Updated:\", { user, supabaseUser, session, isLoading, initialAuthDone });\r\n  }, [user, supabaseUser, session, isLoading, initialAuthDone]);\r\n\r\n  const signIn = async (email: string, password: string) => {\r\n    console.log(\"AuthContext: signIn called. Setting isLoading=true\");\r\n    setIsLoading(true); // Ensure loading is true before async operation\r\n    const { error } = await supabase.auth.signInWithPassword({ email, password });\r\n    // onAuthStateChange will handle setting isLoading to false and updating user\r\n    if (error) {\r\n      console.error(\"Sign-in error:\", error);\r\n      setIsLoading(false); // Explicitly set false if error and onAuthStateChange might not fire as expected\r\n    }\r\n    return { error };\r\n  };\r\n\r\n  const signUp = async (email: string, password: string) => {\r\n    console.log(\"AuthContext: signUp called. Setting isLoading=true\");\r\n    setIsLoading(true); // Ensure loading is true\r\n    const { error } = await supabase.auth.signUp({\r\n      email,\r\n      password,\r\n      options: {\r\n        // emailRedirectTo: `${window.location.origin}/auth/callback`,\r\n      },\r\n    });\r\n    // onAuthStateChange will handle setting isLoading to false\r\n    if (error) {\r\n      console.error(\"Sign-up error:\", error);\r\n      setIsLoading(false); // Explicitly set false if error\r\n    }\r\n    return { error };\r\n  };\r\n\r\n  const signOut = async () => {\r\n    console.log(\"AuthContext: signOut called. Resetting auth state\");\r\n    // Skip if we're in password reset flow\r\n    if (isPasswordResetFlow) {\r\n      console.log('AuthContext: Skipping sign out during password reset flow');\r\n      return;\r\n    }\r\n    \r\n    // First clear the current state\r\n    setUser(null);\r\n    setSupabaseUser(null);\r\n    setSession(null);\r\n    setInitialAuthDone(false);\r\n    setIsLoading(true);\r\n    setIsPasswordResetFlow(false);\r\n\r\n    try {\r\n      // Then sign out from Supabase\r\n      await supabase.auth.signOut();\r\n\r\n      // Clear any local storage items\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.removeItem('opumap_map_state');\r\n        // Clear any auth-related items from localStorage\r\n        Object.keys(localStorage).forEach(key => {\r\n          if (key.startsWith('sb-')) {\r\n            localStorage.removeItem(key);\r\n          }\r\n        });\r\n      }\r\n\r\n      // Reset initial auth state after a short delay to ensure cleanup\r\n      setTimeout(() => {\r\n        setInitialAuthDone(true);\r\n        setIsLoading(false);\r\n      }, 100);\r\n\r\n    } catch (error) {\r\n      console.error('Error during sign out:', error);\r\n      setInitialAuthDone(true);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const signInWithGoogle = async () => {\r\n    console.log(\"AuthContext: signInWithGoogle called. Resetting auth state\");\r\n    // Reset state before starting the OAuth flow\r\n    setUser(null);\r\n    setSupabaseUser(null);\r\n    setSession(null);\r\n    setInitialAuthDone(false);\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const { error } = await supabase.auth.signInWithOAuth({\r\n        provider: 'google',\r\n        options: {\r\n          redirectTo: `${window.location.origin}/auth/callback`,\r\n          queryParams: {\r\n            // Force the consent screen to show every time\r\n            access_type: 'offline',\r\n            prompt: 'consent',\r\n          },\r\n        },\r\n      });\r\n\r\n      if (error) {\r\n        console.error(\"Google Sign-in error:\", error);\r\n        setInitialAuthDone(true);\r\n        setIsLoading(false);\r\n        return { error };\r\n      }\r\n\r\n      // The actual authentication will be handled by the onAuthStateChange listener\r\n      // which will be triggered after the OAuth flow completes\r\n      return { error: null };\r\n    } catch (error) {\r\n      console.error(\"Unexpected error during Google sign-in:\", error);\r\n      setInitialAuthDone(true);\r\n      setIsLoading(false);\r\n      return { error: error as Error };\r\n    }\r\n  };\r\n\r\n  const resetPassword = async (email: string) => {\r\n    console.log(\"AuthContext: resetPassword called for email:\", email);\r\n    try {\r\n      // Set password reset flow flag\r\n      setIsPasswordResetFlow(true);\r\n      \r\n      // Explicitly set the recovery flow type for better handling in the callback\r\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\r\n        redirectTo: `${window.location.origin}/auth/callback?next=/reset-password&type=recovery`,\r\n      });\r\n\r\n      if (error) {\r\n        console.error(\"Password reset error in AuthContext:\", error);\r\n        setIsPasswordResetFlow(false);\r\n        throw error;\r\n      }\r\n\r\n      console.log(\"Password reset email sent successfully\");\r\n      return { error: null };\r\n    } catch (error) {\r\n      console.error(\"Password reset error:\", error);\r\n      setIsPasswordResetFlow(false);\r\n      return { error: error as Error };\r\n    }\r\n  };\r\n\r\n  const updatePassword = async (newPassword: string) => {\r\n    console.log(\"AuthContext: updatePassword called\");\r\n    try {\r\n      // Set password reset flow flag\r\n      setIsPasswordResetFlow(true);\r\n      \r\n      const { data, error } = await supabase.auth.updateUser({\r\n        password: newPassword,\r\n      });\r\n      \r\n      if (error) throw error;\r\n      \r\n      // Reset the password reset flow flag after successful update\r\n      setIsPasswordResetFlow(false);\r\n      return { error: null };\r\n      \r\n    } catch (error) {\r\n      console.error(\"Update password error:\", error);\r\n      setIsPasswordResetFlow(false);\r\n      return { error: error as Error };\r\n    }\r\n  };\r\n\r\n  const refreshSession = async () => {\r\n    // Skip refresh if we're in password reset flow\r\n    if (isPasswordResetFlow) {\r\n      console.log('AuthContext: Skipping session refresh during password reset flow');\r\n      return;\r\n    }\r\n    \r\n    console.log(\"AuthContext: refreshSession called. Setting isLoading=true\");\r\n    setIsLoading(true); // Indicate loading state\r\n\r\n    try {\r\n      const { data, error } = await supabase.auth.refreshSession();\r\n      // onAuthStateChange should ideally handle the session update and subsequent profile fetch.\r\n      // However, if the session hasn't actually changed (e.g., it was already valid and just got refreshed with the same details),\r\n      // onAuthStateChange might not fire. In such cases, we might need to manually trigger a profile update or ensure isLoading is reset.\r\n\r\n      if (error) {\r\n        console.error(\"AuthContext: Error refreshing session:\", error);\r\n        // Potentially handle specific errors, e.g., redirect to login if token is invalid\r\n        // setUser(null); // Or based on error type\r\n        // setSession(null);\r\n      } else {\r\n        console.log(\"AuthContext: Session refreshed successfully.\", data);\r\n        // If onAuthStateChange doesn't fire because the user/session ID remains the same,\r\n        // we might need to manually update the session state here or re-fetch profile if necessary.\r\n        // For now, relying on onAuthStateChange, but this is a point of attention.\r\n        // If session object itself is new, update it:\r\n        if (data.session) {\r\n          setSession(data.session);\r\n          if (data.user) {\r\n            setSupabaseUser(data.user); // Ensure supabaseUser is also updated\r\n            // If user data might have changed on the server, consider a profile refresh\r\n            // await refreshUserProfile(); // This could be an option if needed\r\n          }\r\n        }\r\n      }\r\n    } catch (e) {\r\n      console.error(\"AuthContext: Exception during refreshSession:\", e);\r\n      // setUser(null);\r\n      // setSession(null);\r\n    } finally {\r\n      // Fallback – ensure UI is not stuck\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const refreshUserProfile = async () => {\r\n    if (!supabaseUser) {\r\n      console.log(\"AuthContext: refreshUserProfile called but no supabaseUser.\");\r\n      return;\r\n    }\r\n    console.log(\"AuthContext: refreshUserProfile called for user ID:\", supabaseUser.id);\r\n    setIsLoading(true);\r\n    try {\r\n      const { data: profileData, error: profileError } = await supabase\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', supabaseUser.id)\r\n        .single();\r\n\r\n      if (profileError && profileError.code !== 'PGRST116') {\r\n        console.error(\"AuthContext: Error refreshing user profile:\", profileError);\r\n        // Potentially keep existing user data or set to basic\r\n      } else if (profileData) {\r\n        console.log(\"AuthContext: Profile refreshed successfully:\", profileData);\r\n        const fullUser: User = {\r\n          id: supabaseUser.id,\r\n          email: supabaseUser.email,\r\n          name: profileData.name,\r\n          company_name: profileData.company_name,\r\n          address: profileData.address,\r\n          phone: profileData.phone,\r\n          website: profileData.website,\r\n          employee_count: profileData.employee_count,\r\n          company_info_points: profileData.company_info_points,\r\n          createdAt: profileData.created_at,\r\n          avatarUrl: profileData.avatar_url\r\n        };\r\n        setUser(fullUser);\r\n      } else {\r\n        console.log(\"AuthContext: No profile data found on refresh. User might not have a profile yet.\");\r\n        // Keep existing user data or set to basic if appropriate\r\n        const basicUser = { id: supabaseUser.id, email: supabaseUser.email };\r\n        setUser(basicUser);\r\n      }\r\n    } catch (e) {\r\n      console.error(\"AuthContext: Exception during refreshUserProfile:\", e);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n  return (\r\n    <AuthContext.Provider value={{\r\n      user,\r\n      supabaseUser,\r\n      session,\r\n      isLoading,\r\n      initialAuthDone,\r\n      isAuthReadyForDataFetch,\r\n      signIn,\r\n      signUp,\r\n      signOut,\r\n      signInWithGoogle,\r\n      resetPassword,\r\n      updatePassword,\r\n      refreshSession,\r\n      refreshUserProfile\r\n    }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom hook to use the auth context\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === null) { // Check for null specifically\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA,cAAc,uDAAuD;;;;AA6C9D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA0B;AAO1D,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAExE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,KAAK,EAAE;IAEjD,+DAA+D;IAC/D,MAAM,0BAA0B,CAAC,aAAa,mBAAmB,CAAC,CAAC;IAEnE,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,mBAAmB;QAEnB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAC5D,CAAC,OAAO;YACN,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,OAAO,EAAE,YAAY,iBAAiB,YAAY;YAEnH,gCAAgC;YAChC,IAAI,UAAU,qBAAqB;gBACjC,QAAQ,GAAG,CAAC;gBACZ,uBAAuB;gBACvB;YACF;YAEA,+DAA+D;YAC/D,IAAI,uBAAuB,CAAC,UAAU,gBAAgB,UAAU,iBAAiB,GAAG;gBAClF,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,MAAM,0BAA0B,gBAAgB,QAAQ;YACxD,gBAAgB;YAChB,WAAW;YAEX,IAAI,yBAAyB;gBAC3B,yDAAyD;gBACzD,QAAQ;oBAAE,IAAI,wBAAwB,EAAE;oBAAE,OAAO,wBAAwB,KAAK;gBAAC;gBAC/E,sDAAsD;gBACtD,mBAAmB;gBAEnB,gFAAgF;gBAChF,WAAW;oBACT,IAAI;wBACF,QAAQ,GAAG,CAAC,8CAA8C,wBAAwB,EAAE;wBACpF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,wBAAwB,EAAE,EACnC,MAAM;wBAET,IAAI,gBAAgB,aAAa,IAAI,KAAK,YAAY;4BACpD,QAAQ,KAAK,CAAC,6CAA6C;wBAC3D,yCAAyC;wBAC3C,OAAO,IAAI,aAAa;4BACtB,QAAQ,GAAG,CAAC,+BAA+B;4BAC3C,MAAM,WAAiB;gCACrB,IAAI,wBAAwB,EAAE;gCAC9B,OAAO,wBAAwB,KAAK;gCACpC,MAAM,YAAY,IAAI;gCACtB,cAAc,YAAY,YAAY;gCACtC,SAAS,YAAY,OAAO;gCAC5B,OAAO,YAAY,KAAK;gCACxB,SAAS,YAAY,OAAO;gCAC5B,gBAAgB,YAAY,cAAc;gCAC1C,qBAAqB,YAAY,mBAAmB;gCACpD,WAAW,YAAY,UAAU;gCACjC,WAAW,YAAY,UAAU;4BACnC;4BACA,QAAQ,WAAW,qCAAqC;wBAC1D,OAAO;4BACL,QAAQ,GAAG,CAAC;wBACZ,yCAAyC;wBAC3C;oBACF,EAAE,OAAO,mBAAmB;wBAC1B,QAAQ,KAAK,CAAC,wCAAwC;oBACtD,yCAAyC;oBAC3C;gBACF,GAAG;YAEL,OAAO;gBACL,4EAA4E;gBAC5E,QAAQ,GAAG,CAAC;gBAEZ,WAAW;oBACT,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,SAAS,gBAAgB,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;wBAC9E,IAAI,kBAAkB,MAAM;4BAC1B,QAAQ,GAAG,CAAC,4CAA4C,iBAAiB,IAAI,CAAC,EAAE;4BAChF,qDAAqD;4BACrD,gBAAgB,iBAAiB,IAAI;4BACrC,WAAW;4BACX,QAAQ;gCAAE,IAAI,iBAAiB,IAAI,CAAC,EAAE;gCAAE,OAAO,iBAAiB,IAAI,CAAC,KAAK;4BAAC;wBAC7E,OAAO;4BACL,QAAQ,GAAG,CAAC;4BACZ,QAAQ;wBACV;oBACF,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CAAC,8CAA8C;wBAC5D,QAAQ;oBACV;gBACF,GAAG;gBAEH,mBAAmB,OAAO,uCAAuC;YACnE;YAEA,uFAAuF;YACvF,aAAa;YACb,mDAAmD;YACnD,QAAQ,GAAG,CAAC,CAAC,2DAA2D,EAAE,MAAM,QAAQ,EAAE,CAAC,CAAC,wBAAwB,mBAAmB,EAAE,KAAK,aAAa,EAAE,OAAO;QACtK;QAGF,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,aAAa,YAAY,CAAC,WAAW;QACvC;IACF,GAAG;QAAC;KAAS,GAAG,wFAAwF;IAExG,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAM;YAAc;YAAS;YAAW;QAAgB;IACtG,GAAG;QAAC;QAAM;QAAc;QAAS;QAAW;KAAgB;IAE5D,MAAM,SAAS,OAAO,OAAe;QACnC,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,gDAAgD;QACpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;QAC3E,6EAA6E;QAC7E,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kBAAkB;YAChC,aAAa,QAAQ,iFAAiF;QACxG;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,yBAAyB;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;YAET;QACF;QACA,2DAA2D;QAC3D,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kBAAkB;YAChC,aAAa,QAAQ,gCAAgC;QACvD;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,QAAQ,GAAG,CAAC;QACZ,uCAAuC;QACvC,IAAI,qBAAqB;YACvB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,gCAAgC;QAChC,QAAQ;QACR,gBAAgB;QAChB,WAAW;QACX,mBAAmB;QACnB,aAAa;QACb,uBAAuB;QAEvB,IAAI;YACF,8BAA8B;YAC9B,MAAM,SAAS,IAAI,CAAC,OAAO;YAE3B,gCAAgC;YAChC,uCAAmC;;YAQnC;YAEA,iEAAiE;YACjE,WAAW;gBACT,mBAAmB;gBACnB,aAAa;YACf,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mBAAmB;YACnB,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,6CAA6C;QAC7C,QAAQ;QACR,gBAAgB;QAChB,WAAW;QACX,mBAAmB;QACnB,aAAa;QAEb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;oBACrD,aAAa;wBACX,8CAA8C;wBAC9C,aAAa;wBACb,QAAQ;oBACV;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,mBAAmB;gBACnB,aAAa;gBACb,OAAO;oBAAE;gBAAM;YACjB;YAEA,8EAA8E;YAC9E,yDAAyD;YACzD,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,mBAAmB;YACnB,aAAa;YACb,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,QAAQ,GAAG,CAAC,gDAAgD;QAC5D,IAAI;YACF,+BAA+B;YAC/B,uBAAuB;YAEvB,4EAA4E;YAC5E,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,iDAAiD,CAAC;YAC1F;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,uBAAuB;gBACvB,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uBAAuB;YACvB,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,+BAA+B;YAC/B,uBAAuB;YAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;gBACrD,UAAU;YACZ;YAEA,IAAI,OAAO,MAAM;YAEjB,6DAA6D;YAC7D,uBAAuB;YACvB,OAAO;gBAAE,OAAO;YAAK;QAEvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uBAAuB;YACvB,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,iBAAiB;QACrB,+CAA+C;QAC/C,IAAI,qBAAqB;YACvB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,yBAAyB;QAE7C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,cAAc;YAC1D,2FAA2F;YAC3F,6HAA6H;YAC7H,oIAAoI;YAEpI,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;YACxD,kFAAkF;YAClF,2CAA2C;YAC3C,oBAAoB;YACtB,OAAO;gBACL,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,kFAAkF;gBAClF,4FAA4F;gBAC5F,2EAA2E;gBAC3E,8CAA8C;gBAC9C,IAAI,KAAK,OAAO,EAAE;oBAChB,WAAW,KAAK,OAAO;oBACvB,IAAI,KAAK,IAAI,EAAE;wBACb,gBAAgB,KAAK,IAAI,GAAG,sCAAsC;oBAClE,4EAA4E;oBAC5E,mEAAmE;oBACrE;gBACF;YACF;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,iBAAiB;QACjB,oBAAoB;QACtB,SAAU;YACR,oCAAoC;YACpC,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ;QACF;QACA,QAAQ,GAAG,CAAC,uDAAuD,aAAa,EAAE;QAClF,aAAa;QACb,IAAI;YACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;YAET,IAAI,gBAAgB,aAAa,IAAI,KAAK,YAAY;gBACpD,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,sDAAsD;YACxD,OAAO,IAAI,aAAa;gBACtB,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,MAAM,WAAiB;oBACrB,IAAI,aAAa,EAAE;oBACnB,OAAO,aAAa,KAAK;oBACzB,MAAM,YAAY,IAAI;oBACtB,cAAc,YAAY,YAAY;oBACtC,SAAS,YAAY,OAAO;oBAC5B,OAAO,YAAY,KAAK;oBACxB,SAAS,YAAY,OAAO;oBAC5B,gBAAgB,YAAY,cAAc;oBAC1C,qBAAqB,YAAY,mBAAmB;oBACpD,WAAW,YAAY,UAAU;oBACjC,WAAW,YAAY,UAAU;gBACnC;gBACA,QAAQ;YACV,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,yDAAyD;gBACzD,MAAM,YAAY;oBAAE,IAAI,aAAa,EAAE;oBAAE,OAAO,aAAa,KAAK;gBAAC;gBACnE,QAAQ;YACV;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,qDAAqD;QACrE,SAAU;YACR,aAAa;QACf;IACF;IAGA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAGO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,MAAM;QACpB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useEffect } from 'react'\r\n\r\n/**\r\n * Komponente, die ihre Kinder nur clientseitig rendert\r\n * Dies verhindert Hydration-Fehler bei SVGs und anderen komplexen Komponenten\r\n */\r\ntype ClientOnlyProps = {\r\n  children: React.ReactNode\r\n}\r\n\r\nexport const ClientOnly = ({ children }: ClientOnlyProps) => {\r\n  const [hasMounted, setHasMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true)\r\n  }, [])\r\n\r\n  if (!hasMounted) {\r\n    return null\r\n  }\r\n\r\n  return <>{children}</>\r\n}\r\n\r\n/**\r\n * SVG-spezifische Variante der ClientOnly Komponente \r\n * Leitet alle SVG-Props direkt weiter\r\n */\r\nexport const ClientOnlySVG = (props: React.SVGProps<SVGSVGElement>) => {\r\n  const [hasMounted, setHasMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true)\r\n  }, [])\r\n\r\n  // Nichts rendern, bis wir auf dem Client sind\r\n  if (!hasMounted) {\r\n    return null\r\n  }\r\n\r\n  return <svg {...props} />\r\n}\r\n\r\n/**\r\n * Icon-spezifische Variante der ClientOnly Komponente\r\n * Leitet alle div-Props direkt weiter und wickelt Icons ein\r\n */\r\nexport const ClientOnlyIcon = (props: React.HTMLAttributes<HTMLDivElement> & { \r\n  children: React.ReactNode,\r\n  width?: string,\r\n  height?: string \r\n}) => {\r\n  const [hasMounted, setHasMounted] = useState(false)\r\n  const { children, className, width, height, ...rest } = props\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true)\r\n  }, [])\r\n\r\n  // Nichts rendern, bis wir auf dem Client sind\r\n  if (!hasMounted) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div \r\n      className={className} \r\n      style={{ \r\n        display: 'inline-flex', \r\n        width: width || 'auto', \r\n        height: height || 'auto' \r\n      }} \r\n      {...rest}\r\n    >\r\n      {children}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAYO,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;AAMO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,8CAA8C;IAC9C,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBAAO,8OAAC;QAAK,GAAG,KAAK;;;;;;AACvB;AAMO,MAAM,iBAAiB,CAAC;IAK7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,8CAA8C;IAC9C,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,OAAO,SAAS;YAChB,QAAQ,UAAU;QACpB;QACC,GAAG,IAAI;kBAEP;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/DarkModeToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Sun, Moon } from \"lucide-react\";\r\nimport { ClientOnlyIcon } from \"@/components/ui/client-only\";\r\n\r\nexport default function DarkModeToggle() {\r\n  const { theme, setTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) return null;\r\n\r\n  return (\r\n    <button\r\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n      className=\"p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-black dark:text-white flex items-center justify-center w-10 h-10\"\r\n      title=\"Toggle Dark Mode\"\r\n    >\r\n      <ClientOnlyIcon width=\"20px\" height=\"20px\">\r\n        {theme === \"light\" ? <Sun className=\"h-5 w-5\" /> : <Moon className=\"h-5 w-5\" />}\r\n      </ClientOnlyIcon>\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;QACV,OAAM;kBAEN,cAAA,8OAAC,0IAAA,CAAA,iBAAc;YAAC,OAAM;YAAO,QAAO;sBACjC,UAAU,wBAAU,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;qCAAe,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI3E", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\nimport { Loader2 } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\r\n        outline:\r\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-10 px-4 py-2',\r\n        sm: 'h-9 rounded-md px-3',\r\n        lg: 'h-11 rounded-md px-8',\r\n        icon: 'h-10 w-10',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  (\r\n    {\r\n      className,\r\n      variant,\r\n      size,\r\n      asChild = false,\r\n      isLoading = false,\r\n      children,\r\n      disabled,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const Comp = asChild ? Slot : 'button';\r\n    \r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        disabled={isLoading || disabled}\r\n        {...props}\r\n      >\r\n        {isLoading ? (\r\n          <>\r\n            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n            {children}\r\n          </>\r\n        ) : (\r\n          children\r\n        )}\r\n      </Comp>\r\n    );\r\n  }\r\n);\r\nButton.displayName = 'Button';\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CACE,EACE,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,aAAa;QACtB,GAAG,KAAK;kBAER,0BACC;;8BACE,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAClB;;2BAGH;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"./button\";\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close asChild>\r\n          <Button variant=\"outline\" size=\"icon\" className=\"absolute top-6 right-6\">\r\n            <XIcon className=\"h-5 w-5\" />\r\n            <span className=\"sr-only\">Close</span>\r\n          </Button>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,OAAO;kCAC3B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,WAAU;;8CAC9C,8OAAC,gMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/SessionExpiredModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { createClient } from '@/utils/supabase/client';\r\n\r\n// Globale Variable, um zu verfolgen, ob der Benutzer sich absichtlich ausgeloggt hat\r\nlet intentionalLogout = false;\r\n\r\n// Funktion, die von der Logout-Komponente aufgerufen werden kann\r\nexport const setIntentionalLogout = () => {\r\n  intentionalLogout = true;\r\n  // Nach 2 Sekunden zurücksetzen, um sicherzustellen, dass die Variable nicht dauerhaft gesetzt bleibt\r\n  setTimeout(() => {\r\n    intentionalLogout = false;\r\n  }, 2000);\r\n};\r\n\r\nconst SessionExpiredModal: React.FC = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const router = useRouter();\r\n  const { signOut } = useAuth();\r\n  const supabase = createClient();\r\n\r\n  useEffect(() => {\r\n    // Listen for the custom session-expired event\r\n    const handleSessionExpired = () => {\r\n      setIsVisible(true);\r\n    };\r\n\r\n    // Listen for Supabase auth state changes\r\n    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {\r\n      if (event === 'TOKEN_REFRESHED') {\r\n        // Token was refreshed, no need to show the modal\r\n        setIsVisible(false);\r\n      } else if (event === 'SIGNED_OUT') {\r\n        // Nur das Modal anzeigen, wenn es kein absichtliches Ausloggen war\r\n        if (!intentionalLogout) {\r\n          setIsVisible(true);\r\n        }\r\n      }\r\n    });\r\n\r\n    window.addEventListener('session-expired', handleSessionExpired);\r\n\r\n    // Clean up the event listener and subscription\r\n    return () => {\r\n      window.removeEventListener('session-expired', handleSessionExpired);\r\n      subscription.unsubscribe();\r\n    };\r\n  }, [supabase]);\r\n\r\n  const handleLogin = async () => {\r\n    setIsVisible(false);\r\n    // Sign out the user to clear any stale session data\r\n    await signOut();\r\n    router.push('/login');\r\n  };\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-card rounded-lg shadow-xl max-w-md w-full overflow-hidden flex flex-col\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <svg\r\n              className=\"w-8 h-8 text-amber-500 mr-3\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth=\"2\"\r\n                d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\r\n              />\r\n            </svg>\r\n            <h2 className=\"text-xl font-semibold text-card-foreground\">\r\n              Sitzung abgelaufen\r\n            </h2>\r\n          </div>\r\n          <p className=\"text-muted-foreground mb-6\">\r\n            Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an, um fortzufahren.\r\n          </p>\r\n          <div className=\"flex justify-end\">\r\n            <button\r\n              onClick={handleLogin}\r\n              className=\"bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-5 rounded-lg transition duration-150 ease-in-out\"\r\n            >\r\n              Zum Login\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SessionExpiredModal;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,qFAAqF;AACrF,IAAI,oBAAoB;AAGjB,MAAM,uBAAuB;IAClC,oBAAoB;IACpB,qGAAqG;IACrG,WAAW;QACT,oBAAoB;IACtB,GAAG;AACL;AAEA,MAAM,sBAAgC;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,uBAAuB;YAC3B,aAAa;QACf;QAEA,yCAAyC;QACzC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClE,IAAI,UAAU,mBAAmB;gBAC/B,iDAAiD;gBACjD,aAAa;YACf,OAAO,IAAI,UAAU,cAAc;gBACjC,mEAAmE;gBACnE,IAAI,CAAC,mBAAmB;oBACtB,aAAa;gBACf;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,mBAAmB;QAE3C,+CAA+C;QAC/C,OAAO;YACL,OAAO,mBAAmB,CAAC,mBAAmB;YAC9C,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc;QAClB,aAAa;QACb,oDAAoD;QACpD,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,OAAM;0CAEN,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAY;oCACZ,GAAE;;;;;;;;;;;0CAGN,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;;;;;;;kCAI7D,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useRouter } from 'next/navigation';\r\nimport { setIntentionalLogout } from '@/components/SessionExpiredModal';\r\nimport React from 'react';\r\n\r\ninterface LogoutButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  variant?: 'default' | 'outline';\r\n  size?: 'sm' | 'lg' | 'icon' | 'md';\r\n  className?: string;\r\n  asChild?: boolean;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'default',\r\n  size = 'md',\r\n  className = '',\r\n  children,\r\n  ...props\r\n}: LogoutButtonProps) {\r\n  const { signOut } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const handleLogout = async () => {\r\n    // Markieren, dass dies ein absichtliches Ausloggen ist\r\n    setIntentionalLogout();\r\n    await signOut();\r\n    router.push('/');\r\n  };\r\n\r\n  return (\r\n    <Button\r\n      variant={variant}\r\n      size={size}\r\n      onClick={handleLogout}\r\n      className={`bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white ${className || ''}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACe;IAClB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,uDAAuD;QACvD,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS;QACT,WAAW,CAAC,iFAAiF,EAAE,aAAa,IAAI;QAC/G,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/AuthStatus.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { setIntentionalLogout } from '@/components/SessionExpiredModal';\r\nimport LogoutButton from '@/components/auth/LogoutButton';\r\nimport { Button } from '@/components/ui/button'; // Import Button component\r\n\r\nexport default function AuthStatus() {\r\n  const { user, signOut, isLoading } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const handleSignOut = async () => {\r\n    // Markieren, dass dies ein absichtliches Ausloggen ist\r\n    setIntentionalLogout();\r\n    await signOut();\r\n    router.push('/');\r\n  };\r\n\r\n  if (isLoading) {\r\n    return <div className=\"text-sm text-[var(--color-muted-foreground)]\">Laden...</div>;\r\n  }\r\n\r\n  if (!user) {\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button asChild size=\"md\" className=\"bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-sm\">\r\n          <Link\r\n            href=\"/login\"\r\n          >\r\n            Anmelden\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-4\">\r\n      <div className=\"text-sm text-[var(--color-muted-foreground)]\">\r\n        Angemeldet als <span className=\"font-medium text-[var(--color-foreground)]\">{user.name || user.email}</span>\r\n      </div>\r\n      <LogoutButton\r\n        onClick={handleSignOut}\r\n        className=\"text-sm transition-colors\"\r\n      >\r\n        Abmelden\r\n      </LogoutButton>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,mOAAiD,0BAA0B;AAP3E;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,uDAAuD;QACvD,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,qBAAO,8OAAC;YAAI,WAAU;sBAA+C;;;;;;IACvE;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAC,MAAK;gBAAK,WAAU;0BAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;8BACN;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAA+C;kCAC7C,8OAAC;wBAAK,WAAU;kCAA8C,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;;;;;;;0BAEtG,8OAAC,0IAAA,CAAA,UAAY;gBACX,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlyImage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Image, { ImageProps } from 'next/image';\r\n\r\n/**\r\n * ClientOnlyImage component ensures images are only rendered on the client\r\n * This prevents hydration errors caused by browser extensions that modify images\r\n */\r\nexport default function ClientOnlyImage(props: ImageProps) {\r\n  const [hasMounted, setHasMounted] = useState(false);\r\n  \r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  // Before client-side hydration, render a placeholder div with the same dimensions\r\n  if (!hasMounted) {\r\n    return (\r\n      <div \r\n        className={props.className}\r\n        style={{ \r\n          width: props.width ? \r\n            typeof props.width === 'number' ? `${props.width}px` : props.width \r\n            : 'auto',\r\n          height: props.height ? \r\n            typeof props.height === 'number' ? `${props.height}px` : props.height \r\n            : 'auto',\r\n          display: 'inline-block',\r\n        }} \r\n      />\r\n    );\r\n  }\r\n\r\n  // Ensure the image always has an alt prop\r\n  return <Image {...props} alt={props.alt || \"\"} />;\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,gBAAgB,KAAiB;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,kFAAkF;IAClF,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YACC,WAAW,MAAM,SAAS;YAC1B,OAAO;gBACL,OAAO,MAAM,KAAK,GAChB,OAAO,MAAM,KAAK,KAAK,WAAW,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,KAAK,GAChE;gBACJ,QAAQ,MAAM,MAAM,GAClB,OAAO,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,MAAM,GACnE;gBACJ,SAAS;YACX;;;;;;IAGN;IAEA,0CAA0C;IAC1C,qBAAO,8OAAC,6HAAA,CAAA,UAAK;QAAE,GAAG,KAAK;QAAE,KAAK,MAAM,GAAG,IAAI;;;;;;AAC7C", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\r\n// Import useState and useEffect from React\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\n// Assuming useAuth is correctly imported from your context path\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n// Assuming DarkModeToggle is correctly imported\r\nimport DarkModeToggle from '@/components/ui/DarkModeToggle';\r\n// Import necessary components from shadcn/ui - Added Header, Title, Description, Close\r\nimport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetTitle,\r\n  SheetDescription,\r\n  SheetClose, // Assuming this is now exported correctly after update\r\n} from './ui/sheet';\r\nimport { Button } from './ui/button';\r\n// Import useTheme hook\r\nimport { useTheme } from 'next-themes';\r\nimport { CheckCircle, Zap, Map, Microscope, User, Home, Menu, X, Sparkles } from 'lucide-react';\r\n// Import AuthStatus component\r\nimport AuthStatus from '@/components/auth/AuthStatus';\r\n// Import ClientOnly components\r\nimport { ClientOnlySVG, ClientOnlyIcon } from '@/components/ui/client-only';\r\nimport ClientOnlyImage from '@/components/ui/client-only/ClientOnlyImage';\r\n\r\n// Particle system for the floating navbar\r\nconst ParticleSystem: React.FC<{ isActive: boolean }> = ({ isActive }) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  \r\n  useEffect(() => {\r\n    if (!isActive) return;\r\n    \r\n    const canvas = canvasRef.current;\r\n    if (!canvas) return;\r\n    \r\n    const ctx = canvas.getContext('2d');\r\n    if (!ctx) return;\r\n    \r\n    const particles: Array<{\r\n      x: number;\r\n      y: number;\r\n      vx: number;\r\n      vy: number;\r\n      life: number;\r\n      maxLife: number;\r\n      size: number;\r\n    }> = [];\r\n    \r\n    const createParticle = (x: number, y: number) => {\r\n      particles.push({\r\n        x,\r\n        y,\r\n        vx: (Math.random() - 0.5) * 0.5,\r\n        vy: (Math.random() - 0.5) * 0.5,\r\n        life: 60,\r\n        maxLife: 60,\r\n        size: Math.random() * 2 + 1,\r\n      });\r\n    };\r\n    \r\n    const animate = () => {\r\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n      \r\n      // Update and draw particles\r\n      for (let i = particles.length - 1; i >= 0; i--) {\r\n        const p = particles[i];\r\n        p.x += p.vx;\r\n        p.y += p.vy;\r\n        p.life--;\r\n        \r\n        if (p.life <= 0) {\r\n          particles.splice(i, 1);\r\n          continue;\r\n        }\r\n        \r\n        const alpha = p.life / p.maxLife;\r\n        ctx.save();\r\n        ctx.globalAlpha = alpha * 0.6;\r\n        ctx.fillStyle = '#3b82f6';\r\n        ctx.beginPath();\r\n        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);\r\n        ctx.fill();\r\n        ctx.restore();\r\n      }\r\n      \r\n      // Randomly create new particles\r\n      if (Math.random() < 0.1 && particles.length < 20) {\r\n        createParticle(\r\n          Math.random() * canvas.width,\r\n          Math.random() * canvas.height\r\n        );\r\n      }\r\n      \r\n      requestAnimationFrame(animate);\r\n    };\r\n    \r\n    canvas.width = canvas.offsetWidth;\r\n    canvas.height = canvas.offsetHeight;\r\n    animate();\r\n    \r\n    const handleResize = () => {\r\n      canvas.width = canvas.offsetWidth;\r\n      canvas.height = canvas.offsetHeight;\r\n    };\r\n    \r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [isActive]);\r\n  \r\n  return (\r\n    <canvas\r\n      ref={canvasRef}\r\n      className=\"absolute inset-0 pointer-events-none opacity-20\"\r\n      style={{ width: '100%', height: '100%' }}\r\n    />\r\n  );\r\n};\r\n\r\n// Morphing navigation link component\r\nconst MorphLink: React.FC<{\r\n  href: string;\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  isActive: boolean;\r\n  onClick?: () => void;\r\n}> = ({ href, icon, label, isActive, onClick }) => {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  \r\n  return (\r\n    <Link\r\n      href={href}\r\n      onClick={onClick}\r\n      className={`\r\n        group relative flex items-center gap-3 px-4 py-3 rounded-2xl\r\n        transition-all duration-500 ease-out transform-gpu\r\n        ${isActive \r\n          ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-600 dark:text-blue-400 scale-105' \r\n          : 'hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 hover:scale-105'\r\n        }\r\n        ${isHovered ? 'shadow-lg shadow-blue-500/25' : ''}\r\n      `}\r\n      onMouseEnter={() => setIsHovered(true)}\r\n      onMouseLeave={() => setIsHovered(false)}\r\n    >\r\n      {/* Animated background blob */}\r\n      <div className={`\r\n        absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-purple-500/5\r\n        transition-all duration-700 ease-out transform-gpu\r\n        ${isHovered ? 'scale-110 opacity-100' : 'scale-95 opacity-0'}\r\n      `} />\r\n      \r\n      {/* Icon container with morphing effect */}\r\n      <div className={`\r\n        relative z-10 flex items-center justify-center w-8 h-8 rounded-xl\r\n        transition-all duration-500 ease-out transform-gpu\r\n        ${isActive ? 'bg-blue-500/20 text-blue-600' : 'text-muted-foreground'}\r\n        ${isHovered ? 'rotate-12 scale-110' : ''}\r\n      `}>\r\n        {icon}\r\n        {isHovered && (\r\n          <div className=\"absolute inset-0 bg-blue-500/10 rounded-xl animate-ping\" />\r\n        )}\r\n      </div>\r\n      \r\n      {/* Label with sliding effect */}\r\n      <span className={`\r\n        relative z-10 font-medium transition-all duration-500 ease-out\r\n        ${isActive ? 'text-blue-600 dark:text-blue-400' : 'text-foreground'}\r\n        ${isHovered ? 'translate-x-1' : ''}\r\n      `}>\r\n        {label}\r\n        {isHovered && (\r\n          <div className=\"absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300\" />\r\n        )}\r\n      </span>\r\n      \r\n      {/* Sparkle effect on hover */}\r\n      {isHovered && (\r\n        <div className=\"absolute top-1 right-1 text-blue-500 animate-bounce\">\r\n          <Sparkles className=\"w-3 h-3\" />\r\n        </div>\r\n      )}\r\n    </Link>\r\n  );\r\n};\r\n\r\n// Floating Command Center Navbar\r\nconst Navbar: React.FC = () => {\r\n  const { user, isLoading, initialAuthDone } = useAuth();\r\n  const isAuthenticated = !!user && !isLoading && initialAuthDone;\r\n  const pathname = usePathname();\r\n  const [isScrolled, setIsScrolled] = useState(false);\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\r\n  const navRef = useRef<HTMLElement>(null);\r\n\r\n  // Navigation items configuration\r\n  const navItems = [\r\n    { href: '/', icon: <Home className=\"w-4 h-4\" />, label: 'Home' },\r\n    { href: '/profile', icon: <User className=\"w-4 h-4\" />, label: 'Profil' },\r\n    { href: '/opuscanner', icon: <Map className=\"w-4 h-4\" />, label: 'Opuscanner' },\r\n    { href: '/opulab', icon: <Microscope className=\"w-4 h-4\" />, label: 'Opulab' },\r\n  ];\r\n\r\n  // Scroll detection for morphing effect\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsScrolled(window.scrollY > 20);\r\n    };\r\n    \r\n    window.addEventListener('scroll', handleScroll, { passive: true });\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  // Mouse tracking for interactive effects\r\n  useEffect(() => {\r\n    const handleMouseMove = (e: MouseEvent) => {\r\n      if (navRef.current) {\r\n        const rect = navRef.current.getBoundingClientRect();\r\n        setMousePosition({\r\n          x: e.clientX - rect.left,\r\n          y: e.clientY - rect.top,\r\n        });\r\n      }\r\n    };\r\n\r\n    const nav = navRef.current;\r\n    if (nav) {\r\n      nav.addEventListener('mousemove', handleMouseMove);\r\n      return () => nav.removeEventListener('mousemove', handleMouseMove);\r\n    }\r\n    \r\n    return () => {}; // Ensure all code paths return a cleanup function\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop Floating Command Center */}\r\n      <header\r\n        ref={navRef}\r\n        className={`\r\n          fixed top-4 left-1/2 transform -translate-x-1/2 z-50\r\n          transition-all duration-700 ease-out transform-gpu\r\n          ${isScrolled \r\n            ? 'scale-95 translate-y-0' \r\n            : 'scale-100 translate-y-0'\r\n          }\r\n          ${isHovered ? 'scale-105' : ''}\r\n          hidden lg:block\r\n        `}\r\n        onMouseEnter={() => setIsHovered(true)}\r\n        onMouseLeave={() => setIsHovered(false)}\r\n      >\r\n        {/* Main floating container */}\r\n        <div className={`\r\n          relative overflow-hidden rounded-3xl border border-white/20 dark:border-gray-800/50\r\n          quantum-backdrop bg-white/80 dark:bg-gray-900/80\r\n          shadow-quantum ${isHovered ? 'shadow-quantum-lg quantum-glow' : ''}\r\n          transition-all duration-700 ease-out transform-gpu morph-container\r\n          ${isHovered ? 'neural-network' : ''}\r\n        `}>\r\n          {/* Particle system background */}\r\n          <ParticleSystem isActive={isHovered} />\r\n          \r\n          {/* Interactive gradient overlay */}\r\n          <div \r\n            className=\"absolute inset-0 opacity-30 pointer-events-none\"\r\n            style={{\r\n              background: `radial-gradient(300px circle at ${mousePosition.x}px ${mousePosition.y}px, \r\n                rgba(59, 130, 246, 0.1) 0%, \r\n                rgba(147, 51, 234, 0.05) 50%, \r\n                transparent 100%)`\r\n            }}\r\n          />\r\n          \r\n          {/* Navigation content */}\r\n          <nav className=\"relative z-10 flex items-center gap-2 p-3\">\r\n                         {/* Logo section */}\r\n             <div className=\"flex items-center gap-3 px-3 py-2 rounded-2xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 hover-zone\">\r\n               <Logo className=\"h-8 w-auto transition-transform duration-300 hover:scale-110 glitch-effect\" />\r\n               {isAuthenticated && (\r\n                 <div className=\"flex items-center gap-1 px-2 py-1 rounded-full bg-green-500/20 border border-green-500/30 hologram-flicker\">\r\n                   <div className=\"w-2 h-2 bg-green-500 rounded-full quantum-pulse\" />\r\n                   <span className=\"text-xs font-medium text-green-600 dark:text-green-400 quantum-text\">AKTIV</span>\r\n                 </div>\r\n               )}\r\n             </div>\r\n            \r\n            {/* Separator with animated line */}\r\n            <div className=\"w-px h-8 bg-gradient-to-b from-transparent via-border to-transparent mx-2\" />\r\n            \r\n            {/* Navigation links */}\r\n            <div className=\"flex items-center gap-3\">\r\n              {navItems.map((item) => (\r\n                <MorphLink\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  icon={item.icon}\r\n                  label={item.label}\r\n                  isActive={pathname === item.href}\r\n                />\r\n              ))}\r\n            </div>\r\n            \r\n            {/* Separator */}\r\n            <div className=\"w-px h-8 bg-gradient-to-b from-transparent via-border to-transparent mx-2\" />\r\n            \r\n            {/* Action section */}\r\n            <div className=\"flex items-center gap-2\">\r\n              <AuthStatus />\r\n              <div className=\"p-2 rounded-xl hover:bg-white/50 dark:hover:bg-gray-800/50 transition-colors duration-300\">\r\n                <DarkModeToggle />\r\n              </div>\r\n            </div>\r\n          </nav>\r\n          \r\n          {/* Bottom glow effect */}\r\n          <div className={`\r\n            absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-3/4 h-1\r\n            bg-gradient-to-r from-transparent via-blue-500/50 to-transparent\r\n            transition-opacity duration-500\r\n            ${isHovered ? 'opacity-100' : 'opacity-0'}\r\n          `} />\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Quantum Menu */}\r\n      <header className=\"lg:hidden fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-white/20 dark:border-gray-800/50\">\r\n        <div className=\"flex items-center justify-between p-4\">\r\n          {/* Mobile logo */}\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Logo className=\"h-8 w-auto\" />\r\n            {isAuthenticated && (\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\" />\r\n            )}\r\n      </Link>\r\n\r\n          {/* Quantum menu trigger */}\r\n        <Sheet>\r\n          <SheetTrigger asChild>\r\n                             <Button \r\n                 variant=\"outline\" \r\n                 size=\"icon\" \r\n                 className=\"relative overflow-hidden rounded-2xl border-2 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:scale-110 quantum-border hover-zone\"\r\n               >\r\n                 <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 neural-network\" />\r\n                 <Menu className=\"h-5 w-5 relative z-10 glitch-effect\" />\r\n                 <span className=\"sr-only\">Quantum Menu</span>\r\n            </Button>\r\n          </SheetTrigger>\r\n            \r\n            <SheetContent \r\n              side=\"left\" \r\n              className=\"w-80 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-r border-white/20 dark:border-gray-800/50\"\r\n            >\r\n              <SheetHeader className=\"pb-6\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <Logo className=\"h-10 w-auto\" />\r\n                    <div>\r\n                      <SheetTitle className=\"text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\r\n                        Command Center\r\n                      </SheetTitle>\r\n                      <SheetDescription className=\"text-sm text-muted-foreground\">\r\n                        Quantum Navigation Interface\r\n                      </SheetDescription>\r\n                    </div>\r\n                  </div>\r\n              </div>\r\n            </SheetHeader>\r\n\r\n              {/* Mobile navigation */}\r\n              <div className=\"space-y-2 pb-6\">\r\n                {navItems.map((item) => (\r\n                  <SheetClose key={item.href} asChild>\r\n                    <MorphLink\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                    />\r\n                 </SheetClose>\r\n                ))}\r\n                </div>\r\n\r\n              {/* Mobile auth section */}\r\n              <div className=\"border-t border-white/20 dark:border-gray-800/50 pt-6 space-y-4\">\r\n                <AuthStatus />\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm font-medium\">Quantum Theme</span>\r\n                  <DarkModeToggle />\r\n              </div>\r\n            </div>\r\n          </SheetContent>\r\n        </Sheet>\r\n      </div>\r\n    </header>\r\n\r\n      {/* Spacer for mobile and desktop */}\r\n      <div className=\"h-20 lg:h-24\" />\r\n    </>\r\n  );\r\n};\r\n\r\n// Enhanced Logo Component with quantum effects\r\nfunction Logo(props: React.ImgHTMLAttributes<HTMLImageElement>) {\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const { theme, resolvedTheme } = useTheme();\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  const currentTheme = isMounted ? resolvedTheme || theme : 'light';\r\n  const src = currentTheme === 'dark'\r\n    ? '/weiss-ohne-bg-svg.svg'\r\n    : '/schwarz-ohne-bg-svg.svg';\r\n\r\n  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {\r\n    e.currentTarget.src = 'https://placehold.co/100x40/cccccc/ffffff?text=OPUS';\r\n    e.currentTarget.alt = 'OPUS Logo';\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative group\">\r\n      {/* Quantum glow effect */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\r\n      \r\n    <ClientOnlyImage\r\n      src={src}\r\n        alt=\"OPUS Command Center\"\r\n        width={200}\r\n        height={80}\r\n        className={`${props.className} object-contain relative z-10 transition-all duration-300 group-hover:brightness-110`}\r\n      onError={handleError}\r\n      priority\r\n    />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AACA,2CAA2C;AAC3C;AACA;AACA;AACA,gEAAgE;AAChE;AACA,gDAAgD;AAChD;AACA,uFAAuF;AACvF;AASA;AACA,uBAAuB;AACvB;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,8BAA8B;AAC9B;AAGA;AA3BA;;;;;;;;;;;;;AA6BA,0CAA0C;AAC1C,MAAM,iBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,MAAM,YAQD,EAAE;QAEP,MAAM,iBAAiB,CAAC,GAAW;YACjC,UAAU,IAAI,CAAC;gBACb;gBACA;gBACA,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,MAAM;gBACN,SAAS;gBACT,MAAM,KAAK,MAAM,KAAK,IAAI;YAC5B;QACF;QAEA,MAAM,UAAU;YACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C,4BAA4B;YAC5B,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBAC9C,MAAM,IAAI,SAAS,CAAC,EAAE;gBACtB,EAAE,CAAC,IAAI,EAAE,EAAE;gBACX,EAAE,CAAC,IAAI,EAAE,EAAE;gBACX,EAAE,IAAI;gBAEN,IAAI,EAAE,IAAI,IAAI,GAAG;oBACf,UAAU,MAAM,CAAC,GAAG;oBACpB;gBACF;gBAEA,MAAM,QAAQ,EAAE,IAAI,GAAG,EAAE,OAAO;gBAChC,IAAI,IAAI;gBACR,IAAI,WAAW,GAAG,QAAQ;gBAC1B,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;gBACvC,IAAI,IAAI;gBACR,IAAI,OAAO;YACb;YAEA,gCAAgC;YAChC,IAAI,KAAK,MAAM,KAAK,OAAO,UAAU,MAAM,GAAG,IAAI;gBAChD,eACE,KAAK,MAAM,KAAK,OAAO,KAAK,EAC5B,KAAK,MAAM,KAAK,OAAO,MAAM;YAEjC;YAEA,sBAAsB;QACxB;QAEA,OAAO,KAAK,GAAG,OAAO,WAAW;QACjC,OAAO,MAAM,GAAG,OAAO,YAAY;QACnC;QAEA,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,WAAW;YACjC,OAAO,MAAM,GAAG,OAAO,YAAY;QACrC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAO;;;;;;AAG7C;AAEA,qCAAqC;AACrC,MAAM,YAMD,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,SAAS;QACT,WAAW,CAAC;;;QAGV,EAAE,WACE,kGACA,uFACH;QACD,EAAE,YAAY,iCAAiC,GAAG;MACpD,CAAC;QACD,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAGjC,8OAAC;gBAAI,WAAW,CAAC;;;QAGf,EAAE,YAAY,0BAA0B,qBAAqB;MAC/D,CAAC;;;;;;0BAGD,8OAAC;gBAAI,WAAW,CAAC;;;QAGf,EAAE,WAAW,iCAAiC,wBAAwB;QACtE,EAAE,YAAY,wBAAwB,GAAG;MAC3C,CAAC;;oBACE;oBACA,2BACC,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAKnB,8OAAC;gBAAK,WAAW,CAAC;;QAEhB,EAAE,WAAW,qCAAqC,kBAAkB;QACpE,EAAE,YAAY,kBAAkB,GAAG;MACrC,CAAC;;oBACE;oBACA,2BACC,8OAAC;wBAAI,WAAU;;;;;;;;;;;;YAKlB,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK9B;AAEA,iCAAiC;AACjC,MAAM,SAAmB;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,aAAa;IAChD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEnC,iCAAiC;IACjC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAO;QAC/D;YAAE,MAAM;YAAY,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAS;QACxE;YAAE,MAAM;YAAe,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAa;QAC9E;YAAE,MAAM;YAAW,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAS;KAC9E;IAED,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO,OAAO,CAAC,qBAAqB;gBACjD,iBAAiB;oBACf,GAAG,EAAE,OAAO,GAAG,KAAK,IAAI;oBACxB,GAAG,EAAE,OAAO,GAAG,KAAK,GAAG;gBACzB;YACF;QACF;QAEA,MAAM,MAAM,OAAO,OAAO;QAC1B,IAAI,KAAK;YACP,IAAI,gBAAgB,CAAC,aAAa;YAClC,OAAO,IAAM,IAAI,mBAAmB,CAAC,aAAa;QACpD;QAEA,OAAO,KAAO,GAAG,kDAAkD;IACrE,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAC;;;UAGV,EAAE,aACE,2BACA,0BACH;UACD,EAAE,YAAY,cAAc,GAAG;;QAEjC,CAAC;gBACD,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;0BAGjC,cAAA,8OAAC;oBAAI,WAAW,CAAC;;;yBAGA,EAAE,YAAY,mCAAmC,GAAG;;UAEnE,EAAE,YAAY,mBAAmB,GAAG;QACtC,CAAC;;sCAEC,8OAAC;4BAAe,UAAU;;;;;;sCAG1B,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY,CAAC,gCAAgC,EAAE,cAAc,CAAC,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;;;iCAGjE,CAAC;4BACtB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCACf,iCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAsE;;;;;;;;;;;;;;;;;;8CAM7F,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,MAAM,KAAK,IAAI;4CACf,OAAO,KAAK,KAAK;4CACjB,UAAU,aAAa,KAAK,IAAI;2CAJ3B,KAAK,IAAI;;;;;;;;;;8CAUpB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAA,CAAA,UAAU;;;;;sDACX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAW,CAAC;;;;YAIf,EAAE,YAAY,gBAAgB,YAAY;UAC5C,CAAC;;;;;;;;;;;;;;;;;0BAKL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;;;;;;gCACf,iCACC,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAKrB,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACF,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACnB,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAI/B,8OAAC,iIAAA,CAAA,eAAY;oCACX,MAAK;oCACL,WAAU;;sDAEV,8OAAC,iIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,aAAU;oEAAC,WAAU;8EAA+F;;;;;;8EAGrH,8OAAC,iIAAA,CAAA,mBAAgB;oEAAC,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASpE,8OAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,iIAAA,CAAA,aAAU;oDAAiB,OAAO;8DACjC,cAAA,8OAAC;wDACC,MAAM,KAAK,IAAI;wDACf,MAAM,KAAK,IAAI;wDACf,OAAO,KAAK,KAAK;wDACjB,UAAU,aAAa,KAAK,IAAI;;;;;;mDALnB,KAAK,IAAI;;;;;;;;;;sDAY9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wIAAA,CAAA,UAAU;;;;;8DACX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC,0IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB;AAEA,+CAA+C;AAC/C,SAAS,KAAK,KAAgD;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAExC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,eAAe,YAAY,iBAAiB,QAAQ;IAC1D,MAAM,MAAM,iBAAiB,SACzB,2BACA;IAEJ,MAAM,cAAc,CAAC;QACnB,EAAE,aAAa,CAAC,GAAG,GAAG;QACtB,EAAE,aAAa,CAAC,GAAG,GAAG;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAEjB,8OAAC,6JAAA,CAAA,UAAe;gBACd,KAAK;gBACH,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAW,GAAG,MAAM,SAAS,CAAC,oFAAoF,CAAC;gBACrH,SAAS;gBACT,QAAQ;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 2005, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionContainer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\n\r\ninterface PageTransitionContainerProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * A container component that handles global page transition setup\r\n * This component ensures that transitions are smooth and prevents layout shifts\r\n */\r\nconst PageTransitionContainer: React.FC<PageTransitionContainerProps> = ({ \r\n  children \r\n}) => {\r\n  const pathname = usePathname();\r\n  \r\n  // Set up global transition handling\r\n  useEffect(() => {\r\n    // Clean up any lingering transition classes when pathname changes\r\n    return () => {\r\n      document.body.classList.remove('page-transition-active');\r\n    };\r\n  }, [pathname]);\r\n\r\n  return (\r\n    <div className=\"w-full h-full overflow-hidden\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageTransitionContainer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA;;;CAGC,GACD,MAAM,0BAAkE,CAAC,EACvE,QAAQ,EACT;IACC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kEAAkE;QAClE,OAAO;YACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 2045, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/registry.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState } from 'react'\r\nimport { useServerInsertedHTML } from 'next/navigation'\r\nimport { ServerStyleSheet, StyleSheetManager } from 'styled-components'\r\n\r\nexport default function StyledComponentsRegistry({ children }: { children: React.ReactNode }) {\r\n  // Only create stylesheet once with lazy initial state\r\n  // x-ref: https://reactjs.org/docs/hooks-reference.html#lazy-initial-state\r\n  const [styledComponentsStyleSheet] = useState(() => new ServerStyleSheet())\r\n\r\n  useServerInsertedHTML(() => {\r\n    const styles = styledComponentsStyleSheet.getStyleElement()\r\n    styledComponentsStyleSheet.instance.clearTag()\r\n    return <>{styles}</>\r\n  })\r\n\r\n  if (typeof window !== 'undefined') return <>{children}</>\r\n\r\n  return (\r\n    <StyleSheetManager sheet={styledComponentsStyleSheet.instance}>\r\n      {children}\r\n    </StyleSheetManager>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS,yBAAyB,EAAE,QAAQ,EAAiC;IAC1F,sDAAsD;IACtD,0EAA0E;IAC1E,MAAM,CAAC,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,IAAI,2KAAA,CAAA,mBAAgB;IAExE,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,MAAM,SAAS,2BAA2B,eAAe;QACzD,2BAA2B,QAAQ,CAAC,QAAQ;QAC5C,qBAAO;sBAAG;;IACZ;IAEA,uCAAmC;;IAAqB;IAExD,qBACE,8OAAC,2KAAA,CAAA,oBAAiB;QAAC,OAAO,2BAA2B,QAAQ;kBAC1D;;;;;;AAGP", "debugId": null}}]}