import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

// Custom Hook für clientseitiges Rendering mit Verzögerung
const useClientOnly = (delay = 300) => {
  const [isMounted, setIsMounted] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    // Nur den Loader anzeigen, wenn die Komponente nach einer Verzögerung 
    // immer noch im DOM ist (verhindert unnötiges Aufblitzen)
    const timer = setTimeout(() => {
      if (isMounted) {
        setShouldRender(true);
      }
    }, delay);
    
    return () => {
      clearTimeout(timer);
    };
  }, [delay, isMounted]);

  return shouldRender;
};

// Angepasste Wörter, die besser zu "Lade" passen (Nomen)
const loadingNouns = [
  "Daten",       // Data
  "Strategien",  // Strategies
  "Analysen",    // Analyses
  "Pläne",       // Plans
];

const Loader: React.FC = () => {
  // <PERSON>elle sicher, dass das letzte Wort das erste ist für die Animation
  const animationWords = [...loadingNouns, loadingNouns[0]];
  const shouldRender = useClientOnly();

  // Render nur, wenn wir auf dem Client sind und nach der Verzögerung
  if (!shouldRender) {
    return null;
  }

  return (
    <StyledWrapper>
      <div className="card">
        <div className="loader">
          <p>Lade</p>
          <div className="words">
            {/* Dynamische Wörter angepasst */}
            {animationWords.map((word, index) => (
              <span key={index} className="word">{word}</span>
            ))}
            {/*
              Alternativ, wenn du bei der festen Struktur bleiben willst:
              <span className="word">{loadingNouns[0]}</span>
              <span className="word">{loadingNouns[1]}</span>
              <span className="word">{loadingNouns[2]}</span>
              <span className="word">{loadingNouns[3]}</span>
              <span className="word">{loadingNouns[0]}</span> // Wiederholung für den Loop
            */}
          </div>
        </div>
      </div>
    </StyledWrapper>
  );
};

const StyledWrapper = styled.div`
  /* Kleiner Hinweis: --bg-color ist hier auf transparent gesetzt.
     Falls der Loader auf einem nicht-weißen Hintergrund erscheint,
     muss diese Variable ggf. angepasst werden, damit der
     linear-gradient Effekt im ::after Pseudo-Element gut aussieht.
     Beispiel: --bg-color: #ffffff; für weißen Hintergrund */
  .card {
    --bg-color: transparent; /* Oder die tatsächliche Hintergrundfarbe der Seite/des Containers */
    background-color: transparent;
    padding: 0;
    border-radius: 0;
  }
  .loader {
    /* Use muted-foreground for the static 'Lade' text */
    color: hsl(var(--muted-foreground));
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    /* Ggf. Schriftgröße anpassen, falls die neuen Wörter breiter sind */
    font-size: 25px;
    box-sizing: content-box;
    height: 40px;
    padding: 10px 10px;
    display: flex;
    align-items: center; /* Stellt sicher, dass "Lade" und die Wörter vertikal zentriert sind */
    border-radius: 8px;
  }
  .words {
    overflow: hidden;
    position: relative;
    /* Höhe an die Schriftgröße und line-height anpassen */
    height: 40px;
    line-height: 40px; /* Stellt sicher, dass Text vertikal zentriert ist */
    margin-left: 6px; /* Kleiner Abstand nach "Lade" */
  }
  .words::after {
    content: "";
    position: absolute;
    inset: 0;
    /* Dieser Gradient blendet die Wörter oben/unten aus.
       Passt die Prozentwerte an, wenn nötig. */
    background: linear-gradient(
      var(--bg-color) 10%,
      transparent 30%,
      transparent 70%,
      var(--bg-color) 90%
    );
    z-index: 20;
  }
  .word {
    display: block;
    height: 100%;
    /* padding-left: 6px; // Entfernt, da der Abstand jetzt am .words Container ist */
    /* Apply gradient background and clip to text */
    background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent; /* Make original text color transparent */
    /* Die Animation bleibt gleich, da wir immer noch 5 Zustände haben (4 Wörter + Rückkehr zum ersten) */
    animation: spin_4991 4s infinite;
    text-align: left; /* Stellt sicher, dass die Wörter linksbündig sind */
  }

  /* Die Keyframes bleiben unverändert, da sie 5 Schritte definieren (0% -> 100%)
     und wir 5 <span> Elemente haben (das letzte ist eine Wiederholung des ersten für den Loop) */
  @keyframes spin_4991 {
    10% {
      transform: translateY(-102%); /* Wort 1 -> Wort 2 */
    }
    25% {
      transform: translateY(-100%);
    }
    35% {
      transform: translateY(-202%); /* Wort 2 -> Wort 3 */
    }
    50% {
      transform: translateY(-200%);
    }
    60% {
      transform: translateY(-302%); /* Wort 3 -> Wort 4 */
    }
    75% {
      transform: translateY(-300%);
    }
    85% {
      transform: translateY(-402%); /* Wort 4 -> Wort 5 (Wiederholung von 1) */
    }
    100% {
      transform: translateY(-400%);
    }
  }
`;

export default Loader;
