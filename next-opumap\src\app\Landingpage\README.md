# Landingpage Component

Diese Komponente dient als Startseite für nicht eingeloggte Benutzer und bietet eine einfache Benutzeroberfläche zur Anmeldung oder Registrierung.

## Struktur

Die Landingpage-Komponente folgt dem gleichen Organisationsansatz wie andere Seitenkomponenten:

- **Hauptkomponente** - `LandingPage.tsx` enthält die Hauptlogik und das UI der Landingpage
- **Seitenspezifische Komponenten** können bei Bedarf im `components/`-Unterordner hinzugefügt werden
- **Seitenspezifische Styles** können bei Bedarf im `styles/`-Unterordner hinzugefügt werden

## Funktionalität

- Zeigt eine Willkommensnachricht an
- Bietet einen Button zur Navigation zur Login/Registrierungsseite
- Verwendet das globale Farbschema der Anwendung für konsistentes Design

## Verwendung

Die Landingpage-Komponente wird automatisch als Startseite für nicht eingeloggte Benutzer angezeigt. Sie wird in `app/page.tsx` importiert und bedingt gerendert, wenn kein Benutzer eingeloggt ist.
