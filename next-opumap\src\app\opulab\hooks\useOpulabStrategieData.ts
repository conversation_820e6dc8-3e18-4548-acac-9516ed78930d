import { useState, useEffect, useCallback } from 'react';
import { StrategyService, Strategy } from '../strategyService';

// Consider moving to a shared types file
export interface Aspect {
    id: string;
    label: string;
    checked: boolean;
}

const initialAspects: Aspect[] = [
    { id: 'marketing', label: 'Marketingstrategien', checked: false },
    { id: 'vertrieb', label: 'Vertrieb & Expansion', checked: false },
    { id: 'produkt', label: 'Produktentwicklung', checked: false },
    { id: 'kooperationen', label: 'Kooperationen', checked: false },
    { id: 'innovation', label: 'Innovationsprozesse', checked: false },
    { id: 'prozess', label: 'Prozessoptimierung', checked: false },
];

export const useOpulabStrategieData = (initialCompanyInfo: string = '') => {
    // Strategies
    const [strategies, setStrategies] = useState<Strategy[]>([]);
    const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
    const [isNewStrategy, setIsNewStrategy] = useState<boolean>(true);
    const [isLoadingStrategies, setIsLoadingStrategies] = useState<boolean>(false);

    // Form Activation
    const [isFormEnabled, setIsFormEnabled] = useState<boolean>(false);

    // Form Values
    const [companyInfo, setCompanyInfo] = useState<string>(initialCompanyInfo);
    const [personalGoal, setPersonalGoal] = useState<string>('');
    const [shortTermTasks, setShortTermTasks] = useState<string>('');
    const [longTermGoals, setLongTermGoals] = useState<string>('');
    const [shortTermValue, setShortTermValue] = useState<number>(50);
    const [longTermValue, setLongTermValue] = useState<number>(50);
    const [aspects, setAspects] = useState<Aspect[]>(initialAspects);

    // Analysis Results (tied to selected strategy)
    const [analysisResult, setAnalysisResult] = useState<string>('');
    const [showResultButton, setShowResultButton] = useState<boolean>(false);
    const [priorisierungResult, setPriorisierungResult] = useState<string>('');
    const [showPriorisierungResult, setShowPriorisierungResult] = useState<boolean>(false);
    const [zielsetzungResult, setZielsetzungResult] = useState<string>('');
    const [showZielsetzungResult, setShowZielsetzungResult] = useState<boolean>(false);

    // Update company info if initial value changes (e.g., after user loads)
     useEffect(() => {
        setCompanyInfo(initialCompanyInfo);
     }, [initialCompanyInfo]);


    // Reset form fields, aspects, and results
    const resetFormAndResults = useCallback(() => {
        setPersonalGoal('');
        setShortTermTasks('');
        setLongTermGoals('');
        setShortTermValue(50);
        setLongTermValue(50);
        setAspects(initialAspects.map(a => ({ ...a, checked: false }))); // Reset checks
        setAnalysisResult('');
        setShowResultButton(false);
        setPriorisierungResult('');
        setShowPriorisierungResult(false);
        setZielsetzungResult('');
        setShowZielsetzungResult(false);
    }, []);

    // Update form/state based on selected strategy
    const updateStateFromStrategy = useCallback((strategy: Strategy | null) => {
        if (strategy) {
            setSelectedStrategy(strategy);
        setIsNewStrategy(!strategy.id); // If no ID, it's conceptually new
            setCompanyInfo(strategy.company_info || initialCompanyInfo);

            // Update form values
            setPersonalGoal(strategy.personal_goal || '');
            setShortTermTasks(strategy.short_term_tasks || '');
            setLongTermGoals(strategy.long_term_goals || '');
            setShortTermValue(strategy.short_term_value ?? 50);
            setLongTermValue(strategy.long_term_value ?? 50);
            setAnalysisResult(strategy.analysis_result || '');
            setShowResultButton(strategy.analysis_started || false);

            // Update aspects
            if (strategy.relevant_aspects) {
                try {
                    const relevantAspectsIds = typeof strategy.relevant_aspects === 'string'
                        ? JSON.parse(strategy.relevant_aspects)
                        : strategy.relevant_aspects;
                    setAspects(currentAspects => currentAspects.map(aspect => ({
                        ...aspect,
                        checked: relevantAspectsIds.includes(aspect.id)
                    })));
                } catch (error) {
                    console.error('Error parsing relevant aspects:', error);
                    setAspects(initialAspects.map(a => ({...a, checked: false})));
                }
            } else {
                 setAspects(initialAspects.map(a => ({...a, checked: false})));
            }

            setIsFormEnabled(true);
            // Reset results from potentially different strategies if needed
            setPriorisierungResult('');
            setZielsetzungResult('');
            setShowPriorisierungResult(false);
            setShowZielsetzungResult(false);

        } else {
            // No strategy selected (e.g., after delete)
            setSelectedStrategy(null);
            setIsNewStrategy(true);
            setIsFormEnabled(false);
            resetFormAndResults();
            setCompanyInfo(initialCompanyInfo);
        }
    }, [resetFormAndResults, initialCompanyInfo]); // Ensure resetFormAndResults is stable

    // Load strategies from service
    const loadStrategies = useCallback(async (selectFirst: boolean = true): Promise<Strategy[]> => {
        setIsLoadingStrategies(true);
        try {
            const loadedStrategies = await StrategyService.getStrategies();

            // Sort strategies by created_at to ensure consistent order
            const sortedStrategies = [...loadedStrategies].sort((a, b) => {
                // Sort by created_at in ascending order (oldest first)
                const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
                const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
                return dateA - dateB;
            });

            setStrategies(sortedStrategies);

            if (selectFirst) {
                if (sortedStrategies.length > 0) {
                     updateStateFromStrategy(sortedStrategies[0]);
                 } else {
                     // Create a placeholder new strategy object if none exist
                     const placeholderNew: Strategy = {
                        strategy_name: `Strategie 1`,
                        company_info: '',
                        personal_goal: '',
                        short_term_tasks: '',
                        long_term_goals: '',
                        relevant_aspects: [],
                        short_term_value: 50,
                        long_term_value: 50,
                     };
                     updateStateFromStrategy(placeholderNew);
                     setIsFormEnabled(false); // Keep form disabled for placeholder
                 }
            }
            return sortedStrategies; // Return sorted strategies for potential chaining
        } catch (error) {
            console.error('Error loading strategies:', error);
            setStrategies([]); // Reset strategies on error
            updateStateFromStrategy(null); // Reset selection and form
            throw error; // Re-throw error for the caller to handle
        } finally {
            setIsLoadingStrategies(false);
        }
    }, [updateStateFromStrategy]); // Dependency on updateStateFromStrategy

    // Handle selecting a strategy from the list
    const handleSelectStrategy = useCallback((strategy: Strategy) => {
        updateStateFromStrategy(strategy);
    }, [updateStateFromStrategy]);

    // Handle aspect checkbox changes
    const handleAspectChange = useCallback((id: string, checked: boolean) => {
        setAspects(prevAspects =>
            prevAspects.map(aspect =>
                aspect.id === id ? { ...aspect, checked } : aspect
            )
        );
    }, []);


    // Update single strategy in the list (e.g., after save/update)
    const updateSingleStrategyInList = useCallback((updatedStrategy: Strategy) => {
        // Ensure the strategy has a strategy_name 
        const strategyWithName = {
            ...updatedStrategy,
            strategy_name: updatedStrategy.strategy_name
        };

        // Update the strategy in the list while maintaining the original order
        setStrategies(prevStrategies => {
            const updatedStrategies = prevStrategies.map(s =>
                s.id === strategyWithName.id ? strategyWithName : s
            );

            // Re-sort by created_at to maintain consistent order
            return [...updatedStrategies].sort((a, b) => {
                // Sort by created_at in ascending order (oldest first)
                const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
                const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
                return dateA - dateB;
            });
        });

        // Ensure the selected strategy in state is also the updated one
        if(selectedStrategy?.id === strategyWithName.id) {
            updateStateFromStrategy(strategyWithName);
        }
    }, [selectedStrategy?.id, updateStateFromStrategy]);


    return {
        // State & Values
        strategies,
        selectedStrategy,
        isNewStrategy,
        isLoadingStrategies,
        isFormEnabled,
        companyInfo,
        personalGoal,
        shortTermTasks,
        longTermGoals,
        shortTermValue,
        longTermValue,
        aspects,
        analysisResult,
        showResultButton,
        priorisierungResult,
        showPriorisierungResult,
        zielsetzungResult,
        showZielsetzungResult,

        // Setters (for direct form control)
        setCompanyInfo,
        setPersonalGoal,
        setShortTermTasks,
        setLongTermGoals,
        setShortTermValue,
        setLongTermValue,
        setAnalysisResult, // Allow external update (e.g., manual edit)
        setPriorisierungResult,
        setZielsetzungResult,
        setShowPriorisierungResult,
        setShowZielsetzungResult,
        setShowResultButton, // Allow external update (e.g., analysis started)

        // Actions
        loadStrategies,
        handleSelectStrategy,
        handleAspectChange,
        resetFormAndResults,
        updateStateFromStrategy, // Expose if needed externally
        updateSingleStrategyInList, // Expose for updating after API calls
        setIsFormEnabled, // Allow enabling form manually (e.g., after creating new)

    };
};
