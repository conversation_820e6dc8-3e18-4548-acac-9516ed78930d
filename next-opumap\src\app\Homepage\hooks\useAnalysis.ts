import { useState, useCallback } from "react";
import { BusinessData, AnalysisResult } from "@/types";
import { runQuickAnalysis, getQuickAnalysis } from "@/api/schnelle-analyse";
import { runDeepAnalysis, getDeepAnalysis } from "@/api/tiefe-analyse";
import type { User } from "@/contexts/AuthContext";

/**
 * Custom hook to manage analysis data and operations
 */
export const useAnalysis = (user: User | null) => {
  // Analysis state
  const [quickAnalysisData, setQuickAnalysisData] = useState<string | null>(null);
  const [quickAnalysisDate, setQuickAnalysisDate] = useState<string | null>(null);
  const [isQuickAnalysisLoading, setIsQuickAnalysisLoading] = useState<boolean>(false);

  const [deepAnalysisData, setDeepAnalysisData] = useState<string | null>(null);
  const [deepAnalysisDate, setDeepAnalysisDate] = useState<string | null>(null);
  const [isDeepAnalysisLoading, setIsDeepAnalysisLoading] = useState<boolean>(false);

  const [showAnalysisModal, setShowAnalysisModal] = useState<boolean>(false);
  const [currentAnalysisType, setCurrentAnalysisType] = useState<"schnell" | "tief">("schnell");

  // Reset analysis data when a new business is selected or deselected
  const resetAnalysisState = useCallback(() => {
    setQuickAnalysisData(null);
    setQuickAnalysisDate(null);
    setDeepAnalysisData(null);
    setDeepAnalysisDate(null);
    setIsQuickAnalysisLoading(false); // Also reset loading states
    setIsDeepAnalysisLoading(false);
  }, []);

  // Check if analysis data already exists for the selected place ID
  const checkExistingAnalysis = useCallback(async (placeId: string) => {
    // Don't fetch analysis data if we're not logged in
    if (!user) {
      console.log("Skipping analysis fetch - user not logged in");
      return;
    }

    // Wrap everything in a try-catch to prevent any errors from bubbling up
    try {
      console.log(`Fetching analysis data for place ID: ${placeId}`);

      // Use Promise.allSettled to fetch both analysis types in parallel
      // This ensures that if one fails, the other can still complete
      const [quickPromise, deepPromise] = await Promise.allSettled([
        getQuickAnalysis(placeId),
        getDeepAnalysis(placeId)
      ]);

      // Handle quick analysis result
      if (quickPromise.status === 'fulfilled') {
        const quickResult = quickPromise.value;
        console.log(`Quick analysis result for ${placeId}:`, quickResult);

        if (quickResult && quickResult.analysisContent) {
          setQuickAnalysisData(quickResult.analysisContent);
          setQuickAnalysisDate(quickResult.analysisDate);
        } else {
          setQuickAnalysisData(null);
          setQuickAnalysisDate(null);
        }
      } else {
        console.warn("Quick analysis fetch rejected:", quickPromise.reason);
        setQuickAnalysisData(null);
        setQuickAnalysisDate(null);
      }

      // Handle deep analysis result
      if (deepPromise.status === 'fulfilled') {
        const deepResult = deepPromise.value;
        console.log(`Deep analysis result for ${placeId}:`, deepResult);

        if (deepResult && deepResult.analysisContent) {
          setDeepAnalysisData(deepResult.analysisContent);
          setDeepAnalysisDate(deepResult.analysisDate);
        } else {
          setDeepAnalysisData(null);
          setDeepAnalysisDate(null);
        }
      } else {
        console.warn("Deep analysis fetch rejected:", deepPromise.reason);
        setDeepAnalysisData(null);
        setDeepAnalysisDate(null);
      }
    } catch (error) {
      // This catch block should never be reached due to Promise.allSettled,
      // but we keep it as a safety net
      console.error("Unexpected error in checkExistingAnalysis:", error);
      resetAnalysisState();
    }
  }, [user, resetAnalysisState]);

  // Handler to trigger a new quick analysis
  const handleCreateQuickAnalysis = useCallback(async (selectedBusiness: BusinessData) => {
    if (!selectedBusiness || !selectedBusiness.place_id) return; // Guard clause

    try {
      setIsQuickAnalysisLoading(true);
      const result: AnalysisResult | null = await runQuickAnalysis(selectedBusiness);
      if (result && result.analysisContent) {
        setQuickAnalysisData(result.analysisContent);
        setQuickAnalysisDate(result.analysisDate || new Date().toISOString()); // Use current date if missing
        setCurrentAnalysisType("schnell");
        setShowAnalysisModal(true); // Show modal with new data
      } else {
        console.error("Keine Schnellanalyseergebnisse erhalten");
        // Use a more user-friendly notification instead of alert if possible
        alert("Fehler bei der Erstellung der Schnellanalyse.");
      }
    } catch (error: unknown) {
      console.error("Fehler bei der Erstellung der Schnellanalyse:", error);
      const message = error instanceof Error ? error.message : "Unbekannter Fehler";
      alert("Fehler bei der Erstellung der Schnellanalyse: " + message);
    } finally {
      setIsQuickAnalysisLoading(false); // Ensure loading state is reset
    }
  }, []);

  // Handler to view existing quick analysis data
  const handleViewQuickAnalysis = useCallback(() => {
    if (quickAnalysisData) {
      setCurrentAnalysisType("schnell");
      setShowAnalysisModal(true);
    }
    // Optionally, add an else block to inform the user if no data exists
  }, [quickAnalysisData]);

  // Handler to trigger a new deep analysis
  const handleCreateDeepAnalysis = useCallback(async (selectedBusiness: BusinessData) => {
    if (!selectedBusiness || !selectedBusiness.place_id) return; // Guard clause

    try {
      setIsDeepAnalysisLoading(true);
      const result: AnalysisResult | null = await runDeepAnalysis(selectedBusiness);
      if (result && result.analysisContent) {
        setDeepAnalysisData(result.analysisContent);
        setDeepAnalysisDate(result.analysisDate || new Date().toISOString()); // Use current date if missing
        setCurrentAnalysisType("tief");
        setShowAnalysisModal(true); // Show modal with new data
      } else {
        console.error("Keine Tiefenanalyseergebnisse erhalten");
        alert("Fehler bei der Erstellung der Tiefenanalyse.");
      }
    } catch (error: unknown) {
      console.error("Fehler bei der Erstellung der Tiefenanalyse:", error);
      const message = error instanceof Error ? error.message : "Unbekannter Fehler";
      alert("Fehler bei der Erstellung der Tiefenanalyse: " + message);
    } finally {
      setIsDeepAnalysisLoading(false); // Ensure loading state is reset
    }
  }, []);

  // Handler to view existing deep analysis data
  const handleViewDeepAnalysis = useCallback(() => {
    if (deepAnalysisData) {
      setCurrentAnalysisType("tief");
      setShowAnalysisModal(true);
    }
    // Optionally, add an else block to inform the user if no data exists
  }, [deepAnalysisData]);

  return {
    quickAnalysisData,
    quickAnalysisDate,
    isQuickAnalysisLoading,
    deepAnalysisData,
    deepAnalysisDate,
    isDeepAnalysisLoading,
    showAnalysisModal,
    setShowAnalysisModal,
    currentAnalysisType,
    resetAnalysisState,
    checkExistingAnalysis,
    handleCreateQuickAnalysis,
    handleViewQuickAnalysis,
    handleCreateDeepAnalysis,
    handleViewDeepAnalysis
  };
};
