import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { findUserByEmail } from '@/lib/db'; // Import actual DB function
import { UserProfile } from '@/types'; // Import shared User type

const JWT_SECRET = process.env.JWT_SECRET; // Get secret from environment

// POST /api/auth/login - Handle user login
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { email, password } = body;

        // Validate input
        if (!email || !password) {
            return NextResponse.json({ error: "E-Mail und Passwort sind erforderlich." }, { status: 400 });
        }

        // Find user by email using imported function
        const user = await findUserByEmail(email);
        if (!user || !user.password_hash) {
            console.log("Login attempt failed: User not found or missing hash for email:", email);
            return NextResponse.json({ error: "Ungültige E-Mail oder Passwort." }, { status: 400 });
        }

        // Compare submitted password with stored hash
        const passwordValid = await bcrypt.compare(password, user.password_hash);
        if (!passwordValid) {
            console.log("Login attempt failed: Invalid password for email:", email);
            return NextResponse.json({ error: "Ungültige E-Mail oder Passwort." }, { status: 400 });
        }

        // --- Login successful ---

        // Generate JWT token
        if (!user.id) {
             console.error("Login failed: User ID is missing after successful password validation for email:", email);
             return NextResponse.json({ error: "Interner Serverfehler: Benutzerdaten unvollständig." }, { status: 500 });
        }

        // Ensure JWT_SECRET is available before signing
        if (!JWT_SECRET) {
            console.error("Login failed: JWT_SECRET is not configured.");
            return NextResponse.json({ error: "Interner Serverfehler: Konfigurationsproblem." }, { status: 500 });
        }

        const token = jwt.sign(
            { userId: user.id, email: user.email }, // Payload - include necessary non-sensitive info
            JWT_SECRET,
            { expiresIn: '1h' } // Token expiration time
        );

        // Prepare user data for response (exclude password hash)
        const responseUser: UserProfile = {
            id: user.id,
            name: user.name,
            email: user.email,
            company_name: user.company_name,
            address: user.address,
            phone: user.phone,
            website: user.website,
            employee_count: user.employee_count,
            company_info_points: user.company_info_points
            // Add other fields as needed, ensure password_hash is excluded
        };

        // Return token and user data
        return NextResponse.json({ token, user: responseUser });

    } catch (error) { // Use unknown type for error
        console.error('!!! FEHLER IN LOGIN ROUTE:', error);
         if (error instanceof SyntaxError) {
            return NextResponse.json({ error: 'Ungültige Anfrage-Daten.' }, { status: 400 });
        }
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({
            error: 'Interner Serverfehler beim Login.',
            details: message // Optionally include details in dev mode
        }, { status: 500 });
    }
}
