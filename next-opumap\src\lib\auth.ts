import jwt from 'jsonwebtoken';
import { findUserById } from './db'; // Import the DB function
import { UserProfile } from '@/types'; // Import shared User type
import { NextRequest } from 'next/server';

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
    console.error("FATAL ERROR: JWT_SECRET environment variable is not defined.");
    // In a real app, you might want to prevent the app from starting
    // or have a default fallback for development (not recommended for production)
    throw new Error("JWT_SECRET is not configured.");
}

interface JwtPayload {
    userId: string;
    iat: number; // iat is required in the decoded payload
    // Add other payload fields if you included them during signing (e.g., email, role)
}

/**
 * Verifies the JWT from the Authorization header and fetches the user.
 * @param request - The NextRequest object.
 * @returns The user profile if the token is valid and user exists, otherwise null.
 */
export async function verifyAuth(request: NextRequest): Promise<{ user: UserProfile, refreshedToken: string } | null> {
    console.log("verifyAuth: Attempting token verification..."); // Added log
    const authHeader = request.headers.get('authorization');
    console.log("verifyAuth: Authorization header:", authHeader); // Added log

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.log("verifyAuth: No or invalid Authorization header found.");
        return null;
    }

    const token = authHeader.split(' ')[1]; // Extract token after "Bearer "
    console.log("verifyAuth: Extracted token:", token ? "[Token Present]" : "[No Token Extracted]"); // Added log (don't log the token itself for security)

    if (!token) {
        console.log("verifyAuth: Token could not be extracted from header.");
        return null;
    }

    // Ensure JWT_SECRET is defined before using it
    if (!JWT_SECRET) {
        console.error("verifyAuth: FATAL - JWT_SECRET is not available at verification time."); // Added log
        return null; // Or throw an internal server error
    } else {
        console.log("verifyAuth: JWT_SECRET is available."); // Added log
    }

    try {
    console.log("verifyAuth: Attempting jwt.verify with ignoreExpiration...");
    const decoded = jwt.verify(token, JWT_SECRET, { ignoreExpiration: true });
    console.log("verifyAuth: jwt.verify successful. Decoded payload:", decoded);
    const currentTime = Math.floor(Date.now() / 1000);
    const gracePeriod = 300; // 5 minutes grace period in seconds
    if (currentTime - (decoded as JwtPayload).iat >= (3600 + gracePeriod)) {
        console.error("verifyAuth: Token expired due to inactivity (including grace period).");
        return null;
    }
    // Type guard to check if decoded is an object and has userId (allow number or string)
    if (typeof decoded !== 'object' || decoded === null || (typeof (decoded as JwtPayload).userId !== 'string' && typeof (decoded as JwtPayload).userId !== 'number')) {
         console.error("verifyAuth: Invalid token payload structure or missing/invalid userId type after verification. Payload:", decoded);
         return null;
    }

        // Now we know decoded has userId, cast it safely if needed or access directly
        // Convert userId to string as findUserById likely expects a string
        const userId = String((decoded as JwtPayload).userId);
        console.log(`verifyAuth: Decoded userId (as string): ${userId}`); // Updated log

        // Fetch user from database based on ID in token
        console.log(`verifyAuth: Attempting findUserById for string ID: ${userId}`); // Updated log
        const user = await findUserById(userId);

        if (!user) {
            console.log(`verifyAuth: User not found in DB for ID: ${userId}`); // Updated log
            return null;
        }

        // Return the user profile (excluding sensitive info like password hash)
        console.log(`verifyAuth: Token verified for user ID: ${user.id}`);
        const refreshedToken = generateToken(user);
        console.log("verifyAuth: Refreshed token:", "[Token Present]");
        return { user, refreshedToken };

    } catch (error: unknown) {
        if (error instanceof jwt.JsonWebTokenError) {
            console.error("verifyAuth: JWT Error:", error.message);
        } else {
            console.error("verifyAuth: An unexpected error occurred during token verification:", error);
        }
        return null; // Token is invalid or expired, or another error occurred
    }
}

// Optional: Function to generate JWT (might live here or in the login route)
export function generateToken(user: UserProfile): string {
    if (!user.id) {
        throw new Error("Cannot generate token for user without ID.");
    }
    const payload = { userId: user.id };
    return jwt.sign(payload, JWT_SECRET!, { expiresIn: '1h' }); // Token valid for 1 hour
}
