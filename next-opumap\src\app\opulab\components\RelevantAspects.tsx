'use client';

import InfoIconWithPopup from './InfoIconWithPopup';
import React from 'react';
import { Checkbox } from './ui';
import {
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Import styles
import { cardStyles, spacingStyles, relevantAspectsStyles, formStyles } from '../styles';

interface Aspect {
  id: string;
  label: string;
  checked: boolean;
}

interface RelevantAspectsProps {
  aspects: Aspect[];
  onAspectChange: (id: string, checked: boolean) => void;
  disabled?: boolean;
}

const RelevantAspects: React.FC<RelevantAspectsProps> = ({
  aspects,
  onAspectChange,
  disabled = false
}) => {
  return (
    <div className={cardStyles.elevated}>
      <CardHeader className={cardStyles.header}>
        <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>
          <span className="inline-block">Relevante</span>
          <span className="inline-block">Aspekte</span>
        </CardTitle>
        <p className={cardStyles.subtitle}>Wählen Sie die für Ihre Strategie wichtigen Bereiche</p>
      </CardHeader>
      <CardContent className={`${spacingStyles.betweenItems} ${cardStyles.content}`}>
        {aspects.map((aspect) => (
          <div key={aspect.id} className={relevantAspectsStyles.item}>
            <Checkbox
              id={aspect.id}
              checked={aspect.checked}
              onChange={(checked) => onAspectChange(aspect.id, checked)}
              disabled={disabled}
            />
            <label htmlFor={aspect.id} className="text-lg font-medium">
              {aspect.label}
<InfoIconWithPopup aspectLabel={aspect.label} />
            </label>
          </div>
        ))}
        <p className={formStyles.helperText}>
          Die ausgewählten Aspekte werden bei der Strategieentwicklung besonders berücksichtigt.
        </p>
      </CardContent>
    </div>
  );
};

export default RelevantAspects;
