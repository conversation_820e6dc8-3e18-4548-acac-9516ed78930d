@import "tailwindcss";
@import "tw-animate-css";

@layer base {
  :root {
    --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-heading: 'Space Grotesk', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'Fira Code', 'Fira Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    
    --radius: 0.75rem;
    --transition-default: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.5);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: var(--transition-default);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
  
  /* Selection styles */
  ::selection {
    background: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary-foreground));
  }
  
  /* Smooth scrolling for the HTML element */
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 6rem;
  }
  
  /* Focus styles */
  *:focus-visible {
    outline: 2px solid hsl(var(--primary) / 0.5);
    outline-offset: 2px;
    border-radius: var(--radius);
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
  }
  
  /* Enhanced link styles */
  .link-underline {
    position: relative;
    text-decoration: none;
    background-image: linear-gradient(currentColor, currentColor);
    background-position: 0% 100%;
    background-repeat: no-repeat;
    background-size: 0% 2px;
    transition: background-size var(--transition-smooth);
    padding-bottom: 2px;
  }
  
  .link-underline:hover {
    background-size: 100% 2px;
  }
  
  /* Animated gradient text */
  .gradient-text {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-image: linear-gradient(90deg, hsl(217 91% 60%), hsl(263 90% 61%));
    background-size: 200% auto;
    animation: gradient-shift 8s ease infinite;
  }
  
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
}

@theme inline {
  /* Radius variables mapping to the root --radius */
  --radius-sm: calc(var(--radius) - 4px); /* Adjusted based on new --radius */
  --radius-md: calc(var(--radius) - 2px); /* Adjusted based on new --radius */
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px); /* Adjusted based on new --radius */

  /* Color variables mapping to the root color variables */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground)); /* Mapped muted text color */
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground)); /* Added missing mapping */
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  /* Sidebar variables - Assuming they should map to the new theme.
     If sidebar needs different colors, define specific HSL variables for them. */
  --color-sidebar: hsl(var(--card)); /* Example: Mapping sidebar bg to card */
  --color-sidebar-foreground: hsl(var(--card-foreground)); /* Example: Mapping sidebar text to card text */
  --color-sidebar-primary: hsl(var(--primary)); /* Example: Mapping sidebar primary to main primary */
  --color-sidebar-primary-foreground: hsl(var(--primary-foreground)); /* Example: Mapping sidebar primary text */
  --color-sidebar-accent: hsl(var(--accent)); /* Example: Mapping sidebar accent */
  --color-sidebar-accent-foreground: hsl(var(--accent-foreground)); /* Example: Mapping sidebar accent text */
  --color-sidebar-border: hsl(var(--border)); /* Example: Mapping sidebar border */
  --color-sidebar-ring: hsl(var(--ring)); /* Example: Mapping sidebar ring */
}

/* ===== LIGHT THEME ===== */
:root {
  /* Base colors - More sophisticated palette */
  --background: 0 0% 98%;
  --foreground: 222.2 47% 11%;
  
  /* Card - Slightly off-white with subtle shadow */
  --card: 0 0% 100%;
  --card-foreground: 222.2 47% 11%;
  
  /* Popover */
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47% 11%;
  
  /* Primary - Professional Deep Blue */
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  
  /* Secondary - Teal */
  --secondary: 184 85% 38%;
  --secondary-foreground: 210 40% 98%;
  
  /* Muted - Subtle gray */
  --muted: 210 20% 96%;
  --muted-foreground: 215.4 16% 46%;
  
  /* Accent - Vibrant but professional */
  --accent: 262.1 83.3% 57.8%;
  --accent-foreground: 210 40% 98%;
  
  /* Destructive */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  
  /* Borders & Inputs */
  --border: 214.3 20% 90%;
  --input: 214.3 20% 90%;
  --ring: 221.2 83.2% 53.3%;
  
  /* Chart colors - Harmonious palette */
  --chart-1: 221.2 83.2% 53.3%;  /* Primary Blue */
  --chart-2: 184 85% 38%;        /* Teal */
  --chart-3: 262.1 83.3% 57.8%;  /* Purple */
  --chart-4: 346.8 77.2% 49.8%;  /* Pink */
  --chart-5: 24.6 95% 53.1%;    /* Orange */
  
  /* Custom variables */
  --radius: 0.5rem;
  --header-height: 4.5rem;
  --sidebar-width: 16rem;
  --transition-duration: 200ms;
  
  /* Improved shadows */
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 4px 12px -2px rgb(0 0 0 / 0.05), 0 2px 4px -2px rgb(0 0 0 / 0.05);
  --shadow-md: 0 10px 25px -5px rgb(0 0 0 / 0.06), 0 4px 10px -4px rgb(0 0 0 / 0.05);
  --shadow-lg: 0 20px 40px -10px rgb(0 0 0 / 0.08), 0 10px 20px -5px rgb(0 0 0 / 0.06);
  --shadow-xl: 0 25px 60px -12px rgb(0 0 0 / 0.15);
  
  /* Gradient backgrounds */
  --gradient-primary: linear-gradient(135deg, hsl(221.2 83.2% 53.3%) 0%, hsl(184 85% 38%) 100%);
  --gradient-card: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 20% 98%) 100%);
  --gradient-bg: linear-gradient(
    140deg,
    hsl(210 40% 98%) 0%,
    hsl(210 40% 98%) 30%,
    hsl(210 40% 99%) 40%,
    hsl(210 30% 98%) 50%,
    hsl(210 30% 99%) 60%,
    hsl(210 20% 98%) 70%,
    hsl(210 20% 99%) 100%
  );
  
  /* Subtle dot pattern */
  --dot-pattern: radial-gradient(
    circle at 1px 1px,
    hsl(var(--muted-foreground) / 0.1) 1px,
    transparent 0
  );
  --dot-pattern-size: 24px;
  
  /* Grid pattern */
  --grid-pattern: linear-gradient(
      to right,
      hsl(var(--muted-foreground) / 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(
      to bottom,
      hsl(var(--muted-foreground) / 0.05) 1px,
      transparent 1px
    );
  --grid-pattern-size: 24px;
}

/* ===== DARK THEME ===== */
.dark {
  /* Base colors */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  
  /* Card */
  --card: 222.2 84% 6%;
  --card-foreground: 210 40% 98%;
  
  /* Popover */
  --popover: 222.2 84% 6%;
  --popover-foreground: 210 40% 98%;
  
  /* Primary - Brighter Blue */
  --primary: 210 90% 55%;
  --primary-foreground: 210 40% 10%;
  
  /* Secondary - Brighter Green */
  --secondary: 142 76% 46%;
  --secondary-foreground: 142 76% 10%;
  
  /* Muted */
  --muted: 222.2 47% 8%;
  --muted-foreground: 215 20% 65%;
  
  /* Accent - Brighter Orange */
  --accent: 262.1 83.3% 62.8%;
  --accent-foreground: 210 40% 98%;
  
  /* Destructive */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  
  /* Borders & Inputs */
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 210 90% 55%;
  
  /* Chart colors */
  --chart-1: 217.2 91% 60%;
  --chart-2: 184 85% 42%;
  --chart-3: 262.1 83.3% 62.8%;
  --chart-4: 346.8 77.2% 54.8%;
  --chart-5: 24.6 95% 53.1%;
  
  /* Custom variables */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.4);
  --shadow: 0 4px 12px -2px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-md: 0 10px 25px -5px rgb(0 0 0 / 0.3), 0 4px 10px -4px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 20px 40px -10px rgb(0 0 0 / 0.3), 0 10px 20px -5px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 25px 60px -12px rgb(0 0 0 / 0.4);
}

/* ===== BASE LAYER ===== */
/* Custom background pattern classes */
.bg-dot-pattern {
  background-image: var(--dot-pattern);
  background-size: var(--dot-pattern-size) var(--dot-pattern-size);
}

.bg-grid-pattern {
  background-image: var(--grid-pattern);
  background-size: var(--grid-pattern-size) var(--grid-pattern-size);
}

/* Apply subtle gradient to the page background */
html {
  min-height: 100%;
  background: var(--gradient-bg);
}

/* Add subtle pattern overlay to sections */
.pattern-overlay {
  position: relative;
  z-index: 1;
}

.pattern-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--dot-pattern);
  background-size: var(--dot-pattern-size) var(--dot-pattern-size);
  opacity: 0.2;
  pointer-events: none;
  z-index: -1;
}

/* Card hover effects */
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@layer base {
  /* Apply border-box sizing and reset default border/outline colors */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }

  /* HTML element reset */
  html {
    margin: 0;
    padding: 0;
    height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
    scroll-padding-top: 5rem;
  }

  /* Body styles */
  body {
    margin: 0;
    min-height: 100vh;
    overflow-x: hidden;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: 'rlig' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    line-height: 1.6;
  }

  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: hsl(var(--foreground));
  }

  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }
  h3 { font-size: 1.75rem; }
  h4 { font-size: 1.5rem; }
  h5 { font-size: 1.25rem; }
  h6 { font-size: 1rem; }

  /* Links */
  a {
    color: hsl(var(--primary));
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    color: hsl(var(--primary) / 0.8);
    text-decoration: underline;
  }

  /* Buttons */
  button {
    cursor: pointer;
    font-family: inherit;
    font-size: 100%;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-transform: none;
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none;
  }

  /* Forms */
  input,
  button,
  select,
  optgroup,
  textarea {
    margin: 0;
    font-family: inherit;
    font-size: 100%;
    line-height: inherit;
    color: inherit;
  }

  /* Images */
  img,
  svg,
  video {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
    font-style: italic;
  }

  /* Page transitions */
  .page-transition-active {
    overflow: hidden !important;
  }
  
  /* Custom utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  .full-bleed {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }
  
  /* Custom scrollbar for Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }
  
  /* Custom scrollbar for WebKit */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.5);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: var(--transition-default);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}

/* ===== UTILITIES ===== */
@layer utilities {
  /* Animation utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-slow {
    animation: float 8s ease-in-out infinite;
  }
  
  .animate-float-fast {
    animation: float 4s ease-in-out infinite;
  }
  
  /* Gradient text */
  .text-gradient {
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  /* Custom shadows */
  .shadow-soft {
    box-shadow: var(--shadow-sm);
  }
  
  .shadow-glow {
    box-shadow: 0 0 15px -3px hsl(var(--primary) / 0.3);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 30px -5px hsl(var(--primary) / 0.4);
  }
  
  /* Custom transitions */
  .transition-slow {
    transition: var(--transition-slow);
  }
  
  .transition-default {
    transition: var(--transition-default);
  }
  
  .transition-fast {
    transition: var(--transition-fast);
  }
  
  /* Custom animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slide-up {
    from { 
      opacity: 0;
      transform: translateY(20px);
    }
    to { 
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
  }
}

/* Add styles for MarkdownEditor theming */
.dark-theme {
  --md-editor-bg: hsl(var(--background));  /* Use light mode background */
  --md-editor-color: hsl(var(--foreground));  /* Use light mode text color */
  background-color: var(--md-editor-bg) !important;
  color: var(--md-editor-color) !important;
}

.light-theme {
  --md-editor-bg: hsl(var(--background));  /* Use dark mode background */
  --md-editor-color: hsl(var(--foreground));  /* Use dark mode text color */
  background-color: var(--md-editor-bg) !important;
  color: var(--md-editor-color) !important;
  /* Target specific sub-elements if needed, e.g., .w-md-editor-preview for previews */
  .w-md-editor-preview {
    background-color: var(--md-editor-bg) !important;
    color: var(--md-editor-color) !important;
    border: none !important;  /* Remove any borders causing the highlighted box */
  }

  .w-md-editor-preview pre,
  .w-md-editor-preview code {
    background-color: var(--color-muted) !important;  /* Use a light muted color for code blocks */
    color: var(--color-foreground) !important;  /* Ensure dark text */
    border: none !important;  /* Explicitly remove borders on code elements */
    padding: 0.5rem;  /* Keep some padding for readability */
  }
}

.light-theme .wmde-markdown {
  background-color: var(--md-editor-bg) !important;
  color: var(--md-editor-color) !important;
  border: none !important;  /* Remove any borders or backgrounds */
}

.light-theme .wmde-markdown p,
.light-theme .wmde-markdown ul,
.light-theme .wmde-markdown li {
  background-color: transparent !important;  /* Ensure no background on text elements */
  color: var(--color-foreground) !important;
}

.light-theme .wmde-markdown code,
.light-theme .wmde-markdown pre {
  background-color: var(--color-muted) !important;
  color: var(--color-foreground) !important;
  border: none !important;
  padding: 0.5rem;
}

.dark-theme .wmde-markdown {
  background-color: var(--md-editor-bg) !important;
  color: var(--md-editor-color) !important;
  border: none !important;
}

.dark-theme .wmde-markdown p,
.dark-theme .wmde-markdown ul,
.dark-theme .wmde-markdown li {
  background-color: transparent !important;
  color: var(--color-foreground) !important;
}

.dark-theme .wmde-markdown code,
.dark-theme .wmde-markdown pre {
  background-color: var(--color-muted) !important;
  color: var(--color-foreground) !important;
  border: none !important;
  padding: 0.5rem;
}

.light-theme .wmde-markdown table {
  background-color: var(--color-background) !important;  /* Use light background */
  color: var(--color-foreground) !important;  /* Dark text */
  border-collapse: collapse !important;  /* Ensure tables look clean */
  border: 1px solid var(--color-border) !important;  /* Add subtle borders */
}

.light-theme .wmde-markdown th,
.light-theme .wmde-markdown td {
  background-color: var(--color-muted) !important;  /* Lighter shade for cells */
  color: var(--color-foreground) !important;  /* Dark text for readability */
  padding: 0.5rem !important;  /* Add padding for better spacing */
  border: 1px solid var(--color-border) !important;  /* Consistent borders */
}

/* ===== QUANTUM NAVBAR EFFECTS ===== */
@keyframes quantum-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.8;
  }
  50% { 
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes particle-float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% { 
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
}

@keyframes neural-network {
  0% { 
    background-position: 0% 50%;
  }
  50% { 
    background-position: 100% 50%;
  }
  100% { 
    background-position: 0% 50%;
  }
}

@keyframes quantum-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6), 0 0 60px rgba(147, 51, 234, 0.4);
  }
}

@keyframes morph-link {
  0% {
    border-radius: 1rem;
  }
  50% {
    border-radius: 2rem;
  }
  100% {
    border-radius: 1rem;
  }
}

@keyframes hologram-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.95; }
  75% { opacity: 0.98; }
}

/* Quantum UI Classes */
.quantum-pulse {
  animation: quantum-pulse 3s ease-in-out infinite;
}

.particle-float {
  animation: particle-float 4s ease-in-out infinite;
}

.neural-network {
  background: linear-gradient(-45deg, 
    rgba(59, 130, 246, 0.1), 
    rgba(147, 51, 234, 0.1), 
    rgba(59, 130, 246, 0.1), 
    rgba(147, 51, 234, 0.1)
  );
  background-size: 400% 400%;
  animation: neural-network 8s ease infinite;
}

.quantum-glow {
  animation: quantum-glow 4s ease-in-out infinite;
}

.morph-link {
  animation: morph-link 6s ease-in-out infinite;
}

.hologram-flicker {
  animation: hologram-flicker 2s ease-in-out infinite;
}

/* Enhanced backdrop blur for floating elements */
.quantum-backdrop {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* Glitch effect for special interactions */
@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

.glitch-effect:hover {
  animation: glitch 0.3s ease-in-out;
}

/* Quantum border effect */
.quantum-border {
  position: relative;
  overflow: hidden;
}

.quantum-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.quantum-border:hover::before {
  left: 100%;
}

/* Floating particle background */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-particles::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
  animation: particle-float 20s ease-in-out infinite;
}

/* Enhanced shadow effects */
.shadow-quantum {
  box-shadow: 
    0 10px 30px -10px rgba(59, 130, 246, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.shadow-quantum-lg {
  box-shadow: 
    0 20px 60px -10px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 100px -20px rgba(147, 51, 234, 0.3);
}

/* Morphing container */
.morph-container {
  border-radius: 1.5rem;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.morph-container:hover {
  border-radius: 2rem;
  transform: translateY(-2px);
}

/* Quantum text effect */
.quantum-text {
  background: linear-gradient(
    135deg,
    #3b82f6 0%,
    #8b5cf6 50%,
    #3b82f6 100%
  );
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: neural-network 3s ease infinite;
}

/* Interactive hover zones */
.hover-zone {
  position: relative;
  transition: all 0.3s ease;
}

.hover-zone::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  pointer-events: none;
}

.hover-zone:hover::after {
  width: 200px;
  height: 200px;
}

/* Responsive quantum effects */
@media (max-width: 1024px) {
  .quantum-glow {
    animation: none;
  }
  
  .neural-network {
    animation: none;
  }
  
  .particle-float {
    animation: none;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .quantum-pulse,
  .particle-float,
  .neural-network,
  .quantum-glow,
  .morph-link,
  .hologram-flicker {
    animation: none;
  }
}
