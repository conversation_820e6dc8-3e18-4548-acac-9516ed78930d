import { NextRequest, NextResponse } from 'next/server';
import pkg from 'pg';
const { Client } = pkg;
import { verifyAuth } from '@/lib/auth';
import { mapUuidToNumericId } from '@/utils/userIdMapping';

// Helper: PG client configuration
function getPgClientConfig() {
  const connectionString = process.env.POSTGRES_URL;
  if (!connectionString) {
    throw new Error('POSTGRES_URL environment variable not found.');
  }
  return {
    connectionString,
    ssl: { rejectUnauthorized: false }
  };
}

export async function POST(request: NextRequest) {
  try {
    console.log('scan-results API called');

    // Authentifizierung über verifyAuth statt manueller JWT-Verifikation
    const auth = await verifyAuth(request);
    if (!auth?.user?.id) {
      return NextResponse.json({ error: 'Nicht authentifiziert.' }, { status: 401 });
    }

    // Map the UUID to a numeric ID for database compatibility
    const userId = await mapUuidToNumericId(auth.user.id);
    console.log(`User authenticated: ${auth.user.id}, mapped to numeric ID: ${userId}`);

    // Request-Daten extrahieren
    const requestBody = await request.json();
    console.log('Request body:', requestBody);

    const { strategyId, companyIds } = requestBody;

    if (!strategyId) {
      console.error('Missing strategyId in request');
      return NextResponse.json({
        error: 'Bad Request',
        details: 'Missing strategyId'
      }, { status: 400 });
    }

    if (!Array.isArray(companyIds) || companyIds.length === 0) {
      console.error('Invalid companyIds in request:', companyIds);
      return NextResponse.json({
        error: 'Bad Request',
        details: 'Invalid or missing companyIds'
      }, { status: 400 });
    }

    // Direkte Datenbankverbindung über pg-Client statt Supabase mit Service Role Key
    const client = new Client(getPgClientConfig());
    await client.connect();
    console.log('Database client connected, querying...');

    // SQL-Abfrage mit parametrisierten Werten für Sicherheit
    // Anpassung an die neue Datenbankstruktur mit companies statt selected_companies
    const query = `
      SELECT
        sr.company_id,
        sr.result_text,
        c.name as company_name
      FROM scan_results sr
      LEFT JOIN companies c ON sr.company_id = c.id
      WHERE sr.user_id = $1
        AND sr.strategy_id = $2
        AND sr.company_id = ANY($3)
      ORDER BY sr.created_at DESC
    `;

    try {
      const result = await client.query(query, [
        userId,
        strategyId,
        companyIds
      ]);

      console.log(`Retrieved ${result.rows.length} scan results from database`);

      // Daten in das richtige Format transformieren
      const results = result.rows.map(item => ({
        selected_company_id: item.company_id, // Behalte den Namen für Abwärtskompatibilität
        company_name: item.company_name || 'Unbekanntes Unternehmen',
        result_text: item.result_text,
      }));

      // Client schließen
      await client.end();

      // Antwort mit Token-Aktualisierung
      const res = NextResponse.json({ results }, { status: 200 });
      res.headers.set('X-Refreshed-Token', auth.refreshedToken);
      return res;

    } catch (dbError) {
      await client.end();
      console.error('Database query error:', dbError);
      return NextResponse.json({
        error: 'Database query error',
        details: dbError instanceof Error ? dbError.message : 'Unknown database error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Unexpected error in scan-results API route:', error);
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}