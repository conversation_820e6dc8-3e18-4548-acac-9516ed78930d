import dotenv from 'dotenv';
import pkg from 'pg';
const { Client } = pkg;
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env.local in the parent directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '../.env.local') });

async function addSelectedCompaniesTable() {
  console.log('Starting database update to add selected_companies table...');
  const connectionString = process.env.POSTGRES_URL;

  if (!connectionString) {
    console.error('Error: POSTGRES_URL environment variable not found in .env.local');
    process.exit(1);
  }

  console.log(`Attempting to connect with URL: ${connectionString.replace(/postgres:.*@/, 'postgres://<user>:<password>@')}`);

  // Use the pg Client directly with SSL settings
  const client = new Client({
    connectionString,
    ssl: {
      rejectUnauthorized: false // Allow self-signed certificates
    }
  });

  try {
    await client.connect();
    console.log('Database client connected.');

    // Create selected_companies table
    await client.query(`
      CREATE TABLE IF NOT EXISTS selected_companies (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        place_id VARCHAR(255) NOT NULL,
        company_name VARCHAR(255) NOT NULL,
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, place_id)
      );
    `);
    console.log("Selected companies table created.");

    // Create index for faster lookups
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_selected_companies_user_id ON selected_companies(user_id);
    `);
    console.log("Index on selected_companies.user_id created.");

    console.log('Database update completed successfully.');
  } catch (error) {
    console.error('Error updating database:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the function
addSelectedCompaniesTable().catch(console.error);
