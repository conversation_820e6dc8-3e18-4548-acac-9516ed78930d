'use client';

import React from 'react';

interface NotificationProps {
  type: 'success' | 'error';
  message: string;
  details?: string;
  isVisible: boolean;
  onDismiss: () => void;
}

export const Notification: React.FC<NotificationProps> = ({
  type,
  message,
  details,
  isVisible,
  onDismiss,
}) => {
  const baseClasses = 'fixed top-4 left-1/2 transform -translate-x-1/2 p-4 rounded shadow-lg z-50 flex items-center space-x-4 transition-all duration-300 ease-in-out';
  const typeClasses = type === 'error'
    ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
    : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
  const visibilityClasses = isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-5 pointer-events-none';

  return (
    <div className={`${baseClasses} ${typeClasses} ${visibilityClasses}`}>
      <span className="flex-grow">
        {message}
        {details && (
            <span className="block text-xs italic mt-1">(Details: {details})</span>
        )}
      </span>
      <button
        onClick={onDismiss}
        aria-label="Benachrichtigung schließen"
        className="text-xl font-bold leading-none flex-shrink-0 focus:outline-none hover:text-gray-700 dark:hover:text-gray-300"
      >
        &times;
      </button>
    </div>
  );
}; 