'use client';
import React, { ChangeEvent } from 'react';

export interface StrategyOption {
  value: string;
  label: string;
  info: string;
}

interface StrategyFormProps {
  strategyOptions: StrategyOption[];
  selectedOption: string;
  onOptionChange: (value: string) => void;
}

const StrategyForm: React.FC<StrategyFormProps> = ({ strategyOptions, selectedOption, onOptionChange }) => {
  const iconSizeSmall: React.CSSProperties = { width: '1.25rem', height: '1.25rem' };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    onOptionChange(e.target.value);
  };

  return (
    <form className="space-y-3" id="strategy-form">
      {strategyOptions.map((option) => (
        <div
          key={option.value}
          className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition duration-150 ease-in-out hover:bg-muted/50 ${
            selectedOption === option.value ? 'bg-primary/10 border-primary/30' : 'border-border'
          }`}
          onClick={() => onOptionChange(option.value)}
        >
          <label className={`flex items-center cursor-pointer flex-grow ${selectedOption === option.value ? 'text-foreground font-medium' : 'text-muted-foreground'}`}>
            <input
              type="radio"
              name="strategie"
              value={option.value}
              checked={selectedOption === option.value}
              onChange={handleChange}
              className="appearance-none"
            />
            <span
              className={`w-4 h-4 border-2 rounded-full mr-3 flex-shrink-0 flex items-center justify-center ${
                selectedOption === option.value ? 'border-primary bg-primary' : 'border-input'
              }`}
            >
              {selectedOption === option.value && <span className="w-2 h-2 bg-primary-foreground rounded-full"></span>}
            </span>
            <span>{option.label}</span>
          </label>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="w-5 h-5 text-muted-foreground hover:text-primary cursor-help ml-2 flex-shrink-0"
            style={iconSizeSmall}
            onClick={(e) => {
              e.stopPropagation();
              alert(option.info);
            }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
          </svg>
        </div>
      ))}
    </form>
  );
};

export default StrategyForm;
