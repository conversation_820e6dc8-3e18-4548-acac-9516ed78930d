# Opulab Komponente

Diese Komponente dient als Hauptseite für das Opulab (Opportunity Laboratory), das die Erstellung individueller Strategien ermöglicht.

## Überblick

Die Opulab-Komponente wurde nach dem hybriden Ordnerstrukturansatz der Website refaktoriert, um die Codeorganisation zu verbessern und Best Practices zu implementieren. Die Komponente folgt nun einem modularen Ansatz mit klarer Trennung von Zuständigkeiten.

## Struktur

Die Opulab-Komponente folgt dem hybriden Organisationsansatz der Website:

- **Hauptkomponente** - `page.tsx` enthält die Authentifizierungslogik und rendert die Hauptkomponente
- **Seitenspezifische Komponente** - `OpulabStrategiePage.tsx` enthält die Hauptlogik und das UI der Opulab-Seite
- **Index-Export** - `index.tsx` exportiert die Hauptkomponente für einfachere Imports
- **Komponenten-Ordner**:
  - `components/` - Enthält seitenspezifische, größere UI-Bausteine für diese Seite (z.B. `StrategieInfo`, `StrategieBriefing`).
  - `components/ui/` - Enthält kleinere, wiederverwendbare UI-Komponenten, die *spezifisch für Opulab* sind (z.B. `TouchGlitchButton`). Globale UI-Komponenten werden aus `@/components/ui` importiert.
- **Hooks-Ordner** - `hooks/` enthält seitenspezifische Custom Hooks (z.B. `useOpulabStrategie`).
- **Styles-Ordner** - `styles/` enthält seitenspezifische Styles.
- **Service-Datei** - `strategyService.ts` enthält die API-Logik für Strategien.

## Funktionalität

- Erstellung und Verwaltung individueller Strategien (Liste, Neu, Speichern, Löschen)
- Eingabe von Unternehmensinformationen
- Definition persönlicher Zielsetzungen
- Strategiebriefing mit kurzfristigen To-Dos und langfristigen Zielen
- Priorisierung von Strategieaspekten mittels Slidern (kurzfristige Umsetzbarkeit vs. langfristiger Erfolg)
- Durchführung und Speicherung von KI-gestützten Analysen (Ergebnis in Modal bearbeitbar)
- Responsive Design für verschiedene Bildschirmgrößen
- Unterstützung für Dark Mode (über globales Theme)

## Verwendung

Die Opulab-Komponente ist über die Route `/opulab` erreichbar und erfordert eine Benutzerauthentifizierung.

## Entwicklung

Bei der Weiterentwicklung der Opulab-Komponente sollten folgende Prinzipien beachtet werden:

- **Modularer Ansatz**: Neue Funktionalitäten sollten in eigene Komponenten oder Hooks ausgelagert werden.
- **TypeScript**: Alle neuen Komponenten und Funktionen sollten mit TypeScript-Typen versehen werden.
- **Konsistente Styles**: Neue Styles sollten in der `styles/`-Ordnerstruktur organisiert oder über Tailwind-Klassen realisiert werden.
- **Zustandsmanagement**: Komplexe Logik und Zustandsverwaltung im `useOpulabStrategie`-Hook kapseln.
- **API-Interaktionen**: Alle Backend-Aufrufe über den `StrategyService` leiten.
- **Dokumentation**: Neue Funktionalitäten oder größere Änderungen sollten im README dokumentiert werden.
