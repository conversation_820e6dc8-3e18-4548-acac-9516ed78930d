// OpuScanner integration styles
export const opuScannerStyles = {
  container: 'mt-4 w-full',
  card: 'bg-card p-4 rounded-lg shadow border border-border w-full md:w-[calc(16rem+1rem+16rem)]',
  content: 'flex flex-col space-y-4',
  header: 'flex items-center mb-4',
  title: 'text-lg font-semibold text-card-foreground',  rowContainer: 'flex md:flex-row flex-col md:items-stretch md:justify-between',
  statusContainer: 'w-full md:w-1/2 md:pr-4', // Added w-full for mobile view
  statusBox: 'p-3 rounded-md h-full w-full flex items-center border dark:border-gray-700 border-blue-300 bg-blue-100 shadow-sm dark:bg-gray-800/50 dark:shadow-none', // Anpassung: stärker hervorstehender blauer Hintergrund im Light Mode
  statusText: 'text-sm text-muted-foreground',  statusIndicator: (active: boolean) => `inline-block w-3 h-3 rounded-full mr-1.5 align-middle ${active ? 'bg-green-500' : 'bg-amber-500'}`,
  toggleContainer: 'w-full md:w-1/2 mt-4 md:mt-0 flex flex-col items-center justify-center p-3 rounded-md border dark:border-gray-700 border-blue-300 bg-blue-100 shadow-sm dark:bg-gray-800/50 dark:shadow-none', // Gleiche Anpassung mit stärkerem Hintergrund im Light Mode
  toggleLabel: 'text-sm font-medium text-card-foreground mb-3',
  toggleDisabled: 'opacity-50 cursor-not-allowed'
};
