'use client';

import { useState, useEffect } from 'react';
import Image, { ImageProps } from 'next/image';

/**
 * ClientOnlyImage component ensures images are only rendered on the client
 * This prevents hydration errors caused by browser extensions that modify images
 */
export default function ClientOnlyImage(props: ImageProps) {
  const [hasMounted, setHasMounted] = useState(false);
  
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Before client-side hydration, render a placeholder div with the same dimensions
  if (!hasMounted) {
    return (
      <div 
        className={props.className}
        style={{ 
          width: props.width ? 
            typeof props.width === 'number' ? `${props.width}px` : props.width 
            : 'auto',
          height: props.height ? 
            typeof props.height === 'number' ? `${props.height}px` : props.height 
            : 'auto',
          display: 'inline-block',
        }} 
      />
    );
  }

  // Ensure the image always has an alt prop
  return <Image {...props} alt={props.alt || ""} />;
} 