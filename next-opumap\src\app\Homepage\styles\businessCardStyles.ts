import React from 'react';

// Flip card styles
export const flipCardStyles: { [key: string]: React.CSSProperties } = {
  container: {
    width: '100%', // Full width for mobile
    maxWidth: '100%', // Ensure it doesn't overflow on small screens
    height: '350px', // Default height for desktop view
    perspective: '1000px',
    marginTop: '0px',
    marginLeft: '0px', // No margin on mobile
    marginRight: '0px', // No margin on mobile
    position: 'relative',
    // Media query handled in the component with Tailwind classes
  },
  inner: {
    position: 'relative',
    width: '100%',
    height: '100%',
    textAlign: 'center',
    transition: 'transform 0.8s',
    transformStyle: 'preserve-3d',
    cursor: 'pointer',
    transformOrigin: 'center center'
  },
  front: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    backgroundColor: 'var(--color-card)',
    color: 'var(--color-card-foreground)',
    padding: '20px',
    borderRadius: '12px',
    boxSizing: 'border-box',
    boxShadow: '0 6px 12px rgba(0,0,0,0.3)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start', // Changed from space-between to allow more content
    overflow: 'auto', // Changed from hidden to auto to allow scrolling if needed
    border: '1px solid var(--color-border)'
  },
  back: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    backgroundColor: 'var(--color-card)',
    color: 'var(--color-card-foreground)',
    transform: 'rotateY(180deg)',
    padding: '20px',
    borderRadius: '12px',
    boxSizing: 'border-box',
    boxShadow: '0 6px 12px rgba(0,0,0,0.3)',
    overflowY: 'auto',
    textAlign: 'left',
    border: '1px solid var(--color-border)'
  },
  flipped: {
    transform: 'rotateY(180deg)'
  },
  title: {
    borderBottom: '2px solid var(--color-border)',
    paddingBottom: '8px',
    marginBottom: '10px',
    fontSize: '1.4rem',
    fontWeight: '600',
    textAlign: 'center',
    color: 'var(--color-card-foreground)'
  },
  imageContainer: {
    display: 'flex',
    justifyContent: 'center',
    margin: '10px 0'
  },
  image: {
    width: '120px',
    height: '120px',
    objectFit: 'cover',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0,0,0,0.3)',
    border: '1px solid var(--color-border)'
  },
  address: {
    marginTop: '10px',
    fontSize: '0.95rem',
    color: 'var(--color-muted-foreground)',
    textAlign: 'center'
  },
  hint: {
    fontSize: '11px',
    marginTop: 'auto',
    paddingTop: '10px',
    color: 'var(--color-muted-foreground)',
    fontStyle: 'italic',
    textAlign: 'center'
  },
  sectionTitle: {
    marginTop: '12px',
    marginBottom: '8px',
    fontSize: '1.1rem',
    borderBottom: '1px solid var(--color-border)',
    paddingBottom: '4px',
    color: 'var(--color-card-foreground)',
    fontWeight: '500'
  },
  infoList: {
    listStyle: 'none',
    padding: '0',
    margin: '8px 0'
  },
  infoItem: {
    padding: '3px 0',
    borderBottom: '1px dotted var(--color-border)',
    fontSize: '0.85rem',
    color: 'var(--color-card-foreground)',
    lineHeight: '1.4'
  }
};

// Placeholder card when no business is selected
export const placeholderCardStyles = {
  container: 'bg-card p-6 rounded-lg shadow border border-border text-center h-[450px] sm:h-[500px] md:h-[350px] w-full flex flex-col justify-center items-center',
  icon: 'w-16 h-16 text-primary mb-4',
  text: 'text-muted-foreground'
};

// Additional styles for business card
export const businessCardAdditionalStyles = {
  flipCardContainer: 'w-full md:mx-4 lg:mx-5 md:w-[450px] lg:w-[450px] h-[450px] sm:h-[500px] md:h-[350px]',
  scrollArea: 'pr-4',
  websiteLink: {
    color: 'hsl(var(--primary))',
    textDecoration: 'underline'
  },
  businessCardContainer: 'w-full flex justify-center md:justify-start md:w-1/3 lg:w-1/4',
};
