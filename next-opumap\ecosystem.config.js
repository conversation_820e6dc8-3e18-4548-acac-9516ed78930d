module.exports = {
  apps: [
    {
      name: 'opumap',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      instances: 'max', // Nutzt alle verfügbaren CPUs
      exec_mode: 'cluster', // Ermöglicht Load-Balancing zwischen Instanzen
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NODE_OPTIONS: '--max-old-space-size=4096', // 4GB Speicher pro Instanz
      },
      watch: false, // In Produktion nicht auf Dateiänderungen überwachen
      max_memory_restart: '1G', // Neustart bei 1GB Speicherverbrauch
      // Logs
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
    },
  ],
};
