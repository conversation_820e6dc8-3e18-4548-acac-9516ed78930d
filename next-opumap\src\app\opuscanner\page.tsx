'use client';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import CompanyInfoEditor from './components/CompanyInfoEditor';
import SelectedCompaniesList from './components/SelectedCompaniesList';
import { FooterScan } from './components/FooterScan';
import { RecognizedChancesSection } from './components/RecognizedChancesSection';
import { ScanResultModal } from './components/ScanResultModal';
import { StrategySelection } from './components/StrategySelection';
import { useCompanyInfo } from './hooks/useCompanyInfo';
import { useSelectedCompanies } from './hooks/useSelectedCompanies';
import { useStrategies } from './hooks/useStrategies';
import { useScanProcess } from './hooks/useScanProcess';
import { Notification } from '@/components/ui/notification';
import Loader from '@/components/ui/Loader';
import ScannerStatusIndicator from './components/ui/ScannerStatusIndicator';
import { SelectedCompany, StrategyOption } from './types';

// Define standard strategy options outside the component
const standardStrategyOptions: StrategyOption[] = [
    { value: 'kooperation', label: 'Kooperationsanfrage', info: 'Info zu Kooperationsanfrage' },
    { value: 'marketing', label: 'Marketingideen', info: 'Info zu Marketingideen' },
    { value: 'sales', label: 'Sales Maßnahmen', info: 'Info zu Sales Maßnahmen' },
    { value: 'material', label: 'Materialzulieferungen', info: 'Info zu Materialzulieferungen' },
    { value: 'fortbildung', label: 'Fortbildungen', info: 'Info zu Fortbildungen' }
  ];

const OpuscannerPage: React.FC = () => {
  const {} = useAuth();
  const [isClient, setIsClient] = useState(false);
  const [showLoader, setShowLoader] = useState(false);

  // Set isClient to true when component mounts
  useEffect(() => {
    setIsClient(true);

    // Nur den Loader anzeigen, wenn die Hydration länger als 300ms dauert
    const loaderTimer = setTimeout(() => {
      if (!isClient) {
        setShowLoader(true);
      }
    }, 300);

    return () => clearTimeout(loaderTimer);
  }, [isClient]);

  // --- State Hooks ---
  const [notification, setNotification] = useState<{ type: 'success' | 'error', message: string, details?: string } | null>(null);
  const [notificationVisible, setNotificationVisible] = useState<boolean>(false);
  const [showAllCompanies, setShowAllCompanies] = useState<boolean>(false);

  // --- Custom Hooks ---
  const { companyInfo, isSaving, saveError, saveSuccessMessage, handleMarkdownChange, handleSaveCompanyInfo, setSaveError, setSaveSuccessMessage } = useCompanyInfo({
    onSaveSuccess: () => showNotification('success', 'Unternehmensinformationen erfolgreich gespeichert.'),
    onSaveError: (msg) => showNotification('error', msg)
  });

  const {
    opulabStrategies,
    isLoadingOpulabStrategies,
    opulabLoadError,
    selectedOption,
    displayedScanResults,
    isLoadingDisplayedScanResults,
    handleRadioChange: originalHandleRadioChange,
  } = useStrategies();

  // Wrap handleRadioChange to include showAllCompanies filter
  const handleRadioChange = useCallback((value: string) => {
    originalHandleRadioChange(value, showAllCompanies);
  }, [originalHandleRadioChange, showAllCompanies]);

  // Callback zum Aktualisieren der Scanergebnisse nach dem Entfernen eines Unternehmens
  const updateScanResultsAfterRemoval = useCallback(() => {
    // Wenn eine Strategie ausgewählt ist, aktualisiere die Scanergebnisse
    if (selectedOption.startsWith('opulab_')) {
      handleRadioChange(selectedOption);
    }
  }, [selectedOption, handleRadioChange]);

  const { companies, isLoading: isLoadingCompanies, error: companiesError, handleRemoveCompany, setError: setCompaniesError } = useSelectedCompanies({
    onCompanyRemoved: updateScanResultsAfterRemoval
  });

  // Prepare company data for useScanProcess with full objects including place_id and company_id
  const companyIds = useMemo(() => companies.map(c => ({
    id: c.id,
    place_id: c.place_id,
    company_id: c.company_id
  })), [companies]);

  // Callback for useScanProcess to reload displayed results
  const reloadDisplayedResultsCallback = useCallback(async (strategyId: number) => {
    // Find the opulab strategy value string
    const strategyValue = `opulab_${strategyId}`;
    // Call the handleRadioChange from useStrategies hook to reload
    // This implicitly re-fetches and updates displayedScanResults
    handleRadioChange(strategyValue);
    console.log(`Reloaded displayed results for strategy ${strategyId} after scan completion.`);
  }, [handleRadioChange]);

  const {
    isScanning,
    selectedScanResult,
    isModalOpen,
    scanStatus,
    processedCount,
    totalCompanies,
    startScan,
    handleOpenModal,
    handleCloseModal,
    clearPollingState
  } = useScanProcess({
    selectedOption: selectedOption,
    companyIds: companyIds,
    onScanStartError: (msg) => showNotification('error', 'Scan konnte nicht gestartet werden', msg),
    onPollUpdate: () => { /* Optional: Zeige Fortschritt als Notification oder ignoriere hier */ },
    onPollComplete: () => showNotification('success', 'Scan abgeschlossen'),
    onPollError: (msg) => showNotification('error', 'Scan fehlgeschlagen', msg),
    onResultsLoadError: (msg) => showNotification('error', 'Ergebnisse konnten nicht geladen werden', msg),
    reloadDisplayedResults: reloadDisplayedResultsCallback, // Pass the callback
  });

  // --- Derived State ---
  const isOpulabStrategySelected = selectedOption.startsWith('opulab_');

  // --- Effects ---
  // Set client flag
  useEffect(() => {
    setIsClient(true);
  }, []);


  // --- Notification Logic ---
  const showNotification = useCallback((type: 'success' | 'error', message: string, details?: string) => {
    setNotification({ type, message, details });
    setNotificationVisible(true);
  }, []);

  useEffect(() => {
    if (!notificationVisible) return;
    const timer = setTimeout(() => {
      setNotificationVisible(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, [notificationVisible]);

  // Clear notification data after fade out
  useEffect(() => {
    if (notificationVisible) return;
    const timer = setTimeout(() => {
      setNotification(null);
      // Wenn die verworfene Notification ein Fehler/Timeout vom Polling war, setze den Hook-State zurück
      // Prüfe jetzt scanStatus
      if (scanStatus === 'error') {
          clearPollingState();
      }
      // Clear save errors/success from useCompanyInfo if they generated the notification
      if(saveError) setSaveError(null);
      if(saveSuccessMessage) setSaveSuccessMessage(null);
      // Clear company load/remove errors from useSelectedCompanies
      if(companiesError) setCompaniesError(null);
    }, 300); // Match CSS transition duration
    return () => clearTimeout(timer);
  }, [notificationVisible, scanStatus, clearPollingState, saveError, saveSuccessMessage, companiesError, setSaveError, setSaveSuccessMessage, setCompaniesError]);
  // Show loading state until client-side hydration complete
  if (!isClient) {
    // Leeres Fragment zurückgeben, bis showLoader true ist
    if (!showLoader) {
      return <></>;
    }

    return (
      <div className="flex justify-center items-center h-screen">
        <p className="text-center text-muted-foreground">Lade...</p>
      </div>
    );
  }

  // --- Render Logic ---
  return (
    <>
      {/* Notification Display */}
      {notification && (
         <Notification
           type={notification.type}
           message={notification.message}
           details={notification.details}
           isVisible={notificationVisible}
           onDismiss={() => setNotificationVisible(false)}
         />
       )}

      <div className="bg-background p-6 flex flex-col min-h-[calc(100vh-var(--header-height,0px))]" style={{ '--header-height': '64px' } as React.CSSProperties}>
        <main className="grid grid-cols-1 lg:grid-cols-4 gap-6 flex-grow mt-6">
          {/* Company Info Column - Disable and grey out if scanning */}
          <aside className={`lg:col-span-1 flex flex-col min-h-[450px] lg:min-h-0 ${isScanning ? 'opacity-50 pointer-events-none' : ''}`}>
            <CompanyInfoEditor
              companyInfo={companyInfo}
              isSaving={isSaving}
              onChange={handleMarkdownChange}
              onSave={handleSaveCompanyInfo}
              useMarkdown={true}
            />
          </aside>

          {/* Strategy Selection Column - Disable and grey out if scanning */}
          <section className={`lg:col-span-2 bg-card p-5 rounded-lg shadow-sm flex flex-col min-h-[450px] lg:min-h-0 ${isScanning ? 'opacity-50 pointer-events-none' : ''}`}>
             <StrategySelection
                 selectedOption={selectedOption}
                 onValueChange={handleRadioChange}
                 opulabStrategies={opulabStrategies}
                 standardOptions={standardStrategyOptions}
                 isLoadingStrategies={isLoadingOpulabStrategies}
                 loadingError={opulabLoadError}
                 // Disable if saving info OR scanning
                 isSaving={isSaving || isScanning}
              />
          </section>

          {/* Selected Companies Column - Disable and grey out if scanning */}
          <aside className={`lg:col-span-1 bg-card p-5 rounded-lg shadow-sm flex flex-col min-h-[450px] lg:min-h-0 ${isScanning ? 'opacity-50 pointer-events-none' : ''}`}>
            <h2 className="font-semibold text-lg text-card-foreground mb-5 border-b border-border pb-3 flex-shrink-0">
              Ausgewählte Unternehmen
            </h2>
            <div className="h-full overflow-y-auto custom-scrollbar lg:h-[400px]">
              {/* Show loading/error state for companies */}
              {isLoadingCompanies && <p className="text-muted-foreground p-4">Lade Unternehmen...</p>}
              {companiesError && <p className="text-red-500 p-4">{companiesError}</p>}
              {!isLoadingCompanies && !companiesError && (
                <SelectedCompaniesList
                  companies={companies}
                  isLoading={false} // Already handled loading state above
                  onRemoveCompany={(company: SelectedCompany) => {
                      // Prevent removal if scan is in progress
                      if (isScanning) return;
                      handleRemoveCompany(company)
                          .then(() => showNotification('success', 'Unternehmen entfernt.'))
                          .catch(() => showNotification('error', 'Fehler beim Entfernen'));
                  }}
                />
              )}
            </div>
          </aside>
        </main>

        {/* Footer with Scan Button */}
        <footer className="mt-8 flex justify-center items-center flex-shrink-0">
          <FooterScan
            isScanning={isScanning || isSaving} // Button auch deaktivieren, wenn Info gespeichert wird
            onStartScan={startScan} // Directly use startScan from the hook
            hasSelectedStrategy={isOpulabStrategySelected} // Use derived state
            hasSelectedCompanies={companies.length > 0}
            scanProgressStatus={scanStatus} // Übergebe den neuen Status
          />
        </footer>

        {/* Loading Indicator Area (verschoben, wird nur angezeigt, wenn isScanning true ist) */}
        {isScanning && (
            <div className="flex flex-col items-center justify-center mt-4 mb-4">
                <Loader />
                <ScannerStatusIndicator
                    status={scanStatus}
                    processedCount={processedCount}
                    totalCompanies={totalCompanies}
                />
            </div>
        )}

        {/* Recognized Chances Section */}
        <div className="mt-8 w-full lg:w-1/2 mx-auto">
           <RecognizedChancesSection
              scanResults={displayedScanResults}
              // isLoading wird jetzt durch isScanning ODER isLoadingDisplayedScanResults bestimmt
              isLoading={isLoadingDisplayedScanResults}
              onResultClick={handleOpenModal} // Use modal handler from hook
              showAllCompanies={showAllCompanies}
              onToggleChange={(e) => {
                const newShowAllCompanies = e.target.checked;
                setShowAllCompanies(newShowAllCompanies);
                if (selectedOption.startsWith('opulab_')) {
                  originalHandleRadioChange(selectedOption, newShowAllCompanies);
                }
              }}
           />
        </div>
      </div>

      {/* Scan Result Modal */}
      <ScanResultModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        result={selectedScanResult}
      />
    </>
  );
};

const OpuscannerPageWithProtection = () => {
  return (
    <ProtectedRoute>
      <OpuscannerPage />
    </ProtectedRoute>
  );
};

export default OpuscannerPageWithProtection;
