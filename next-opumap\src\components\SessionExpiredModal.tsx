'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { createClient } from '@/utils/supabase/client';

// Globale Variable, um zu verfolgen, ob der Benutzer sich absichtlich ausgeloggt hat
let intentionalLogout = false;

// Funktion, die von der Logout-Komponente aufgerufen werden kann
export const setIntentionalLogout = () => {
  intentionalLogout = true;
  // Nach 2 Sekunden zurücksetzen, um sicherzustellen, dass die Variable nicht dauerhaft gesetzt bleibt
  setTimeout(() => {
    intentionalLogout = false;
  }, 2000);
};

const SessionExpiredModal: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();
  const { signOut } = useAuth();
  const supabase = createClient();

  useEffect(() => {
    // Listen for the custom session-expired event
    const handleSessionExpired = () => {
      setIsVisible(true);
    };

    // Listen for Supabase auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {
      if (event === 'TOKEN_REFRESHED') {
        // Token was refreshed, no need to show the modal
        setIsVisible(false);
      } else if (event === 'SIGNED_OUT') {
        // Nur das Modal anzeigen, wenn es kein absichtliches Ausloggen war
        if (!intentionalLogout) {
          setIsVisible(true);
        }
      }
    });

    window.addEventListener('session-expired', handleSessionExpired);

    // Clean up the event listener and subscription
    return () => {
      window.removeEventListener('session-expired', handleSessionExpired);
      subscription.unsubscribe();
    };
  }, [supabase]);

  const handleLogin = async () => {
    setIsVisible(false);
    // Sign out the user to clear any stale session data
    await signOut();
    router.push('/login');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl max-w-md w-full overflow-hidden flex flex-col">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <svg
              className="w-8 h-8 text-amber-500 mr-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <h2 className="text-xl font-semibold text-card-foreground">
              Sitzung abgelaufen
            </h2>
          </div>
          <p className="text-muted-foreground mb-6">
            Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an, um fortzufahren.
          </p>
          <div className="flex justify-end">
            <button
              onClick={handleLogin}
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-5 rounded-lg transition duration-150 ease-in-out"
            >
              Zum Login
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionExpiredModal;
