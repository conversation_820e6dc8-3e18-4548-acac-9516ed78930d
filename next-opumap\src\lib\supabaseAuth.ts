import { createClient } from '@/utils/supabase/server';
import { NextResponse } from 'next/server';

/**
 * Verifies the Supabase Auth session from the request cookies.
 * @param request - The NextRequest object.
 * @returns The user profile if the session is valid and user exists, otherwise null.
 */
export async function verifySupabaseAuth() {
  try {
    // Create a Supabase client using cookies from the request
    const supabase = await createClient();

    // Get the authenticated user - this is the recommended approach
    // that authenticates the data by contacting the Supabase Auth server
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting authenticated user:", userError);
      return null;
    }

    if (!user) {
      console.log("No authenticated user found");
      return null;
    }

    // Get the current session for additional session data if needed
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("Error getting session:", sessionError);
      // We can still continue with just the user data
    }

    if (!session) {
      console.error("No active session found despite having a user");

      // Try to refresh the session
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError || !refreshData.session) {
        console.error("Failed to refresh session:", refreshError);
        return null;
      }

      console.log("Session refreshed successfully");
    } else {
      // Log session information for debugging
      console.log(`Session found for user ${user.id}, expires at: ${new Date(session.expires_at! * 1000).toISOString()}`);

      // Check if session is about to expire (within 5 minutes)
      const expiresAt = session.expires_at! * 1000; // convert to milliseconds
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;

      if (expiresAt - now < fiveMinutes) {
        console.log("Session is about to expire, refreshing...");
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

        if (refreshError) {
          console.error("Error refreshing session:", refreshError);
        } else if (refreshData.session) {
          console.log("Session refreshed successfully");
        }
      }
    }

    // Get the user profile from the profiles table
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error("Error fetching user profile:", profileError);
      // We can still continue with just the user data
    }

    // Combine Supabase user data with profile data
    const userData = {
      id: user.id,
      email: user.email,
      ...(profileData || {})
    };

    return {
      user: userData,
      session: session || null
    };
  } catch (error) {
    console.error("Error in verifySupabaseAuth:", error);
    return null;
  }
}

/**
 * Creates a JSON response with the appropriate headers for Supabase Auth.
 * @param data - The data to include in the response.
 * @param status - The HTTP status code.
 * @returns A NextResponse object.
 */
export function createAuthResponse(data: unknown, status: number = 200) {
  return NextResponse.json(data, { status });
}
