# Profile Component

Diese Komponente dient zur Anzeige und Bearbeitung des Benutzerprofils und der Unternehmensinformationen.

## Struktur

Die Profile-Komponente folgt dem gleichen Organisationsansatz wie andere Seitenkomponenten:

- **Hauptkomponente** - `Profile.tsx` enthält die Hauptlogik und das UI der Profilseite
- **Seitenspezifische Komponenten** können bei Bedarf im `components/`-Unterordner hinzugefügt werden
- **Seitenspezifische Styles** können bei Bedarf im `styles/`-Unterordner hinzugefügt werden
- **Seitenspezifische Hooks** können bei Bedarf im `hooks/`-Unterordner hinzugefügt werden

## Funktionalität

- Anzeige und Bearbeitung von Unternehmensinformationen
- Speichern der Profiländerungen in der Datenbank
- Durchführung einer KI-basierten Unternehmensanalyse
- Responsive Design für verschiedene Bildschirmgrößen
- Unterstützung für Dark Mode
- Anzeige von Erfolgs- und Fehlermeldungen

## API-Integration

Die Komponente kommuniziert mit folgenden API-Endpunkten:
- `/api/profile` - Für das Speichern der Profilinformationen
- Perplexity API (über `runAnalysis`) - Für die KI-basierte Unternehmensanalyse

## Datenstruktur

Die Komponente verwendet die folgenden Hauptdatenfelder:
- Unternehmensname
- Ansprechpartner Name
- Email
- Adresse
- Telefonnummer
- Webseite
- Mitarbeiteranzahl
- Unternehmensinformationen (Stichpunkte)

## Verwendung

Die Profile-Komponente wird in `app/profile/page.tsx` importiert und als Hauptkomponente der Profilseite verwendet. Sie ist über die Route `/profile` erreichbar und sollte nur für authentifizierte Benutzer zugänglich sein.
