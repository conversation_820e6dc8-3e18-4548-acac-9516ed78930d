import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from "@/lib/auth";
import pkg from 'pg';
const { Client } = pkg;
import { mapUuidToNumericId } from '@/utils/userIdMapping';

export async function GET(request: NextRequest) {
  try {
    // Get strategy wish ID from query params
    const { searchParams } = new URL(request.url);
    const strategyWishId = searchParams.get('strategyWishId') as string;

    if (!strategyWishId) {
      return NextResponse.json({ error: 'Strategy wish ID is required' }, { status: 400 });
    }

    // Verify user authentication using the existing auth utility
    const authResult = await verifyAuth(request);

    if (!authResult || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { user, refreshedToken } = authResult;

    // Ensure user.id is a string before mapping
    if (typeof user.id !== 'string') {
      console.error('User ID is not a string:', user.id);
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 500 });
    }

    // Map the UUID to a numeric ID for database compatibility
    const userId = await mapUuidToNumericId(user.id);

    // Connect to the database
    const connectionString = process.env.POSTGRES_URL;
    if (!connectionString) {
      throw new Error('POSTGRES_URL environment variable not found.');
    }

    const client = new Client({
      connectionString,
      ssl: {
        rejectUnauthorized: false,
      },
    });

    await client.connect();

    // Query for all strategies except the selected one
    const result = await client.query(`
      SELECT
          id,
          name as title,
          personal_goal as description,
          created_at,
          id as strategy_wish_id
      FROM
          strategies
      WHERE
          user_id = $1
          AND id != $2
          AND analysis_result IS NOT NULL
          AND analysis_result != ''
      ORDER BY
          created_at DESC
    `, [userId, strategyWishId]);

    // Close the database connection
    await client.end();

    // Return the strategies with refreshed token in the header
    const response = NextResponse.json({
      strategies: result.rows,
      message: 'Related strategies loaded successfully'
    });

    // Add refreshed token to response headers if available
    if (refreshedToken) {
      response.headers.set('x-refreshed-token', refreshedToken);
    }

    return response;

  } catch (error) {
    console.error('Error in related-strategies API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}