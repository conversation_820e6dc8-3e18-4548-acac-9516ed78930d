import client from '@/lib/opulabAIClient';

/**
 * Runs an AI analysis combining the four previous Opulab outputs
 * and returns prompt and result content.
 * @param companyInfo - Unternehmensinformationen
 * @param zielsetzung - Inhalt der persönlichen Zielsetzung
 * @param relevanteAspekte - Inhalt der relevanten Aspekte
 * @param priorisierung - Inhalt der Priorisierung
 * @param strategieBriefing - Inhalt des Strategiebriefings
 */
export async function runOpulabGesamtAnalyse(
  companyInfo: string,
  zielsetzung: string,
  relevanteAspekte: string,
  priorisierung: string,
  strategieBriefing: string
): Promise<{ prompt: string; content: string }> {
  let prompt = `## Gegebene Unternehmensinformationen:\n///\n${companyInfo}\n///\n\n`;
  prompt += `## Relevante Aspekte:\n///\n${relevanteAspekte}\n///\n\n`;
  prompt += `## Priorisierung:\n///\n${priorisierung}\n///\n\n`;
  prompt += `## **Persönliche Zielsetzung & Kontext:**\n///\n${zielsetzung}\n///\n\n`;
  prompt += `## **Strategiebriefing:**\n///\n${strategieBriefing}\n///\n\n`;
  prompt += `Entwickeln Sie eine Strategie, um das ausgewählte Unternehmen damit dann zu analysieren und Chancen, Schwächen sowie Möglichkeiten zu identifizieren. Nutzen Sie die bereitgestellten Unternehmensinformationen, strategischen Wünsche und Priorisierungen, um eine umfassende Markt- und Strategiebewertung durchzuführen. Identifizieren und priorisieren Sie umsetzbare Maßnahmen, um dem Unternehmen zu helfen, kurzfristige Erfolge und langfristige Ziele zu erreichen.`;

  const completion = await client.chat.completions.create({
    model: "google/gemma-3-27b-it:free",
    messages: [
      { role: "system", content: "Bitte präzise Antwort liefern." },
      { role: "user", content: prompt }
    ]
  });

  const content = completion.choices?.[0]?.message?.content ?? "";
  return { prompt, content };
}
