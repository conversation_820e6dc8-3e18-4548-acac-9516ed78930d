import { useState, useCallback } from 'react';

export const useOpulabUI = () => {
    const [isSavingCompanyInfo, setIsSavingCompanyInfo] = useState<boolean>(false);
    const [isSavingSettings, setIsSavingSettings] = useState<boolean>(false);
    // Separate state for tracking if an analysis is running
    const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
    const [message, setMessage] = useState<string>('');
    // Determines where the message is shown
    const [messageLocation, setMessageLocation] = useState<'companyInfo' | 'saveSettings' | 'analysis'>('companyInfo');
    // For backward compatibility
    const [showMessageInCompanyInfo, setShowMessageInCompanyInfo] = useState<boolean>(true);
    const [showLabModal, setShowLabModal] = useState<boolean>(false);
    // State for the text input within the analysis result modal
    const [analysisInput, setAnalysisInput] = useState<string>('');

    const clearMessageAfterDelay = useCallback((delay = 3000) => {
        setTimeout(() => setMessage(''), delay);
    }, []);

    const showMessage = useCallback((
        msg: string,
        location: 'companyInfo' | 'saveSettings' | 'analysis' | boolean = 'analysis',
        clearDelay?: number
    ) => {
        setMessage(msg);

        // Handle both new location parameter and backward compatibility with boolean
        if (typeof location === 'boolean') {
            // Old API: boolean indicates if message is shown in company info section
            setShowMessageInCompanyInfo(location);
            setMessageLocation(location ? 'companyInfo' : 'analysis');
        } else {
            // New API: string indicates where the message is shown
            setMessageLocation(location);
            setShowMessageInCompanyInfo(location === 'companyInfo');
        }

        if (clearDelay !== undefined) {
             if (clearDelay > 0) {
                 clearMessageAfterDelay(clearDelay);
             }
        } else {
             // Default clear delay for non-error messages
             if (!msg.toLowerCase().includes('fehler')) {
                 clearMessageAfterDelay();
             }
        }
    }, [clearMessageAfterDelay]);


    return {
        isSavingCompanyInfo,
        setIsSavingCompanyInfo,
        isSavingSettings,
        setIsSavingSettings,
        isAnalyzing,
        setIsAnalyzing,
        message,
        showMessage,
        messageLocation, // New property for message location
        showMessageInCompanyInfo, // For backward compatibility
        showLabModal,
        setShowLabModal,
        analysisInput,
        setAnalysisInput,
    };
};