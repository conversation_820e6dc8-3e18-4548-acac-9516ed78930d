import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { AnalysisResult } from '@/types'; // Import shared type

// Define the expected shape of the context parameters
interface RouteContext {
    params: {
        placeId?: string; // Make placeId optional to handle potential undefined case gracefully
    };
}

// GET /api/schnelle-analyse/[placeId] - Fetch latest analysis by placeId
export async function GET(request: NextRequest, context: RouteContext) { // Use the defined type
    try {
        const { placeId } = await context.params; // Access params directly using the type

        if (!placeId) {
            return NextResponse.json({ error: 'Place ID ist erforderlich' }, { status: 400 });
        }

        // Create a Supabase client
        const supabase = await createClient();

        // Query the schnelle_analyse table
        const { data, error } = await supabase
            .from('schnelle_analyse')
            .select('*')
            .eq('place_id', placeId)
            .order('analysis_date', { ascending: false })
            .limit(1)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({
                    message: 'Keine Analyse für dieses Unternehmen gefunden'
                }, { status: 404 });
            }

            throw error;
        }

        // Return the analysis data in the expected format
        const responseData: AnalysisResult = {
            analysisContent: data.analysis_content,
            analysisDate: data.analysis_date,
            // Include businessData if needed by the frontend when fetching single analysis
            // businessData: {
            //     name: data.name,
            //     address: data.address,
            //     phone: data.phone,
            //     website: data.website,
            //     placeId: data.place_id,
            //     lat: data.lat,
            //     lng: data.lng
            // }
        };

        return NextResponse.json(responseData);

    } catch (error: unknown) {
        console.error('Fehler beim Abrufen der Schnellanalyse:', error);
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({
            error: 'Serverfehler beim Abrufen der Analyse',
            details: message // Use the derived message variable
        }, { status: 500 });
    }
}
