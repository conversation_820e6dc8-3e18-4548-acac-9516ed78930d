import { NextRequest, NextResponse } from 'next/server';
import { createPgClient } from '@/lib/db-utils';
import { verifySupabaseAuth, createAuthResponse } from '@/lib/supabaseAuth';
import { runOpulabGesamtAnalyse } from '@/api/opulab-gesamt-analyse';

/**
 * POST /api/opulab-gesamt-analyse
 * Body: { strategyId: number }
 * Combines the four previous analyses into one Gesamtanalyse and stores it.
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();
    if (!authResult || !authResult.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const { user } = authResult;
    const userId = user.id;

    const { strategyId } = await request.json();
    if (!strategyId) {
      return createAuthResponse({ error: 'strategyId ist erforderlich.' }, 400);
    }

    const client = createPgClient();
    await client.connect();

    try {
      // Fetch strategy inputs from the strategies table
      const strategyRes = await client.query(
        'SELECT company_info_points FROM public.profiles WHERE id = $1',
        [userId]
      );
      const companyInfo = strategyRes.rows[0]?.company_info_points || user.company_info_points || '';

      // Fetch individual analysis results from the analysis_results table
      const analysisTypes = ['personal_goal', 'relevant_aspects', 'prioritization', 'briefing'];
      const analysisResultsQuery = `
        SELECT analysis_type, content
        FROM public.analysis_results
        WHERE strategy_id = $1 AND analysis_type = ANY($2::text[])
        ORDER BY CASE analysis_type
          WHEN 'personal_goal' THEN 1
          WHEN 'relevant_aspects' THEN 2
          WHEN 'prioritization' THEN 3
          WHEN 'briefing' THEN 4
          ELSE 5
        END;
      `;
      const individualResultsRes = await client.query(analysisResultsQuery, [strategyId, analysisTypes]);

      const resultsMap: { [key: string]: string } = {};
      individualResultsRes.rows.forEach(row => {
        resultsMap[row.analysis_type] = row.content;
      });

      const zielContent = resultsMap['personal_goal'] || '';
      const aspekContent = resultsMap['relevant_aspects'] || '';
      const priorContent = resultsMap['prioritization'] || '';
      const briefingContent = resultsMap['briefing'] || '';

      // Run the combined analysis
      const { content: gesamtAnalyseContent } = await runOpulabGesamtAnalyse(
        companyInfo,
        zielContent,
        aspekContent,
        priorContent,
        briefingContent
      );

      // Update the main analysis_result in the strategies table
      const updateGesamtAnalyseQuery = `
        UPDATE public.strategies
        SET analysis_result = $1,
            updated_at      = now()
        WHERE user_id = $2
          AND id      = $3
        RETURNING id, analysis_result;
      `;
      const upd = await client.query(updateGesamtAnalyseQuery, [
        gesamtAnalyseContent,
        userId,
        strategyId
      ]);

      const file = upd.rows[0] || null;
      return createAuthResponse({ file });

    } finally {
      await client.end();
    }
  } catch (err) {
    console.error('Error in POST /api/opulab-gesamt-analyse:', err);
    const message = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
