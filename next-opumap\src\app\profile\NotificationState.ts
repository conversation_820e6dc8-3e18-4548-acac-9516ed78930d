import { useState, useCallback } from 'react';

export type NotificationType = 'success' | 'error';

export function useNotification(initialState: boolean = false) {
  const [isVisible, setIsVisible] = useState(initialState);
  const [message, setMessage] = useState<string>('');
  const [type, setType] = useState<NotificationType>('success');
  const [details, setDetails] = useState<string | undefined>(undefined);
  const showNotification = useCallback((msg: string, type: NotificationType = 'success', details?: string) => {
    setMessage(msg);
    setType(type);
    setDetails(details);
    setIsVisible(true);
  }, []);
  const hideNotification = useCallback(() => setIsVisible(false), []);
  return { isVisible, message, type, details, showNotification, hideNotification };
}
