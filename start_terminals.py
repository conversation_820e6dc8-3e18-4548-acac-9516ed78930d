#!/usr/bin/env python3
import subprocess
import time
import socket
import os
import platform
import sys

class Terminal:
    def __init__(self, directory, command, env=None):
        self.directory = directory
        self.command = command
        self.env = env # Store environment variables

    def open(self):
        # Executes the command in the specified directory using the current terminal environment
        print(f"Executing in directory {self.directory}: {self.command}")
        # Pass the modified environment if provided
        process_env = os.environ.copy()
        if self.env:
            process_env.update(self.env)

        # Use shell=True for command chaining/parsing, pass env separately
        return subprocess.Popen(self.command, cwd=self.directory, shell=True, env=process_env)

def wait_for_server(host, port, timeout=60, check_interval=1.0):
    """Waits for a server to become available at the specified host and port."""
    print(f"Waiting for server at {host}:{port}...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            with socket.create_connection((host, port), timeout=1):
                print(f"Server detected on port {port}.")
                return True
        except (socket.timeout, ConnectionRefusedError):
            # Use a shorter sleep time for more responsive checking
            time.sleep(check_interval)
            # Print a dot to show progress without cluttering the console
            sys.stdout.write(".")
            sys.stdout.flush()
        except Exception as e:
            print(f"An unexpected error occurred while checking port {port}: {e}")
            time.sleep(check_interval)
    print(f"\nServer did not become available on port {port} within {timeout} seconds.")
    return False

def main():
    # Define the Next.js application terminal
    next_app_dir = os.path.join(os.path.dirname(__file__), "next-opumap")

    # Set optimized Node.js flags for better performance
    node_options = "--max-old-space-size=4096" # Increase memory limit

    # Prepare environment variables for the subprocess
    next_app_env = os.environ.copy()
    next_app_env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0'
    next_app_env['NODE_OPTIONS'] = node_options

    # Run the optimization script first
    print(f"Running optimization script in {next_app_dir}...")
    try:
        subprocess.run("npm run optimize", cwd=next_app_dir, shell=True, check=True)
        print("Optimization completed successfully.")
    except subprocess.CalledProcessError:
        print("Optimization script failed, continuing with startup...")

    # Use the standard dev command for compatibility
    next_app_command = "npm run dev"

    # Create Terminal instance with the environment
    next_app_terminal = Terminal(next_app_dir, next_app_command, env=next_app_env)

    # Start the Next.js application
    print(f"Starting Next.js development server in {next_app_dir}...")
    process = next_app_terminal.open() # This will now run in the current integrated terminal

    # Wait until the Next.js server is listening on port 3000
    print("Waiting for the Next.js server to be ready on port 3000...")
    if wait_for_server("localhost", 3000, timeout=120, check_interval=0.5): # Increased timeout, shorter check interval
        print("\nNext.js server is ready on http://localhost:3000")
        # Optionally open the browser
        # try:
        #     import webbrowser
        #     webbrowser.open("http://localhost:3000")
        # except ImportError:
        #     print("Could not import webbrowser module to open the browser.")
    else:
        print("Next.js server did not start within the timeout period. Please check the terminal logs.")
        # Try to terminate the process if it's still running
        try:
            process.terminate()
        except:
            pass

if __name__ == "__main__":
    main()
