{"name": "next-op<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--no-warnings next dev --turbopack", "dev:fast": "cross-env NEXT_TURBO=1 NODE_OPTIONS=--no-warnings next dev --turbopack", "dev:analyze": "cross-env ANALYZE=true next dev", "build": "cross-env NODE_OPTIONS=--no-warnings next build", "build:analyze": "cross-env ANALYZE=true NODE_OPTIONS=--no-warnings next build", "start": "cross-env NODE_OPTIONS=--no-warnings next dev --turbopack", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .next && rimraf node_modules/.cache", "optimize": "node optimize-dev.js", "optimize:db": "node db-optimize.js", "deploy": "chmod +x deploy.sh && ./deploy.sh", "deploy:prod": "NODE_ENV=production npm run deploy", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:logs": "pm2 logs opumap"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@react-google-maps/api": "^2.20.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@uiw/react-md-editor": "^4.0.5", "@vercel/postgres": "^0.10.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.18.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "openai": "^4.96.0", "pg": "^8.14.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "styled-components": "^6.1.17", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/css": "^0.7.0", "@eslint/eslintrc": "^3", "@eslint/js": "^9.26.0", "@eslint/json": "^0.12.0", "@eslint/markdown": "^6.4.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/estree": "^1.0.7", "@types/google.maps": "^3.58.1", "@types/json-schema": "^7.0.15", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.19.1", "@types/pg": "^8.11.12", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.26.0", "eslint-config-next": "15.3.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^8.32.0"}}