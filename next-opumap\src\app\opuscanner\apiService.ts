'use client';

import { Strategy } from "../opulab/strategyService";
import { createClient } from '@/utils/supabase/client';
import { ScanResult, DisplayScanResult, UserProfileUpdateData, SelectedCompany } from './types'; // Import types from local types file
import { UserProfile } from '@/types';

// Interface for related strategies
// Represents the data structure for display

export class OpuscannerService {
  // Keine Token-Aktualisierung mehr nötig, da Supabase Auth die Session verwaltet
  static async loadSelectedCompanies(): Promise<SelectedCompany[]> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        if (typeof window !== 'undefined') {
          window.location.href = "/login";
        }
        return [];
      }

      // Log the session token for debugging (only the first few characters)
      const token = session.access_token;
      if (token) {
        if (process.env.NODE_ENV !== 'production') {
          console.log(`Auth token available: ${token.substring(0, 10)}...`);
        }
      } else {
        console.warn("No access token in session");
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      try {
        const response = await fetch("/api/selected-companies", {
          method: "GET",
          signal: controller.signal,

        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 401) {
            // Session ist abgelaufen oder ungültig
            console.warn("Session expired or invalid (401 Unauthorized)");

            // Try to refresh the session
            const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
            if (refreshError || !refreshData.session) {
              console.error("Failed to refresh session:", refreshError);
              if (typeof window !== 'undefined') {
                window.location.href = "/login";
              }
              return [];
            }

            // Retry the request with the refreshed session
            console.log("Retrying request with refreshed session");
            const retryResponse = await fetch("/api/selected-companies", {
              method: "GET",
            });

            if (!retryResponse.ok) {
              console.error("Retry failed:", retryResponse.status, retryResponse.statusText);
              if (typeof window !== 'undefined') {
                window.location.href = "/login";
              }
              return [];
            }

            const retryData = await retryResponse.json();
            return retryData.companies || [];
          }

          // Get more detailed error information if available
          let errorMessage = "Fehler beim Laden der ausgewählten Unternehmen";
          try {
            const errorData = await response.json();
            if (errorData.error) {
              errorMessage += `: ${errorData.error}`;
            }
          } catch (_) {
            // Ignore JSON parsing errors
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        return data.companies || [];
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          console.error("Request timed out");
          throw new Error("Zeitüberschreitung beim Laden der ausgewählten Unternehmen");
        }
        throw error;
      }
    } catch (error) {
      console.error("Error loading companies:", error);
      throw error;
    }
  }

  static async loadUserStrategies(): Promise<Strategy[]> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        return [];
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);
      const response = await fetch("/api/strategies", {
        method: "GET",
        signal: controller.signal,
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 401) {
          console.warn("Session potentially expired or invalid.");
        }
        console.error("Fehler beim Laden der Benutzerstrategien:", response.statusText);
        return [];
      }

      const data = await response.json();
      return (data.strategies || []).map((s: Strategy) => ({
        ...s,
        relevant_aspects: typeof s.relevant_aspects === 'string'
          ? JSON.parse(s.relevant_aspects)
          : s.relevant_aspects,
        analysis_result: s.analysis_result || undefined
      }));
    } catch (error) {
      console.error("Error loading user strategies:", error);
      return [];
    }
  }

  static async checkSession(): Promise<void> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        window.location.href = "/login";
        return;
      }

      // Optionale Überprüfung der API-Erreichbarkeit
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      const response = await fetch("/api/selected-companies", {
        method: "GET",
        signal: controller.signal,
      });
      clearTimeout(timeoutId);

      if (!response.ok && response.status === 401) {
        window.location.href = "/login";
      }
    } catch (error) {
      console.error("Session check error:", error);
      if (
        error instanceof Error &&
        (error.message.includes("abgelaufen") ||
          error.message.includes("Nicht authentifiziert") ||
          error.message.includes("Laden der ausgewählten Unternehmen"))
      ) {
        window.location.href = "/login";
      }
    }
  }

  static async removeCompany(place_id: string): Promise<void> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error("Nicht authentifiziert.");
      }

      const response = await fetch(`/api/selected-companies?place_id=${place_id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Fehler beim Entfernen des Unternehmens");
      }
    } catch (error) {
      console.error("Error removing company:", error);
      throw error;
    }
  }

  static async saveCompanyInfo(profileData: UserProfileUpdateData): Promise<UserProfile> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error("Nicht authentifiziert.");
      }

      const response = await fetch("/api/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(profileData),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Fehler beim Speichern der Unternehmensinformationen.");
      }
      return result;
    } catch (error) {
      console.error("Error saving company info:", error);
      throw error;
    }
  }

  /**
   * Loads scan results for a specific strategy and set of companies.
   * @param strategyId - The ID of the strategy used for the scan.
   * @param placeIds - Array of place_ids for the companies included in the scan.
   * @returns Promise<ScanResult[]>
   */
  static async loadScanResults(strategyId: number, placeIds: string[]): Promise<ScanResult[]> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('loadScanResults: No active session found');

        // Versuchen, zum Login-Bildschirm zu navigieren
        if (typeof window !== 'undefined') {
          console.log('Redirecting to login page');
          // Aktuelle URL für Redirect-Zurück speichern
          localStorage.setItem('redirectAfterLogin', window.location.pathname);
          // Zur Login-Seite weiterleiten
          window.location.href = "/login";
        }

        throw new Error('Not authenticated');
      }

      console.log('Making API request to /api/scan-results with strategyId:',
        strategyId, 'and', placeIds.length, 'place_ids');

      // API-Anfrage senden
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 15000);

const response = await fetch("/api/scan-results", {
   method: "POST",
   headers: {
     "Content-Type": "application/json",
   },
   body: JSON.stringify({ strategyId, placeIds }),
  signal: controller.signal,
 });
clearTimeout(timeoutId);

      // Fehler behandeln
      if (!response.ok) {
        if (response.status === 401) {
          console.error('loadScanResults: Authentication failed (401 Unauthorized)');
          throw new Error('Not authenticated');
        }

        // Versuchen, Fehlerdetails aus der Antwort zu extrahieren
        let errorDetails = response.statusText;
        try {
          const errorData = await response.json();
          errorDetails = errorData.error || errorData.details || errorDetails;
        } catch {
          // JSON-Parsing fehlgeschlagen, Status-Text verwenden
        }

        throw new Error(`Failed to load scan results: ${errorDetails}`);
      }

      // Erfolgreich Daten verarbeiten
      const data = await response.json();
      console.log('Scan results received:', data.results?.length || 0, 'items');

      // Daten transformieren
      const results: ScanResult[] = (data.results || []).map((item: {
        place_id: string;
        title?: string;
        description: string;
      }) => ({
        place_id: item.place_id,
        companyName: item.title || 'Unbekanntes Unternehmen',
        result_text: item.description,
      }));

      return results;
    } catch (error) {
      console.error('Error loading scan results:', error);

      // Spezifische Fehlerbehandlung für häufige Probleme
      if (
        error instanceof Error &&
        (error.message.includes('Not authenticated') ||
         error.message.includes('token expired') ||
         error.message.includes('Authentication failed'))
      ) {
        // Auth Problem - zum Login umleiten
        if (typeof window !== 'undefined') {
          localStorage.setItem('redirectAfterLogin', window.location.pathname);
          window.location.href = "/login";
        }
      }

      throw error instanceof Error
        ? error
        : new Error('Failed to load scan results.');
    }
  }

  /**
   * Loads the LATEST scan result for each company associated with a specific strategy.
   * @param strategyId - The ID of the strategy.
   * @param showAllCompanies - Whether to include results for all companies or only selected ones.
   * @returns Promise<DisplayScanResult[]>
   */
  static async loadLatestScanResultsForStrategy(strategyId: number, showAllCompanies: boolean = false): Promise<DisplayScanResult[]> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        return []; // Return empty array if not authenticated
      }

      const response = await fetch(`/api/strategy-scan-results?strategyId=${strategyId}&showAllCompanies=${showAllCompanies}`, {
        method: 'GET'
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.error('Authentication error fetching latest scan results.');
          // Consider redirecting to login or throwing an error
        }
        throw new Error(`Failed to load latest scan results: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Latest scan results loaded for strategy', strategyId, ':', data.results?.length || 0, 'items');

      // Transform the data to ensure it has place_id instead of company_id
      // Note: The DisplayScanResult type in './types.ts' should be updated:
      // - 'title' property should become 'title?: string;'
      // - A new property 'companyName: string;' should be added.
      const results: DisplayScanResult[] = (data.results || []).map((item: {
        id: number;
        title?: string;
        description: string;
        place_id: string;
        company_id?: number; // For backward compatibility
        created_at: string;
      }) => ({
        id: item.id,
        title: item.title, // Preserves the original title (could be undefined)
        companyName: item.title || 'Unbekanntes Unternehmen', // Derived name with fallback
        description: item.description,
        place_id: item.place_id,
        created_at: item.created_at
      }));

      return results;

    } catch (error) {
      console.error('Error in loadLatestScanResultsForStrategy:', error);
      return []; // Return empty array on error
    }
  }
}
