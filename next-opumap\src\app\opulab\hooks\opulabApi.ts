import { OpuscannerService } from '../../opuscanner/apiService'; // Corrected path relative to hooks dir

// Interfaces might be moved to a central types file later
interface ProfileData {
    name: string;
    email: string;
    company_name?: string | null;
    address?: string | null;
    phone?: string | null;
    website?: string | null;
    employee_count?: number | string | null;
    company_info_points: string;
}

interface ApiError extends Error {
    status?: number;
    details?: unknown;
}

// Helper function to convert employee_count to number | null
const convertEmployeeCount = (count: number | string | null | undefined): number | null => {
    if (typeof count === 'number') {
        return count;
    }
    if (typeof count === 'string' && count !== '') {
        const parsed = parseInt(count, 10);
        // Return parsed number if valid, otherwise null
        return isNaN(parsed) ? null : parsed;
    }
    // Handles null, undefined, and empty string
    return null;
};

// No need to manually get the token with Supabase Auth
// Cookies are automatically sent with the request
const fetchApi = async (url: string, options: RequestInit) => {
    const headers = {
        "Content-Type": "application/json",
        ...options.headers,
    };

    const response = await fetch(url, { ...options, headers });

    if (!response.ok) {
        let errorDetails;
        try {
            errorDetails = await response.json();
        } catch (_) {
            errorDetails = await response.text();
        }
        const error: ApiError = new Error(`API Error (${response.status}): ${response.statusText}`);
        error.status = response.status;
        error.details = errorDetails;
        console.error(`API Error fetching ${url}:`, error);
        throw error;
    }

    // Handle responses that might not have a body (e.g., 204 No Content)
     if (response.status === 204) {
        return null;
     }

    try {
        return await response.json();
    } catch (_) {
         console.error(`API Error parsing JSON from ${url}`);
         // If JSON parsing fails but response was ok, maybe return null or handle differently
         return null; // Or throw a different error
    }
};

export const opulabApi = {
    saveCompanyInfo: async (profileData: ProfileData) => {
        // Ensure the profileData conforms to UserProfileUpdateData type before passing

        const dataToSend = {
            name: profileData.name ?? null,
            email: profileData.email ?? null,
            company_name: profileData.company_name ?? null,
            address: profileData.address ?? null,
            phone: profileData.phone ?? null,
            website: profileData.website ?? null,
            // Use the helper function for employee_count conversion
            employee_count: convertEmployeeCount(profileData.employee_count),
            company_info_points: profileData.company_info_points ?? null
        };

        // This uses OpuscannerService directly as it handles its own API logic
        try {
            // Explicitly cast dataToSend to UserProfileUpdateData if needed, or rely on structure
            return await OpuscannerService.saveCompanyInfo(dataToSend);
        } catch (error) {
            console.error("Error in OpuscannerService.saveCompanyInfo:", error);
            throw error; // Re-throw to be handled by the calling hook
        }
    },

    startPersonalGoalAnalysis: async (strategyId: number, personalGoal: string) => {
        return fetchApi("/api/opulab-persoenliche-zielsetzung", {
            method: "POST",
            body: JSON.stringify({ strategyId, personalGoal }),
        });
    },

    startAspectsAnalysis: async (strategyId: number, aspects: string[]) => {
        return fetchApi("/api/relevante-aspekte-analyse", {
            method: "POST",
            body: JSON.stringify({ strategyId, aspects }),
        });
    },

    startPrioritizationAnalysis: async (strategyId: number, shortTerm: number, longTerm: number) => {
        return fetchApi("/api/opulab-priorisierung", {
            method: "POST",
            body: JSON.stringify({ strategyId, shortTerm, longTerm }),
        });
    },

     startBriefingAnalysis: async (strategyId: number, shortTermTasks: string, longTermGoals: string) => {
        return fetchApi("/api/opulab-strategie-briefing", {
            method: "POST",
            body: JSON.stringify({ strategyId, shortTermTasks, longTermGoals }),
        });
     },

    startTotalAnalysis: async (strategyId: number) => {
        return fetchApi("/api/opulab-gesamt-analyse", {
            method: "POST",
            body: JSON.stringify({ strategyId }),
        });
    },

    // Note: StrategyService calls (get, create, update, delete) are kept separate
    // as they are imported from ../strategyService and might handle Supabase logic directly.
    // If StrategyService was just fetch wrappers, they could be merged here.

};