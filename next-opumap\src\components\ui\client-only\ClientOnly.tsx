'use client';

import { useEffect, useState, ReactNode } from 'react';

interface ClientOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>, die sicherstellt, dass ihre Kinder nur auf dem Client gerendert werden.
 * Dies verhindert Hydration-<PERSON>hler, die durch Browser-Erweiterungen wie Dark Reader verursacht werden können.
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Auf dem Server oder während der ersten Client-Renderung zeigen wir den Fallback an
  if (!isClient) {
    return <>{fallback}</>;
  }

  // Auf dem Client zeigen wir die Kinder an
  return <>{children}</>;
}
