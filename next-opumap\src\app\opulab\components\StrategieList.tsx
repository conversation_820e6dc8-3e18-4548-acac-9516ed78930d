'use client';

import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Sparkles, Calendar } from 'lucide-react';
import { Strategy } from '../strategyService';

// Import styles
import { cardStyles } from '../styles';

interface StrategieListProps {
  strategies: Strategy[];
  selectedStrategy: Strategy | null;
  onSelectStrategy: (strategy: Strategy) => void;
  onAddNewStrategy: () => void;
  onCreateAIStrategy?: () => void;
  isLoading?: boolean;
}

const StrategieList: React.FC<StrategieListProps> = ({
  strategies,
  selectedStrategy,
  onSelectStrategy,
  onAddNewStrategy,
  onCreateAIStrategy = () => console.log('KI-Strategie erstellen'),
  isLoading = false
}) => {
  // Format date to a readable string
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className={cardStyles.elevated + ' shadow-xl bg-gradient-to-br from-primary/5 via-background to-secondary/5'}>
      <CardHeader className={`${cardStyles.header} text-center`}>
        <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>
          <span className="inline-block">Erstellte</span>
          <span className="inline-block">Strategien</span>
        </CardTitle>
        <p className={cardStyles.subtitle}>Wählen oder erstellen Sie eine Strategie</p>
      </CardHeader>
      <CardContent className={cardStyles.content}>
        {isLoading ? (
          <div className="py-4 text-center text-muted-foreground">
            <p>Lade Strategien...</p>
          </div>
        ) : strategies.length === 0 ? (
          <div className="py-4 text-center text-muted-foreground">
            <p>Keine Strategien vorhanden.</p>
            <p className="text-sm mt-2">Erstellen Sie eine neue Strategie, um zu beginnen.</p>
          </div>
        ) : (
          <ul className="flex flex-col gap-2 mb-4 pr-1">
            {strategies.map((strategy) => (
              <li key={strategy.id}>
                <button
                  type="button"
                  className={`w-full flex flex-col gap-1 px-4 py-3 rounded-xl border transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-primary/60 focus:ring-offset-2 shadow-sm
                    ${selectedStrategy?.id === strategy.id
                      ? 'bg-primary/10 border-primary text-primary font-semibold shadow-primary/10'
                      : 'bg-background border-border hover:bg-muted/60 hover:border-primary/40 text-foreground'}
                  `}
                  onClick={() => onSelectStrategy(strategy)}
                  aria-pressed={selectedStrategy?.id === strategy.id}
                >
                  <div className="flex items-center w-full">
                    <span className={`flex-1 text-left ${selectedStrategy?.id === strategy.id ? 'text-primary' : ''}`}>
                      {strategy.strategy_name}
                    </span>
                    {selectedStrategy?.id === strategy.id && (
                      <span className="inline-block w-2.5 h-2.5 rounded-full bg-primary shadow-md border border-white" />
                    )}
                  </div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    <span>{formatDate(strategy.updated_at)}</span>
                  </div>
                </button>
              </li>
            ))}
          </ul>
        )}
        <div className="flex flex-col gap-2 mt-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full flex items-center justify-center gap-2 border-primary/40 hover:border-primary"
            onClick={onAddNewStrategy}
            disabled={strategies.length >= 5}
            title={strategies.length >= 5 ? 'Maximale Anzahl an Strategien erreicht (5)' : 'Neue Strategie erstellen'}
          >
            <Plus className="h-4 w-4" /> Neue Strategie
            {strategies.length >= 5 && <span className="ml-1 text-xs">(Max. 5)</span>}
          </Button>
          <Button
            size="sm"
            className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-primary/80 to-secondary/80 text-primary-foreground shadow-md hover:from-primary hover:to-secondary"
            onClick={onCreateAIStrategy}
          >
            <Sparkles className="h-4 w-4" /> KI-Strategie erstellen
          </Button>
        </div>
      </CardContent>
    </div>
  );
};

export default StrategieList;
