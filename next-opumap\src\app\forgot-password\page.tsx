'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState<string | null>('');
  const [error, setError] = useState<string | null>('');
  const [isLoading, setIsLoading] = useState(false);
  const { resetPassword } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');

    if (!email) {
      setError('Bitte geben Sie Ihre E-Mail-Adresse ein.');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Sending password reset email to:', email);
      const { error } = await resetPassword(email);

      if (error) {
        console.error('Error from resetPassword function:', error);
        throw error;
      }

      setMessage('Eine E-Mail mit einem Link zum Zurücksetzen Ihres Passworts wurde versendet. Bitte überprüfen Sie Ihren Posteingang und auch den Spam-Ordner.');
      setEmail('');
    } catch (error) {
      console.error('Error resetting password:', error);

      // More specific error message
      let errorMessage = 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.';

      // Type guard for error with message property
      if (error && typeof error === 'object' && 'message' in error) {
        const errorMsg = String(error.message);

        if (errorMsg.includes('rate limit') || errorMsg.includes('too many requests')) {
          errorMessage = 'Zu viele Anfragen. Bitte warten Sie einige Minuten, bevor Sie es erneut versuchen.';
        } else if (errorMsg.includes('user not found') || errorMsg.includes('no user found')) {
          errorMessage = 'Es wurde kein Konto mit dieser E-Mail-Adresse gefunden.';
        } else if (errorMsg.includes('network') || errorMsg.includes('connection')) {
          errorMessage = 'Netzwerkfehler. Bitte überprüfen Sie Ihre Internetverbindung.';
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col items-center justify-start pt-16 bg-[var(--color-background)] min-h-screen transition-colors duration-200`}>
      <div className="w-full max-w-md p-8 space-y-6 bg-[var(--color-card)] rounded-lg shadow-lg border border-[var(--color-border)]">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-[var(--color-card-foreground)] mb-2">
            Passwort zurücksetzen
          </h1>
          <p className="text-sm text-[var(--color-muted-foreground)]">
            Geben Sie Ihre E-Mail-Adresse ein, um einen Link zum Zurücksetzen Ihres Passworts zu erhalten.
          </p>
        </div>

        {error && (
          <div className="p-4 text-sm rounded-md bg-[var(--color-destructive)] text-[var(--color-destructive-foreground)]" role="alert">
            <span>{error}</span>
          </div>
        )}

        {message ? (
          <div className="space-y-6">
            <div className="p-4 text-sm rounded-md bg-[var(--color-success)] text-[var(--color-success-foreground)]">
              <span>{message}</span>
            </div>
            <div className="flex flex-col space-y-4">
              <p className="text-sm text-[var(--color-muted-foreground)]">
                Wenn Sie keine E-Mail erhalten haben:
              </p>
              <ul className="list-disc pl-5 text-sm text-[var(--color-muted-foreground)] space-y-2">
                <li>Überprüfen Sie Ihren Spam-Ordner</li>
                <li>Stellen Sie sicher, dass Sie die richtige E-Mail-Adresse eingegeben haben</li>
                <li>Warten Sie ein paar Minuten, da die E-Mail-Zustellung manchmal verzögert sein kann</li>
              </ul>
              <Button
                type="button"
                className="mt-4 py-2 px-4 bg-[var(--color-secondary)] text-[var(--color-secondary-foreground)] hover:bg-[var(--color-secondary-hover)] focus:ring-2 focus:ring-offset-2 focus:ring-[var(--color-secondary)] transition-colors duration-200"
                onClick={() => {
                  setMessage(null);
                  setEmail('');
                }}
              >
                Neue Anfrage senden
              </Button>
            </div>
          </div>
        ) : (
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <div>
                <label
                  htmlFor="email-address"
                  className="block text-sm font-medium text-[var(--color-card-foreground)] mb-1"
                >
                  E-Mail-Adresse
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="w-full px-4 py-2 rounded-md border border-[var(--color-border)] bg-[var(--color-input)] text-[var(--color-foreground)] placeholder-[var(--color-muted-foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <Button
              type="submit"
              className="w-full py-2 px-4 bg-[var(--color-primary)] text-[var(--color-primary-foreground)] hover:brightness-90 focus:ring-2 focus:ring-offset-2 focus:ring-[var(--color-primary)] transition-all duration-200"
              disabled={isLoading}
            >
              {isLoading ? 'Wird gesendet...' : 'Link zum Zurücksetzen senden'}
            </Button>
          </form>
        )}

        <div className="text-center text-sm">
          <Link
            href="/login"
            className="font-medium text-[var(--color-primary)] hover:underline hover:text-[var(--color-primary-hover)] transition-colors"
          >
            ← Zurück zur Anmeldung
          </Link>
        </div>
      </div>
    </div>
  );
}
