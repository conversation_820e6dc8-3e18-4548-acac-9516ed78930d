'use client'; // Provider needs to be a client component to use hooks

import React, { createContext, useState, useEffect, useContext, ReactNode, useMemo } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Session, User as SupabaseUser } from '@supabase/supabase-js';

// Define the User type based on fields used in Profile.tsx
// Ensure this matches the actual user data structure from your API
export interface User {
  id?: string;
  company_name?: string | null;
  name?: string | null;
  email?: string | null;
  address?: string | null;
  phone?: string | null;
  website?: string | null;
  employee_count?: number | string | null;
  company_info_points?: string | null;
  // Zusätzliche Felder für Profil-Statistiken und Darstellung
  analysisCount?: number;
  activityCount?: number;
  createdAt?: string;
  avatarUrl?: string;
}

// Define the shape of the context value
export interface AuthContextType { // Export the interface
  user: User | null;
  supabaseUser: SupabaseUser | null;
  session: Session | null;
  isLoading: boolean;
  initialAuthDone: boolean;
  isAuthReadyForDataFetch: boolean; // Add new state for data fetching readiness
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signUp: (email: string, password: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<{ error: Error | null }>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  updatePassword: (newPassword: string) => Promise<{ error: Error | null }>;
  refreshSession: () => Promise<void>;
  refreshUserProfile: () => Promise<void>;
}

// Create the context with a default value (can be null or a default object)
// Using null requires careful checks in consumers, but is often cleaner.
export const AuthContext = createContext<AuthContextType | null>(null);

// Define props for the provider
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [initialAuthDone, setInitialAuthDone] = useState<boolean>(false);
  const [isPasswordResetFlow, setIsPasswordResetFlow] = useState<boolean>(false);

  const supabase = useMemo(() => createClient(), []);

  // Derived state to indicate if auth is ready for data fetching
  const isAuthReadyForDataFetch = !isLoading && initialAuthDone && !!user;

  // Handle auth state changes
  useEffect(() => {
    console.log("AuthContext: useEffect setting up auth listener. Initial state: isLoading=true, initialAuthDone=false");
    setIsLoading(true);
    setInitialAuthDone(false);

    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, currentSession) => {
        console.log(`AuthContext: onAuthStateChange triggered - event: ${event}`, "session:", currentSession ? 'present' : 'null');

        // Handle password recovery flow
        if (event === 'PASSWORD_RECOVERY') {
          console.log('AuthContext: Password recovery flow detected');
          setIsPasswordResetFlow(true);
          return;
        }

        // If we're in password reset flow, don't update the user state
        if (isPasswordResetFlow && (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED')) {
          console.log('AuthContext: Suppressing auth state update during password reset flow');
          return;
        }

        const currentSupabaseAuthUser = currentSession?.user || null;
        setSupabaseUser(currentSupabaseAuthUser);
        setSession(currentSession);

        if (currentSupabaseAuthUser) {
          // Set basic user information as soon as session is known
          setUser({ id: currentSupabaseAuthUser.id, email: currentSupabaseAuthUser.email });
          // Mark initial authentication (session check) as done
          setInitialAuthDone(true);
          
          // DEADLOCK FIX: Move async operations out of onAuthStateChange using setTimeout
          setTimeout(async () => {
            try {
              console.log("AuthContext: Fetching profile for user ID:", currentSupabaseAuthUser.id);
              const { data: profileData, error: profileError } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', currentSupabaseAuthUser.id)
                .single();

              if (profileError && profileError.code !== 'PGRST116') {
                console.error("AuthContext: Error fetching user profile:", profileError);
                // User remains with basic info set above
              } else if (profileData) {
                console.log("AuthContext: Profile found:", profileData);
                const fullUser: User = {
                  id: currentSupabaseAuthUser.id,
                  email: currentSupabaseAuthUser.email,
                  name: profileData.name,
                  company_name: profileData.company_name,
                  address: profileData.address,
                  phone: profileData.phone,
                  website: profileData.website,
                  employee_count: profileData.employee_count,
                  company_info_points: profileData.company_info_points,
                  createdAt: profileData.created_at,
                  avatarUrl: profileData.avatar_url
                };
                setUser(fullUser); // Update user with full profile data
              } else {
                console.log("AuthContext: No profile data found. User remains with basic info.");
                // User remains with basic info set above
              }
            } catch (profileCatchError) {
              console.error("AuthContext: Error fetching profile:", profileCatchError);
              // User remains with basic info set above
            }
          }, 0);
          
        } else {
          // No Supabase auth user in session - handle session refresh with setTimeout
          console.log("AuthContext: No user in session, attempting session refresh...");
          
          setTimeout(async () => {
            try {
              const { data: { session: refreshedSession } } = await supabase.auth.getSession();
              if (refreshedSession?.user) {
                console.log("AuthContext: Session refresh found user:", refreshedSession.user.id);
                // Don't recursively call, just update state directly
                setSupabaseUser(refreshedSession.user);
                setSession(refreshedSession);
                setUser({ id: refreshedSession.user.id, email: refreshedSession.user.email });
              } else {
                console.log("AuthContext: Session refresh found no user");
                setUser(null);
              }
            } catch (refreshError) {
              console.error("AuthContext: Error during session refresh:", refreshError);
              setUser(null);
            }
          }, 0);
          
          setInitialAuthDone(true); // Mark initial auth as done regardless
        }

        // Regardless of profile fetch outcome, initial loading (session check) is now complete
        setIsLoading(false);
        // Log final states for this event processing cycle
        console.log(`AuthContext: Finished onAuthStateChange processing. Event: ${event}, User: ${!!currentSupabaseAuthUser}, InitialAuthDone: ${true}, IsLoading: ${false}`);
      }
    );

    return () => {
      console.log("AuthContext: Unsubscribing from onAuthStateChange.");
      authListener.subscription.unsubscribe();
    };
  }, [supabase]); // supabase is the correct and only dependency for setting up/tearing down the listener.

  // Add logging for state changes
  useEffect(() => {
    console.log("AuthContext State Updated:", { user, supabaseUser, session, isLoading, initialAuthDone });
  }, [user, supabaseUser, session, isLoading, initialAuthDone]);

  const signIn = async (email: string, password: string) => {
    console.log("AuthContext: signIn called. Setting isLoading=true");
    setIsLoading(true); // Ensure loading is true before async operation
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    // onAuthStateChange will handle setting isLoading to false and updating user
    if (error) {
      console.error("Sign-in error:", error);
      setIsLoading(false); // Explicitly set false if error and onAuthStateChange might not fire as expected
    }
    return { error };
  };

  const signUp = async (email: string, password: string) => {
    console.log("AuthContext: signUp called. Setting isLoading=true");
    setIsLoading(true); // Ensure loading is true
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        // emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });
    // onAuthStateChange will handle setting isLoading to false
    if (error) {
      console.error("Sign-up error:", error);
      setIsLoading(false); // Explicitly set false if error
    }
    return { error };
  };

  const signOut = async () => {
    console.log("AuthContext: signOut called. Resetting auth state");
    // Skip if we're in password reset flow
    if (isPasswordResetFlow) {
      console.log('AuthContext: Skipping sign out during password reset flow');
      return;
    }
    
    // First clear the current state
    setUser(null);
    setSupabaseUser(null);
    setSession(null);
    setInitialAuthDone(false);
    setIsLoading(true);
    setIsPasswordResetFlow(false);

    try {
      // Then sign out from Supabase
      await supabase.auth.signOut();

      // Clear any local storage items
      if (typeof window !== 'undefined') {
        localStorage.removeItem('opumap_map_state');
        // Clear any auth-related items from localStorage
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('sb-')) {
            localStorage.removeItem(key);
          }
        });
      }

      // Reset initial auth state after a short delay to ensure cleanup
      setTimeout(() => {
        setInitialAuthDone(true);
        setIsLoading(false);
      }, 100);

    } catch (error) {
      console.error('Error during sign out:', error);
      setInitialAuthDone(true);
      setIsLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    console.log("AuthContext: signInWithGoogle called. Resetting auth state");
    // Reset state before starting the OAuth flow
    setUser(null);
    setSupabaseUser(null);
    setSession(null);
    setInitialAuthDone(false);
    setIsLoading(true);

    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            // Force the consent screen to show every time
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error("Google Sign-in error:", error);
        setInitialAuthDone(true);
        setIsLoading(false);
        return { error };
      }

      // The actual authentication will be handled by the onAuthStateChange listener
      // which will be triggered after the OAuth flow completes
      return { error: null };
    } catch (error) {
      console.error("Unexpected error during Google sign-in:", error);
      setInitialAuthDone(true);
      setIsLoading(false);
      return { error: error as Error };
    }
  };

  const resetPassword = async (email: string) => {
    console.log("AuthContext: resetPassword called for email:", email);
    try {
      // Set password reset flow flag
      setIsPasswordResetFlow(true);
      
      // Explicitly set the recovery flow type for better handling in the callback
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/callback?next=/reset-password&type=recovery`,
      });

      if (error) {
        console.error("Password reset error in AuthContext:", error);
        setIsPasswordResetFlow(false);
        throw error;
      }

      console.log("Password reset email sent successfully");
      return { error: null };
    } catch (error) {
      console.error("Password reset error:", error);
      setIsPasswordResetFlow(false);
      return { error: error as Error };
    }
  };

  const updatePassword = async (newPassword: string) => {
    console.log("AuthContext: updatePassword called");
    try {
      // Set password reset flow flag
      setIsPasswordResetFlow(true);
      
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword,
      });
      
      if (error) throw error;
      
      // Reset the password reset flow flag after successful update
      setIsPasswordResetFlow(false);
      return { error: null };
      
    } catch (error) {
      console.error("Update password error:", error);
      setIsPasswordResetFlow(false);
      return { error: error as Error };
    }
  };

  const refreshSession = async () => {
    // Skip refresh if we're in password reset flow
    if (isPasswordResetFlow) {
      console.log('AuthContext: Skipping session refresh during password reset flow');
      return;
    }
    
    console.log("AuthContext: refreshSession called. Setting isLoading=true");
    setIsLoading(true); // Indicate loading state

    try {
      const { data, error } = await supabase.auth.refreshSession();
      // onAuthStateChange should ideally handle the session update and subsequent profile fetch.
      // However, if the session hasn't actually changed (e.g., it was already valid and just got refreshed with the same details),
      // onAuthStateChange might not fire. In such cases, we might need to manually trigger a profile update or ensure isLoading is reset.

      if (error) {
        console.error("AuthContext: Error refreshing session:", error);
        // Potentially handle specific errors, e.g., redirect to login if token is invalid
        // setUser(null); // Or based on error type
        // setSession(null);
      } else {
        console.log("AuthContext: Session refreshed successfully.", data);
        // If onAuthStateChange doesn't fire because the user/session ID remains the same,
        // we might need to manually update the session state here or re-fetch profile if necessary.
        // For now, relying on onAuthStateChange, but this is a point of attention.
        // If session object itself is new, update it:
        if (data.session) {
          setSession(data.session);
          if (data.user) {
            setSupabaseUser(data.user); // Ensure supabaseUser is also updated
            // If user data might have changed on the server, consider a profile refresh
            // await refreshUserProfile(); // This could be an option if needed
          }
        }
      }
    } catch (e) {
      console.error("AuthContext: Exception during refreshSession:", e);
      // setUser(null);
      // setSession(null);
    } finally {
      // Fallback – ensure UI is not stuck
      setIsLoading(false);
    }
  };

  const refreshUserProfile = async () => {
    if (!supabaseUser) {
      console.log("AuthContext: refreshUserProfile called but no supabaseUser.");
      return;
    }
    console.log("AuthContext: refreshUserProfile called for user ID:", supabaseUser.id);
    setIsLoading(true);
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', supabaseUser.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error("AuthContext: Error refreshing user profile:", profileError);
        // Potentially keep existing user data or set to basic
      } else if (profileData) {
        console.log("AuthContext: Profile refreshed successfully:", profileData);
        const fullUser: User = {
          id: supabaseUser.id,
          email: supabaseUser.email,
          name: profileData.name,
          company_name: profileData.company_name,
          address: profileData.address,
          phone: profileData.phone,
          website: profileData.website,
          employee_count: profileData.employee_count,
          company_info_points: profileData.company_info_points,
          createdAt: profileData.created_at,
          avatarUrl: profileData.avatar_url
        };
        setUser(fullUser);
      } else {
        console.log("AuthContext: No profile data found on refresh. User might not have a profile yet.");
        // Keep existing user data or set to basic if appropriate
        const basicUser = { id: supabaseUser.id, email: supabaseUser.email };
        setUser(basicUser);
      }
    } catch (e) {
      console.error("AuthContext: Exception during refreshUserProfile:", e);
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <AuthContext.Provider value={{
      user,
      supabaseUser,
      session,
      isLoading,
      initialAuthDone,
      isAuthReadyForDataFetch,
      signIn,
      signUp,
      signOut,
      signInWithGoogle,
      resetPassword,
      updatePassword,
      refreshSession,
      refreshUserProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === null) { // Check for null specifically
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
