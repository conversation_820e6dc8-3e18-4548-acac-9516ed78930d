import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server'; // Ensure this path is correct for your server-side Supabase client

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/';

  if (code) {
    const supabase = await createClient(); // Added await here
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    if (!error) {
      // Check for X-Forwarded-Host for correct redirect in production environments (e.g., behind a load balancer)
      const forwardedHost = request.headers.get('x-forwarded-host');
      const isLocalEnv = process.env.NODE_ENV === 'development';

      let redirectUrl = origin;
      if (!isLocalEnv && forwardedHost) {
        redirectUrl = `https://${forwardedHost}`;
      }
      
      return NextResponse.redirect(`${redirectUrl}${next}`);
    }
  }

  // return the user to an error page with instructions
  console.error('Error exchanging code for session or no code provided');
  return NextResponse.redirect(`${origin}/auth/auth-code-error`);
}
