# OpioScanner Komponente

Diese Komponente dient als Hauptseite für den OpioScanner, der eine KI-gestützte Analyse von Unternehmen und Strategien ermöglicht.

## Struktur

Die OpioScanner-Komponente folgt dem gleichen Organisationsansatz wie andere Seitenkomponenten:

- **Hauptkomponente** - `page.tsx` enthält die Hauptlogik und das UI der OpioScanner-Seite
- **Seitenspezifische Komponenten** befinden sich im `components/`-Unterordner:
  - `GlitchButton.tsx` - Spezieller Button mit Glitch-Effekt für Desktop
  - `TouchGlitchButton.tsx` - Optimierter Glitch-Button für Touch-Geräte
- **Seitenspezifische Styles** können bei Bedarf im `styles/`-Unterordner hinzugefügt werden

## Funktionalität

- Anzeige und Bearbeitung von Unternehmensinformationen
- Auswahl von Strategieoptionen
- Verwaltung ausgewählter Unternehmen
- Integration mit der Karten-Komponente (Unternehmen können von der Karte hinzugefügt werden)
- Durchführung von KI-gestützten Analysen
- Anzeige erkannter Chancen
- Responsive Design für verschiedene Bildschirmgrößen
- Unterstützung für Dark Mode

## API-Integration

Die Komponente kommuniziert mit folgenden API-Endpunkten:
- `/api/profile` - Für das Speichern der Unternehmensinformationen
- `/api/selected-companies` - Für die Verwaltung der ausgewählten Unternehmen

## Verwendung

Die OpioScanner-Komponente ist über die Route `/opuscanner` erreichbar und sollte nur für authentifizierte Benutzer zugänglich sein.
