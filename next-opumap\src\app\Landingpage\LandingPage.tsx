'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, MapPin, Search, Users, ArrowRight, ChevronRight, CheckCircle2 } from 'lucide-react';

// Import custom components
import {
  Button,
  Card, CardContent, CardHeader, CardTitle,
  Input,
  Separator,
  Badge,
  buttonVariants
} from '@/components/ui';

// Animation variants
import type { Variants } from 'framer-motion';

const container: Variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const item: Variants = {
  hidden: { opacity: 0, y: 20 },
  show: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.5,
      ease: [0.16, 1, 0.3, 1] as const
    } 
  }
};

const features = [
  {
    icon: <MapPin className="w-6 h-6 text-primary" />,
    title: 'Strategische Standortanalyse',
    description: 'Identifizieren Sie strategisch interessante Unternehmen basierend auf Ihrer Geschäftslogik und Marktpräsenz.',
    gradient: 'from-blue-500 to-cyan-400',
    hoverGradient: 'from-blue-600 to-cyan-500',
    delay: 0.1
  },
  {
    icon: <Search className="w-6 h-6 text-primary" />,
    title: 'Erweiterte Filter & Kriterien',
    description: 'Verfeinern Sie Ihre Suche mit leistungsstarken Filtern nach Branche, Unternehmensgröße, Wachstum und mehr.',
    gradient: 'from-purple-500 to-pink-500',
    hoverGradient: 'from-purple-600 to-pink-600',
    delay: 0.2
  },
  {
    icon: <Users className="w-6 h-6 text-primary" />,
    title: 'Partnerschafts-Potenzial',
    description: 'Entdecken Sie Unternehmen mit komplementären Stärken für strategische Allianzen und Kooperationen.',
    gradient: 'from-emerald-500 to-teal-400',
    hoverGradient: 'from-emerald-600 to-teal-500',
    delay: 0.3
  },
  {
    icon: <ArrowRight className="w-6 h-6 text-primary" />,
    title: 'Direkter Kontakt',
    description: 'Starten Sie gezielte Vertriebs- und Kooperationsanfragen direkt über die Plattform.',
    gradient: 'from-amber-500 to-orange-400',
    hoverGradient: 'from-amber-600 to-orange-500',
    delay: 0.4
  }
];

const benefits = [
  'Keine Kreditkarte erforderlich',
  '14 Tage kostenlos testen',
  'Jederzeit kündbar',
  'Dedizierter Support'
];

const LandingPage: React.FC = () => {
  const router = useRouter();
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLoginClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isAnimating) return;

    setIsAnimating(true);
    document.body.classList.add('page-transition-active');

    setTimeout(() => {
      router.push('/login');
      setTimeout(() => {
        document.body.classList.remove('page-transition-active');
      }, 50);
    }, 300);
  };

  const scrollToFeatures = () => {
    const featuresSection = document.getElementById('features');
    featuresSection?.scrollIntoView({ behavior: 'smooth' });
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  if (!isMounted) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
          className="w-full"
        >
          {/* Background Elements - Consistent across all sections */}
          <div className="fixed inset-0 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-background" />
            <div className="absolute inset-0 bg-dot-pattern opacity-5" />
            <div className="absolute inset-0 bg-grid-pattern opacity-5" />
            
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-background/80 via-background/20 to-background/80" />
          </div>

          {/* Hero Section */}
          <section className="relative pt-24 pb-16 md:pt-32 md:pb-24 lg:pt-40 lg:pb-32 bg-background">
          <div className="container px-4 mx-auto">
            <div className="flex flex-col items-center max-w-4xl mx-auto text-center">
              {/* Animated badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
                className="inline-flex items-center px-4 py-1.5 mb-6 text-sm font-medium rounded-full bg-primary/10 text-primary"
              >
                <span className="relative flex w-2 h-2 mr-2">
                  <span className="absolute inline-flex w-full h-full rounded-full opacity-75 bg-primary animate-ping" />
                  <span className="relative inline-flex w-2 h-2 rounded-full bg-primary" />
                </span>
                Jetzt verfügbar • Trusted by leading companies
              </motion.div>

              {/* Main heading */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2, ease: [0.16, 1, 0.3, 1] }}
                className="max-w-4xl mx-auto"
              >
                <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
                  <span className="block bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80">
                    Strategische Partner &<br className="hidden sm:inline" /> Verkaufschancen
                  </span>
                  <span className="relative inline-block mt-2 sm:mt-3">
                    <span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                      auf einen Blick
                    </span>
                    <span className="absolute bottom-1.5 left-0 w-full h-3 bg-primary/10 -z-10 rounded-full" />
                  </span>
                </h1>
              </motion.div>

              {/* Description */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3, ease: [0.16, 1, 0.3, 1] }}
                className="mt-6 text-lg leading-8 text-muted-foreground max-w-2xl"
              >
                OpuMap revolutioniert die Geschäftsentwicklung durch intelligente Standortanalyse und strategische Marktübersicht für zielgerichtetes Business Development.
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4, ease: [0.16, 1, 0.3, 1] }}
                className="flex flex-wrap items-center justify-center gap-4 mt-10"
              >
                <Button 
                  onClick={handleLoginClick}
                  size="lg"
                  className="group relative overflow-hidden px-8 py-6 text-base font-medium transition-all duration-300 hover:shadow-lg hover:shadow-primary/20"
                >
                  <span className="relative z-10">Jetzt starten</span>
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                  <span className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  className="px-8 py-6 text-base group"
                  onClick={scrollToFeatures}
                >
                  <span className="relative z-10">Mehr erfahren</span>
                  <ChevronRight className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                </Button>
              </motion.div>

              {/* Benefits list */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5, ease: [0.16, 1, 0.3, 1] }}
                className="flex flex-wrap items-center justify-center gap-6 mt-8 text-sm text-muted-foreground"
              >
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle2 className="w-4 h-4 mr-2 text-primary" />
                    <span>{benefit}</span>
                  </div>
                ))}
              </motion.div>
            </div>

            {/* Dashboard preview */}
            <motion.div
              initial={{ opacity: 0, y: 40, scale: 0.98 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ 
                duration: 0.8, 
                delay: 0.6,
                ease: [0.16, 1, 0.3, 1],
                scale: { 
                  type: 'spring', 
                  stiffness: 100, 
                  damping: 20 
                }
              }}
              className="relative w-full max-w-6xl mx-auto mt-16 rounded-2xl overflow-hidden shadow-2xl border border-border/50 bg-background/50 backdrop-blur-sm"
            >
              {/* Browser chrome */}
              <div className="flex items-center justify-between p-3 border-b border-border/50">
                <div className="flex gap-1.5">
                  <div className="w-3 h-3 rounded-full bg-red-500" />
                  <div className="w-3 h-3 rounded-full bg-yellow-500" />
                  <div className="w-3 h-3 rounded-full bg-green-500" />
                </div>
                <div className="text-xs text-muted-foreground">
                  OpuMap Business Intelligence Dashboard
                </div>
                <div className="w-16"></div> {/* Spacer for centering */}
              </div>
              
              {/* Dashboard content */}
              <div className="aspect-video bg-muted/20 flex items-center justify-center p-8">
                <div className="text-center max-w-2xl">
                  <div className="relative inline-block">
                    <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-xl blur opacity-30"></div>
                    <div className="relative p-8 bg-background/80 backdrop-blur-sm rounded-xl border border-border/50 shadow-lg">
                      <div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-primary/10">
                        <MapPin className="w-8 h-8 text-primary" />
                      </div>
                      <h3 className="text-2xl font-bold tracking-tight text-foreground">Interaktive Marktanalyse</h3>
                      <p className="mt-3 text-muted-foreground">
                        Visualisieren Sie Marktchancen und strategische Partner in Ihrer Zielregion mit unserem leistungsstarken Analyse-Dashboard.
                      </p>
                      <div className="mt-6">
                        <Button variant="outline" className="group">
                          Demo ansehen
                          <ChevronRight className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
          </section>

          {/* Features Section */}
          <section id="features" className="relative py-24 overflow-hidden bg-background">
            {/* Background pattern */}
            <div className="absolute inset-0 -z-10">
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-background/70 via-background/90 to-background" />
            </div>
            {/* Animated background elements */}
            <div className="absolute inset-0 -z-10 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-b from-background to-muted/30" />
              <div className="absolute inset-0 bg-grid-pattern opacity-5" />
              
              {/* Floating gradient orbs */}
              <motion.div 
                className="absolute w-64 h-64 rounded-full bg-gradient-to-r from-primary/20 to-transparent -left-32 -top-32 blur-3xl"
                animate={{
                  y: [0, 15, 0],
                  scale: [1, 1.05, 1],
                }}
                transition={{
                  duration: 12,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <motion.div 
                className="absolute w-96 h-96 rounded-full bg-gradient-to-r from-secondary/15 to-transparent -right-48 -bottom-48 blur-3xl"
                animate={{
                  y: [0, -20, 0],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 15,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2
                }}
              />
            </div>
            
            <div className="container px-4 mx-auto">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
                className="max-w-4xl mx-auto text-center"
              >
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  whileInView={{ scale: 1, opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <Badge 
                    variant="outline" 
                    className="relative px-4 py-1.5 mb-6 text-sm font-medium rounded-full border-primary/20 bg-background/80 backdrop-blur-sm group"
                  >
                    <span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                      Leistungsstarke Funktionen
                    </span>
                    <span className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Badge>
                </motion.div>
                
                <motion.h2 
                  className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  Alles für Ihren <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">strategischen Vorsprung</span>
                </motion.h2>
                
                <motion.p 
                  className="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  Entdecken Sie, wie OpuMap Ihre Geschäftsentwicklung mit intelligenten Analysen und präzisen Markteinblicken revolutioniert.
                </motion.p>
              </motion.div>

              <motion.div 
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, margin: "-100px" }}
                className="grid gap-8 mt-20 sm:grid-cols-2 lg:grid-cols-2"
              >
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    variants={item}
                    className="group relative p-8 bg-background/80 backdrop-blur-sm rounded-2xl border border-border/30 shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-2 overflow-hidden"
                    whileHover={{ 
                      scale: 1.02,
                      transition: { duration: 0.3 }
                    }}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-50px" }}
                    transition={{ 
                      duration: 0.6, 
                      delay: 0.1 * index,
                      ease: [0.16, 1, 0.3, 1] 
                    }}
                  >
                    {/* Animated gradient border */}
                    <div className={`absolute inset-0 rounded-2xl p-[1px] bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}>
                      <div className="absolute inset-0 bg-background/80 rounded-[15px]" />
                    </div>
                    
                    {/* Feature content */}
                    <div className="relative z-10">
                      <div className={`flex items-center justify-center w-14 h-14 mb-6 rounded-xl bg-gradient-to-br ${feature.gradient} text-white shadow-lg shadow-${feature.gradient.split(' ')[0].replace('from-', '')}/20`}>
                        {React.cloneElement(feature.icon, { className: 'w-6 h-6' })}
                      </div>
                      
                      <h3 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/90">
                        {feature.title}
                      </h3>
                      
                      <p className="mt-3 text-muted-foreground">
                        {feature.description}
                      </p>
                      
                      <div className="absolute bottom-6 right-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">
                        <ChevronRight className="w-5 h-5 text-primary" />
                      </div>
                      
                      {/* Subtle hover effect */}
                      <div className={`absolute -bottom-8 -right-8 w-32 h-32 rounded-full bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />
                    </div>
                    
                    {/* Animated dots pattern */}
                    <div className="absolute -bottom-4 -right-4 w-24 h-24 opacity-10">
                      <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
                        <circle cx="20" cy="20" r="2" fill="currentColor" className="text-primary">
                          <animate attributeName="r" values="2;3;2" dur="3s" repeatCount="indefinite" />
                        </circle>
                        <circle cx="50" cy="20" r="2" fill="currentColor" className="text-primary">
                          <animate attributeName="r" values="2;3;2" dur="3s" begin="0.5s" repeatCount="indefinite" />
                        </circle>
                        <circle cx="80" cy="20" r="2" fill="currentColor" className="text-primary">
                          <animate attributeName="r" values="2;3;2" dur="3s" begin="1s" repeatCount="indefinite" />
                        </circle>
                      </svg>
                    </div>
                  </motion.div>
                ))}
              </motion.div>

          {/* CTA Card */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative mt-24 overflow-hidden rounded-2xl border border-border/50 bg-muted"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10" />
            <div className="absolute inset-0 bg-grid-pattern opacity-5" />
            <div className="relative px-6 py-12 sm:px-12 sm:py-16 lg:px-16">
              <div className="max-w-3xl mx-auto text-center">
                <h3 className="text-2xl font-bold tracking-tight sm:text-3xl text-foreground">
                  Bereit für den nächsten Schritt?
                </h3>
                <p className="mt-4 text-lg text-muted-foreground">
                  Starten Sie noch heute mit OpuMap und entdecken Sie neue Geschäftschancen in Ihrer Region.
                </p>
                <div className="flex flex-wrap items-center justify-center gap-4 mt-8">
                  <Button 
                    onClick={handleLoginClick}
                    size="lg"
                    className="group relative overflow-hidden px-8 py-6 text-base font-medium transition-all duration-300 hover:shadow-lg hover:shadow-primary/20"
                  >
                    <span className="relative z-10">Kostenlos starten</span>
                    <ArrowRight className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                    <span className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="lg"
                    className="px-8 py-6 text-base group"
                  >
                    <span className="relative z-10">Demo anfragen</span>
                    <ChevronRight className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </div>
                <p className="mt-4 text-sm text-muted-foreground">
                  Keine Kreditkarte erforderlich • 14 Tage kostenlos testen
                </p>
              </div>
            </div>
          </motion.div>
          </div>
        </section>

        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default LandingPage;
