import dotenv from 'dotenv';
import pkg from 'pg'; // Import the default export
const { Client } = pkg; // Destructure Client from the default export
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env.local in the parent directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '../.env.local') });

async function setupDatabase() {
  console.log('Starting database setup using pg client...');
  const connectionString = process.env.POSTGRES_URL;

  if (!connectionString) {
    console.error('Error: POSTGRES_URL environment variable not found in .env.local');
    process.exit(1);
  }

  console.log(`Attempting to connect with URL: ${connectionString.replace(/postgres:.*@/, 'postgres://<user>:<password>@')}`);

  // Use the pg Client directly, relying on NODE_TLS_REJECT_UNAUTHORIZED=0 for SSL bypass
  const client = new Client({ connectionString });

  try {
    await client.connect();
    console.log('Database client connected.');

    // Ensure users table exists
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100),
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        company_name VARCHAR(100),
        address TEXT,
        phone VARCHAR(50),
        website VARCHAR(255),
        description TEXT,
        industry VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log("Users table ensured.");

    // Ensure schnelle_analyse table exists
    await client.query(`
      CREATE TABLE IF NOT EXISTS schnelle_analyse (
        id SERIAL PRIMARY KEY,
        place_id VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        address TEXT,
        phone VARCHAR(50),
        website VARCHAR(255),
        lat DECIMAL(10, 8),
        lng DECIMAL(11, 8),
        analysis_content TEXT NOT NULL,
        analysis_date TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log("Schnelle Analyse table ensured.");

    // Ensure index on schnelle_analyse.place_id exists
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_schnelle_analyse_place_id ON schnelle_analyse(place_id);
    `);
    console.log("Index on schnelle_analyse.place_id ensured.");

    // Ensure tiefe_analyse table exists
    await client.query(`
      CREATE TABLE IF NOT EXISTS tiefe_analyse (
        id SERIAL PRIMARY KEY,
        place_id VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        address TEXT,
        phone VARCHAR(50),
        website VARCHAR(255),
        lat DECIMAL(10, 8),
        lng DECIMAL(11, 8),
        analysis_content TEXT NOT NULL,
        analysis_date TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log("Tiefe Analyse table ensured.");

    // Ensure index on tiefe_analyse.place_id exists
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_tiefe_analyse_place_id ON tiefe_analyse(place_id);
    `);
    console.log("Index on tiefe_analyse.place_id ensured.");

    // Add missing columns to users table (idempotent)
    const columnsToAdd = [
      { name: 'company_name', type: 'VARCHAR(100)' },
      { name: 'address', type: 'TEXT' },
      { name: 'phone', type: 'VARCHAR(50)' },
      { name: 'website', type: 'VARCHAR(255)' },
      { name: 'description', type: 'TEXT' },
      { name: 'industry', type: 'VARCHAR(100)' },
      { name: 'company_info_points', type: 'TEXT' }
    ];

    console.log("Ensuring columns exist in users table...");
    for (const col of columnsToAdd) {
      try {
        await client.query(`ALTER TABLE users ADD COLUMN IF NOT EXISTS ${col.name} ${col.type}`);
      } catch (alterError: unknown) {
        const message = alterError instanceof Error ? alterError.message : 'Unknown error';
        console.error(`   Error trying to add column ${col.name}:`, message);
      }
    }
    console.log("Finished checking/adding columns to users table.");

    console.log('Database setup completed successfully.');

  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error during setup';
    console.error('Database setup failed:', message);
    if (error instanceof Error && 'code' in error) {
        console.error('Code:', (error as any).code);
    }
     if (error instanceof Error && 'routine' in error) {
        console.error('Routine:', (error as any).routine);
    }
    process.exit(1);
  } finally {
    await client.end();
    console.log('Database client disconnected.');
  }
}

setupDatabase();
