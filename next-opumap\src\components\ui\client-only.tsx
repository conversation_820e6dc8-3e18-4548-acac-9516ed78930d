'use client'

import React, { useState, useEffect } from 'react'

/**
 * Komponente, die ihre Kinder nur clientseitig rendert
 * Dies verhindert Hydration-Fehler bei SVGs und anderen komplexen Komponenten
 */
type ClientOnlyProps = {
  children: React.ReactNode
}

export const ClientOnly = ({ children }: ClientOnlyProps) => {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return null
  }

  return <>{children}</>
}

/**
 * SVG-spezifische Variante der ClientOnly Komponente 
 * Leitet alle SVG-Props direkt weiter
 */
export const ClientOnlySVG = (props: React.SVGProps<SVGSVGElement>) => {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  // Nichts rendern, bis wir auf dem Client sind
  if (!hasMounted) {
    return null
  }

  return <svg {...props} />
}

/**
 * Icon-spezifische Variante der ClientOnly Komponente
 * Leitet alle div-Props direkt weiter und wickelt Icons ein
 */
export const ClientOnlyIcon = (props: React.HTMLAttributes<HTMLDivElement> & { 
  children: React.ReactNode,
  width?: string,
  height?: string 
}) => {
  const [hasMounted, setHasMounted] = useState(false)
  const { children, className, width, height, ...rest } = props

  useEffect(() => {
    setHasMounted(true)
  }, [])

  // Nichts rendern, bis wir auf dem Client sind
  if (!hasMounted) {
    return null
  }

  return (
    <div 
      className={className} 
      style={{ 
        display: 'inline-flex', 
        width: width || 'auto', 
        height: height || 'auto' 
      }} 
      {...rest}
    >
      {children}
    </div>
  )
}
