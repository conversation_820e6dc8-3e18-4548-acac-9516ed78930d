import client from '@/lib/opulabAIClient';

export async function runOpulabPriorisierung(
  companyInfo: string,
  shortTerm: number,
  longTerm: number
): Promise<{ prompt: string; content: string }> {
  let prompt = `Gegebene Unternehmensinformationen:\n///\n${companyInfo}\n///\n\n`;
  prompt +=
    "Nutze die gegebenen Unternehmensinformationen und befolge vollständig den folgenden gegebenen Aspekt und die bezogenen Aufgaben. " +
    "Dein Output soll keinen unnötigen Fluff, keine gesonderte Formatierung und auch keine unnötige Einleitung haben, " +
    "aber dennoch eine Erklärende Sichtweise einnehmen und die Ergebnisse als gegebene Anforderungen ausgeben:\n\n///\n";
  prompt +=
    `Bei der Strategieentwicklung ist zu beachten, dass die kurzfristige Umsetzbarkeit eine Wichtigkeit von ${shortTerm} von 100% besitzt, ` +
    `während der langfristige Erfolg eine Relevanz von ${longTerm} von 100% aufweist.\n\n`;
  prompt += `Kurze Erläuterung der Werte:\n\n`;
  prompt +=
    "Kurzfristige Umsetzbarkeit: Ein hoher Wert in diesem Bereich deutet darauf hin, dass schnelle, " +
    "unmittelbar umsetzbare Maßnahmen bevorzugt werden. Dies ist besonders wichtig in Umfeldern, in denen rasche Anpassungen " +
    "erforderlich sind, um Wettbewerbsvorteile zu sichern oder auf kurzfristige Marktveränderungen zu reagieren.\n\n";
  prompt +=
    "Langfristiger Erfolg: Ein hoher Wert hier legt den Fokus auf nachhaltige und zukunftsorientierte Strategien, " +
    "die auf langfristiges Wachstum und Stabilität ausgerichtet sind.\n\n";
  prompt += `Auswirkungen auf die Strategieentwicklung:\n\n`;
  prompt +=
    "Hohe kurzfristige Umsetzbarkeit, geringer langfristiger Erfolg (z. B. 85/23): Diese Einstellung legt Gewicht auf schnell " +
    "erreichbare Ziele, was in dynamischen oder volatilen Märkten vorteilhaft sein kann. Achten Sie darauf, dass kurzfristige " +
    "Lösungen nicht zu Lasten der langfristigen Unternehmensziele gehen.\n\n";
  prompt +=
    "Hohe kurzfristige Umsetzbarkeit und hoher langfristiger Erfolg (z. B. 70/90): Diese Balance strebt sowohl schnelle " +
    "Umsetzungsschritte als auch eine nachhaltige Zukunftsplanung an. Bei dieser Konstellation ist es wichtig, Ressourcen " +
    "effizient zu nutzen, um beide Zielsetzungen gleichzeitig zu verfolgen.\n\n";
  prompt +=
    "Geringe kurzfristige Umsetzbarkeit, hoher langfristiger Erfolg (z. B. 30/90): Solch eine Priorisierung favorisiert stabile, " +
    "langfristige Planungen über sofortige Ergebnisse. Dies kann vorteilhaft sein, wenn die Unternehmen in stabilen Märkten " +
    "agieren oder einen klaren Fokus auf Innovationsführerschaft haben.\n\n";
  prompt +=
    "Geringe Werte in beiden Bereichen (z. B. 40/40): Wenn beide Werte moderat sind, könnte dies auf eine vorsichtige oder " +
    "abwarten Strategie hindeuten, die möglicherweise Gelegenheiten zur kurzfristigen Anpassung oder langfristigem Wachstum verpasst.\n\n";
  prompt += `Empfehlungen und Zielsetzungen:\n\n`;
  prompt +=
    "Kurzfristige Ziele: Betonen Sie schnelle Erfolge in Bereichen, die Ihre kurzfristige Umsetzbarkeit maximieren.\n\n";
  prompt +=
    "Langfristige Unternehmensziele: Richten Sie strategische Initiativen darauf aus, nachhaltige Wettbewerbsvorteile zu erlangen " +
    "und langfristige Erfolgspotentiale zu maximieren.\n\n";
  prompt +=
    "Durch die richtige Balance dieser beiden Werte in der strategischen Planung können Organisationen sowohl reaktive als auch " +
    "proaktive Maßnahmen effektiv gestalten. Dies erfordert eine regelmäßige Überprüfung und Anpassung der Strategie, um sowohl " +
    "adaptiert als auch innovativ zu bleiben.\n\n///\n";

  const completion = await client.chat.completions.create({
    model: "google/gemma-3-27b-it:free",
    messages: [
      { role: "system", content: "Bitte präzise Antwort liefern." },
      { role: "user", content: prompt }
    ]
  });

  const content = completion.choices?.[0]?.message?.content ?? "";
  return { prompt, content };
}
