'use client';
import React from 'react';
import { OpulabStrategy, StrategyOption as StandardStrategyOption } from '../types'; // Adjusted path
import { Badge } from '@/components/ui/badge';
import { Loader2, HelpCircle } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface StrategySelectionProps {
  selectedOption: string;
  onValueChange: (value: string) => void;
  opulabStrategies: OpulabStrategy[];
  standardOptions: StandardStrategyOption[];
  isLoadingStrategies: boolean;
  loadingError: string | null;
  isSaving: boolean; // To disable selection during saves
}

export const StrategySelection: React.FC<StrategySelectionProps> = ({
  selectedOption,
  onValueChange,
  opulabStrategies,
  standardOptions,
  isLoadingStrategies,
  loadingError,
  isSaving,
}) => {
  return (
    <>
      <h2 className="font-semibold text-lg text-card-foreground mb-5 border-b border-border pb-3 flex-shrink-0">
        Strategiewunsch
      </h2>
      <div className="h-[400px] overflow-y-auto border border-border rounded-md p-3 custom-scrollbar">
        {loadingError && <p className="text-red-500 p-4">{loadingError}</p>}
        <TooltipProvider delayDuration={200}>
          <RadioGroup
            value={selectedOption}
            onValueChange={onValueChange}
            className="space-y-3"
            aria-label="Strategiewunsch"
          >
            {/* Opulab Strategies Section */}
            {isLoadingStrategies && (
              <div className="flex items-center space-x-2 text-muted-foreground p-3 border rounded-lg border-border">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Lade Opulab Strategien...</span>
              </div>
            )}
            {!isLoadingStrategies && opulabStrategies.length > 0 && (
              <div className="pt-2 pb-1">
                <h3 className="text-sm font-medium text-muted-foreground mb-2 ml-1 flex items-center">
                  <Badge variant="secondary" className="mr-2">Opulab</Badge> Erstellte Strategien
                </h3>
              </div>
            )}
            {!isLoadingStrategies && opulabStrategies.map((strategy) => {
              const hasAnalysis = !!strategy.analysis_result && strategy.analysis_result.trim() !== '';
              const isDisabled = !hasAnalysis || isSaving;
              const id = `opulab_${strategy.id}`;
              const tooltipContent = isDisabled
                ? (hasAnalysis ? 'Scan läuft oder Info wird gespeichert...' : `Strategie '${strategy.strategy_name}' hat noch keine abgeschlossene Analyse`)
                : `Wähle Opulab Strategie: ${strategy.strategy_name}`;

              return (
                <Tooltip key={id}>
                  <TooltipTrigger asChild>
                    <div
                      className={`flex items-center justify-between p-3 border rounded-lg transition-colors duration-150 ease-in-out ${
                        selectedOption === id ? 'bg-primary/10 border-primary/30' : 'border-border'
                      } ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-muted/50'}`}
                      onClick={() => !isDisabled && onValueChange(id)}
                    >
                      <Label
                        htmlFor={id}
                        className={`flex items-center flex-grow ${isDisabled ? 'text-muted-foreground/70' : 'cursor-pointer'} ${selectedOption === id ? 'text-foreground font-medium' : 'text-muted-foreground'}`}
                      >
                        <RadioGroupItem value={id} id={id} className="mr-3 flex-shrink-0" disabled={isDisabled} />
                        <span>{strategy.strategy_name}</span>
                      </Label>
                      {/* No info icon needed for Opulab strategies currently */}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{tooltipContent}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
            {!isLoadingStrategies && opulabStrategies.length === 0 && !loadingError && (
              <div className="pt-2 pb-1">
                <h3 className="text-sm font-medium text-muted-foreground mb-2 ml-1 flex items-center">
                  <Badge variant="secondary" className="mr-2">Opulab</Badge> Erstellte Strategien
                </h3>
                <p className="text-sm text-muted-foreground p-3 border rounded-lg border-dashed border-border">
                  Keine Opulab Strategien gefunden oder erstellt.
                </p>
              </div>
            )}

            {/* Separator if both Opulab and Standard options exist and Opulab are loaded */}
            {!isLoadingStrategies && opulabStrategies.length > 0 && standardOptions.length > 0 && (
              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-border"></span>
                </div>
                <div className="relative flex justify-center">
                  <span className="bg-card px-2 text-xs uppercase text-muted-foreground">Oder Standard</span>
                </div>
              </div>
            )}

            {/* Standard Strategy Options Section */}
            {standardOptions.map((option) => {
              const id = option.value;
              const isDisabled = isSaving; // Standard options only disabled by saving
              return (
                <Tooltip key={id}>
                  <TooltipTrigger asChild>
                    <div
                      className={`flex items-center justify-between p-3 border rounded-lg transition-colors duration-150 ease-in-out ${
                        selectedOption === id ? 'bg-primary/10 border-primary/30' : 'border-border'
                      } ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-muted/50'}`}
                      onClick={() => !isDisabled && onValueChange(id)}
                    >
                      <Label
                        htmlFor={id}
                        className={`flex items-center flex-grow ${isDisabled ? 'text-muted-foreground/70' : 'cursor-pointer'} ${selectedOption === id ? 'text-foreground font-medium' : 'text-muted-foreground'}`}
                      >
                        <RadioGroupItem value={id} id={id} className="mr-3 flex-shrink-0" disabled={isDisabled} />
                        <span>{option.label}</span>
                      </Label>
                      <Tooltip>
                        <TooltipTrigger asChild onClick={(e: React.MouseEvent) => e.stopPropagation()}> 
                          <HelpCircle className="w-5 h-5 text-muted-foreground hover:text-primary cursor-help ml-2 flex-shrink-0" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{option.info}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </TooltipTrigger>
                  {/* No outer tooltip needed for standard options */}
                </Tooltip>
              );
            })}
          </RadioGroup>
        </TooltipProvider>
      </div>
    </>
  );
}; 