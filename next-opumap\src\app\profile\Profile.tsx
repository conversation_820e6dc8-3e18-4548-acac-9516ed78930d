'use client'; // Mark as client component

import React, { useState, useEffect, FormEvent } from 'react';
import { useAuth } from '@/contexts/AuthContext'; // Adjust import path
import { runAnalysis } from '@/api/unternehmensanalyse'; // Adjust import path
import { updateProfile } from './actions/update-profile';
import { Card, CardHeader, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Edit, Settings, Activity, Loader } from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';
import { Notification } from '@/components/ui/notification';
import { useNotification } from './NotificationState';
import MarkdownEditor from '@/components/MarkdownEditor';  // Import the new component

const Profile: React.FC = () => {
  // Use the auth hook
  const { user, isLoading, refreshUserProfile } = useAuth();

  // State with types
  const [companyName, setCompanyName] = useState<string>('');
  const [name, setName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [address, setAddress] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [website, setWebsite] = useState<string>('');
  // Ensure employeeCount is treated as string for input, but potentially number elsewhere
  const [employeeCount, setEmployeeCount] = useState<string | number>('');
  const [companyInfoPoints, setCompanyInfoPoints] = useState<string>('');

  const { isVisible, message, type, details, showNotification, hideNotification } = useNotification();
  const notificationTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // Update local form state when user data loads or changes
  useEffect(() => {
    if (!isLoading && user) {
      setCompanyName(user.company_name ?? '');
      setName(user.name ?? '');
      setEmail(user.email ?? '');
      setAddress(user.address ?? '');
      setPhone(user.phone ?? '');
      setWebsite(user.website ?? '');
      setEmployeeCount(user.employee_count ?? '');
      setCompanyInfoPoints(user.company_info_points ?? '');
    }
     // Clear form if user logs out (user becomes null) and loading is done
     else if (!isLoading && !user) {
        setCompanyName('');
        setName('');
        setEmail('');
        setAddress('');
        setPhone('');
        setWebsite('');
        setEmployeeCount('');
        setCompanyInfoPoints('');
     }
  }, [user, isLoading]);

  // Type the event
  const handleSave = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Set saving state to show loading indicator
    setIsSaving(true);

    // Create a FormData object from the form
    const formData = new FormData();
    formData.append('name', name);
    formData.append('email', email || '');
    formData.append('company_name', companyName);
    formData.append('address', address);
    formData.append('phone', phone);
    formData.append('website', website);
    formData.append('employee_count', employeeCount.toString());
    formData.append('company_info_points', companyInfoPoints);

    try {
      // Use the server action to update the profile
      const result = await updateProfile(formData);

      // Check if result is undefined or has an error property
      if (!result || result.error) {
        // If result is undefined, throw a generic error, otherwise throw the result.error
        throw new Error(result ? result.error : 'Unbekannter Fehler beim Speichern des Profils');
      }

      // Refresh the user profile data to get the latest changes
      const refreshSuccess = await refreshUserProfile();

      console.log("Profile updated and refreshed successfully:", refreshSuccess);

      // Show success notification
      showNotification('Profil erfolgreich aktualisiert.', 'success');
      if (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);
      notificationTimeoutRef.current = setTimeout(hideNotification, 5000);

    } catch (error) { // Use unknown type for error
      console.error("Profile update error:", error);
      const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
      showNotification(`Fehler: ${message}`, 'error');
      if (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);
      notificationTimeoutRef.current = setTimeout(hideNotification, 5000);
    } finally {
      // Reset saving state
      setIsSaving(false);
    }
  };

  const handleAnalyse = async () => {
    // removed obsolete setMessage('');
    setIsAnalyzing(true);

    // Define type for analysis input data
    interface CompanyAnalysisData {
        companyName: string;
        name: string;
        address: string;
        phone: string;
        website: string;
        employeeCount: string | number;
    }

    const companyData: CompanyAnalysisData = {
      companyName,
      name,
      address,
      phone,
      website,
      employeeCount
    };
    showNotification('Unternehmensanalyse wird durchgeführt, bitte warten...', 'success');
    try {
      // Assuming runAnalysis returns a string
      const analysisResult: string = await runAnalysis(companyData);
      setCompanyInfoPoints(analysisResult);
      showNotification('Analyse abgeschlossen.', 'success');
if (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);
notificationTimeoutRef.current = setTimeout(hideNotification, 3000);
    } catch (error) { // Use unknown type for error
      const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
      showNotification(`Analyse fehlgeschlagen: ${message}`, 'error');
if (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);
notificationTimeoutRef.current = setTimeout(hideNotification, 3000);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Loading state from context
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <p className="text-center text-gray-500">Lade Profil...</p>
      </div>
    );
  }

  // No user state (either not logged in or context failed)
  if (!user) {
    return (
      <div className="max-w-xl mx-auto mt-10 p-6 border rounded shadow-lg text-center">
        <h2 className="text-xl font-semibold mb-4">Zugriff verweigert</h2>
        <p>Sie müssen eingeloggt sein, um Ihr Profil anzuzeigen und zu bearbeiten.</p>
        {/* Optional: Add a link to the login page */}
         {/* <Link href="/login"><a className="text-blue-500 hover:underline mt-4 inline-block">Zum Login</a></Link> */}
      </div>
    );
  }

  // User is loaded and exists
  return (
    <form className="max-w-4xl mx-auto my-8 p-4 space-y-6" onSubmit={handleSave}>
      <Notification
  type={type}
  message={message}
  details={details}
  isVisible={isVisible && !!message}
  onDismiss={hideNotification}
/>
      <Card className="shadow-xl rounded-2xl p-6">
        <CardHeader className="pb-4">
          <div className="flex flex-col md:flex-row items-center md:items-end gap-6 md:gap-10">
            <div className="relative flex-shrink-0">
              <Avatar
                src={user.avatarUrl ?? undefined}
                initials={user.name ? user.name.split(' ').map((n: string) => n[0]).join('').slice(0,2).toUpperCase() : '?'}
                alt={user.name ?? undefined}
                className="w-24 h-24 md:w-32 md:h-32"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                aria-label="Profil bearbeiten"
                className="absolute bottom-0 right-0 bg-card border border-border shadow-md rounded-full w-8 h-8 flex items-center justify-center"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex-1 min-w-0 text-center md:text-left">
              <h1 className="text-3xl md:text-4xl font-bold text-foreground truncate">{user.name}</h1>
              <p className="text-base text-muted-foreground truncate mt-1">{user.email}</p>
              <div className="flex justify-center md:justify-start gap-4 mt-4 flex-wrap">
                <span className="inline-flex items-center gap-1 px-4 py-1.5 rounded-full bg-secondary/20 text-secondary text-sm font-semibold shadow-sm">
                  <Activity className="w-4 h-4" /> Analysen: <span className="font-bold">{user.analysisCount ?? 0}</span>
                </span>
                <span className="inline-flex items-center gap-1 px-4 py-1.5 rounded-full bg-accent/20 text-accent text-sm font-semibold shadow-sm">
                  <Settings className="w-4 h-4" /> Aktivitäten: <span className="font-bold">{user.activityCount ?? 0}</span>
                </span>
                <span className="inline-flex items-center gap-1 px-4 py-1.5 rounded-full bg-muted/30 text-muted-foreground text-sm font-semibold shadow-sm">
                  Mitglied seit: <span className="font-bold">{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '—'}</span>
                </span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6 pb-0 space-y-6">

          {/* Section: Unternehmensprofil */}
          <section>
            <div className="flex items-center gap-2 mb-4">
              <span className="w-2 h-6 rounded bg-primary" aria-hidden="true"></span>
              <h2 className="text-xl font-semibold text-foreground">Unternehmensprofil</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="companyName">Unternehmensname</Label>
                <Input id="companyName" value={companyName} onChange={(e) => setCompanyName(e.target.value)} className="mt-1" />
              </div>
              <div>
                <Label htmlFor="name">Ansprechpartner Name</Label>
                <Input id="name" value={name} onChange={(e) => setName(e.target.value)} className="mt-1" />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} className="mt-1" disabled readOnly />
              </div>
              <div>
                <Label htmlFor="address">Adresse</Label>
                <Input id="address" value={address} onChange={(e) => setAddress(e.target.value)} className="mt-1" />
              </div>
              <div>
                <Label htmlFor="phone">Telefonnummer</Label>
                <Input id="phone" type="tel" value={phone} onChange={(e) => setPhone(e.target.value)} className="mt-1" />
              </div>
              <div>
                <Label htmlFor="website">Webseite</Label>
                <Input id="website" type="url" placeholder="https://beispiel.de" value={website} onChange={(e) => setWebsite(e.target.value)} className="mt-1" />
              </div>
              <div>
                <Label htmlFor="employeeCount">Mitarbeiteranzahl</Label>
                <Input id="employeeCount" type="number" min="0" value={employeeCount} onChange={(e) => setEmployeeCount(e.target.value)} className="mt-1" />
              </div>
            </div>
          </section>
        </CardContent>
      </Card>
      {/* Analysis Section Card */}
      <Card className="shadow-xl rounded-2xl p-6">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-2 mb-4">
            <span className="w-2 h-6 rounded bg-primary" aria-hidden="true"></span>
            <h2 className="text-xl font-semibold text-foreground">Unternehmensinformation & Analyse</h2>
          </div>
        </CardHeader>
        <CardContent>
          <Label htmlFor="companyInfoPoints">Unternehmensinformation und Analyse</Label>
          <MarkdownEditor
            value={companyInfoPoints}
            onChange={(value?: string) => setCompanyInfoPoints(value || '')}
            height={400}
            preview='edit'
          />
        </CardContent>
        <CardFooter className="flex justify-end pt-4">
          <Button variant="default" onClick={handleAnalyse} disabled={isAnalyzing}>
            {isAnalyzing ? (
              <>
                <Loader className="mr-2 animate-spin" /> Analyse läuft...
              </>
            ) : (
              "KI-Analyse durchführen"
            )}
          </Button>
        </CardFooter>
      </Card>
      <div className="flex justify-end">
          <Button type="submit" variant="default" disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader className="mr-2 animate-spin" /> Speichern...
            </>
          ) : (
            "Profil Speichern"
          )}
        </Button>
      </div>
    </form>
  );
};

export default Profile;
