import { NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { verifySupabaseAuth, createAuthResponse } from '@/lib/supabaseAuth';
import { Database } from '@/types/supabase'; // Added import

// Type definitions
type SelectedCompanyRow = Database['public']['Tables']['selected_companies']['Row'];

interface SelectedCompanyWithPossibleNameAlias extends SelectedCompanyRow {
  name: string | null; // This will hold the aliased company_name or original name
}

interface EnrichedSelectedCompany extends SelectedCompanyWithPossibleNameAlias { // Extend the new interface
  companies?: {
    id: NonNullable<SelectedCompanyRow['company_id']>;
    name: string;
  };
}

export async function GET(_request: NextRequest) {
  try {
    const authResult = await verifySupabaseAuth();
    if (!authResult?.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const userId = authResult.user.id;
    const supabase = await createClient();

    // First, get the selected companies without the join to avoid potential issues
    const { data, error } = await supabase // Renamed companies to data for clarity before typing
      .from('selected_companies')
      .select(`
        id,
        place_id,
        company_name,
        address,
        is_deleted,
        deleted_at,
        created_at,
        user_id,
        name,
        updated_at,
        company_id
      `)
      .eq('user_id', userId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false });

    // Explicitly type the fetched data
    const companies: SelectedCompanyWithPossibleNameAlias[] = data as SelectedCompanyWithPossibleNameAlias[];

    // If we have companies with company_id, fetch the company names separately
    if (!error && companies && companies.length > 0) {
      const companyIds = companies
        .filter(c => c.company_id)
        .map(c => c.company_id);

      if (companyIds.length > 0) {
        const { data: companyData } = await supabase
          .from('companies')
          .select('id, name')
          .in('id', companyIds as string[]); // Assuming companyIds are strings, adjust if necessary

        // Create a lookup map for company names
        const companyMap = new Map<NonNullable<SelectedCompanyRow['company_id']>, string>();
        if (companyData) {
          companyData.forEach(c => companyMap.set(c.id, c.name));
        }

        // Add company names to the results
        companies.forEach(company => { // company is SelectedCompanyRow
          if (company.company_id && companyMap.has(company.company_id)) {
            // Cast to EnrichedSelectedCompany to inform TypeScript about the new property
            const companyWithDetails = company as EnrichedSelectedCompany;
            companyWithDetails.companies = {
              id: company.company_id, // company_id is non-null here due to the check
              name: companyMap.get(company.company_id) as string // Ensure name is string
            };
          }
        });
      }
    }

    if (error) {
      console.error('Fehler beim Abrufen der ausgewählten Unternehmen:', error);
      return createAuthResponse({ error: error.message }, 500);
    }

    // Transform the data to match frontend expected field names
    const transformedCompanies = companies.map(company => ({
      ...company,
      name: company.company_name, // Explicitly use company_name for the 'name' field
      // Ensure company_id is always available
      company_id: company.company_id
    }));

    return createAuthResponse({ companies: transformedCompanies });
  } catch (err) {
    console.error('Fehler in GET /api/selected-companies:', err);
    return createAuthResponse({ error: 'Unbekannter Fehler' }, 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await verifySupabaseAuth();
    if (!authResult?.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const userId = authResult.user.id;
    const { place_id, name, address } = await request.json();
    if (!place_id || !name) {
      return createAuthResponse({ error: 'Place ID und Unternehmensname sind erforderlich.' }, 400);
    }
    const supabase = await createClient();

    // First, check if this place_id exists in the companies table
    let company_id = null;
    const { data: existingCompany } = await supabase
      .from('companies')
      .select('id')
      .eq('place_id', place_id)
      .single();

    if (existingCompany) {
      company_id = existingCompany.id;
    } else {
      // If not, create a new company entry
      const { data: newCompany, error: companyError } = await supabase
        .from('companies')
        .insert({
          place_id,
          name,
          address: address || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();

      if (companyError) {
        console.error('Fehler beim Erstellen des Unternehmens:', companyError);
        return createAuthResponse({ error: companyError.message }, 500);
      }

      company_id = newCompany?.[0]?.id;
    }

    // Now upsert the selected_companies entry with the company_id
    const { data: companyArray, error } = await supabase
      .from('selected_companies')
      .upsert(
        {
          user_id: userId,
          place_id,
          company_name: name,
          address: address || null,
          is_deleted: false,
          company_id
        },
        { onConflict: 'user_id,place_id' }
      )
      .select();

    if (error) {
      console.error('Fehler beim Hinzufügen des Unternehmens:', error);
      return createAuthResponse({ error: error.message }, 500);
    }

    const company = Array.isArray(companyArray) ? companyArray[0] : companyArray;
    return createAuthResponse({ message: 'Unternehmen erfolgreich zur Auswahl hinzugefügt.', company });
  } catch (err) {
    console.error('Fehler in POST /api/selected-companies:', err);
    return createAuthResponse({ error: 'Unbekannter Fehler' }, 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const authResult = await verifySupabaseAuth();
    if (!authResult?.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const userId = authResult.user.id;
    const url = new URL(request.url);
    const place_id = url.searchParams.get('place_id');
    if (!place_id) {
      return createAuthResponse({ error: 'Place ID ist erforderlich.' }, 400);
    }
    const supabase = await createClient();

    const { data: company, error } = await supabase
      .from('selected_companies')
      .update({ is_deleted: true, deleted_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('place_id', place_id)
      .select()
      .single();

    if (error) {
      console.error('Fehler beim Entfernen des Unternehmens:', error);
      return createAuthResponse({ error: error.message }, 500);
    }
    if (!company) {
      return createAuthResponse({ message: 'Unternehmen nicht gefunden.' }, 404);
    }
    return createAuthResponse({ message: 'Unternehmen erfolgreich entfernt.', company });
  } catch (err) {
    console.error('Fehler in DELETE /api/selected-companies:', err);
    return createAuthResponse({ error: 'Unbekannter Fehler' }, 500);
  }
}
