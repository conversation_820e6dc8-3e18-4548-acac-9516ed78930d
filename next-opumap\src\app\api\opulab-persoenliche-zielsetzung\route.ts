import { NextRequest, NextResponse } from 'next/server';
import { createPgClient } from '@/lib/db-utils';
import { verifySupabaseAuth, createAuthResponse } from '@/lib/supabaseAuth';
import { runOpulabPersoenlicheZielsetzung } from '@/api/opulab-persoenliche-zielsetzung';

/**
 * POST /api/opulab-persoenliche-zielsetzung
 * Body: { strategyId: number; personalGoal: string }
 * Runs AI analysis for personal goal and stores prompt+result.
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();
    if (!authResult || !authResult.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const { user } = authResult;
    const userId = user.id;

    const { strategyId, personalGoal } = await request.json();
    if (!strategyId || typeof personalGoal !== 'string') {
      return createAuthResponse(
        { error: 'strategyId und personalGoal sind erforderlich.' },
        400
      );
    }

    const companyInfo = user.company_info_points || '';
    const { content } = await runOpulabPersoenlicheZielsetzung(
      companyInfo,
      personalGoal
    );

    const client = createPgClient();
    await client.connect();

    // Start a transaction
    await client.query('BEGIN');

    try {
      // 1. Update personal_goal in strategies table
      const updateStrategyQuery = `
        UPDATE public.strategies
        SET personal_goal = $3,
            updated_at = now()
        WHERE user_id = $1
          AND id = $2
        RETURNING id;
      `;
      const strategyUpdateResult = await client.query(updateStrategyQuery, [
        userId,
        strategyId,
        personalGoal
      ]);

      if (strategyUpdateResult.rows.length === 0) {
        throw new Error('Strategy not found or user does not have permission.');
      }

      // 2. Insert the analysis result into analysis_results table
      const insertAnalysisQuery = `
        INSERT INTO public.analysis_results (strategy_id, analysis_type, content, updated_at)
        VALUES ($1, $2, $3, now())
        ON CONFLICT (strategy_id, analysis_type) DO UPDATE
        SET content = EXCLUDED.content, updated_at = now()
        RETURNING id, strategy_id, analysis_type, content;
      `;
      const analysisResult = await client.query(insertAnalysisQuery, [
        strategyId,
        'personal_goal', // analysis_type
        content          // AI generated content
      ]);

      await client.query('COMMIT'); // Commit transaction

      // Return the result of the analysis insertion
      const file = analysisResult.rows[0] || null;
      return createAuthResponse({ file });

    } catch (transactionError) {
      await client.query('ROLLBACK'); // Rollback transaction on error
      throw transactionError; // Re-throw the error to be caught by the outer catch block
    } finally {
      await client.end();
    }

  } catch (err) {
    console.error('Error in POST /api/opulab-persoenliche-zielsetzung:', err);
    const message = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
