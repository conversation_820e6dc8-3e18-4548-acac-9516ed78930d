# Vollständiges ER-Diagramm für Supabase Projekt "opumap1"

```mermaid
erDiagram
    SCHNELLE_ANALYSE {
        int id PK
        varchar place_id
        varchar name
        text address
        varchar phone
        varchar website
        numeric lat
        numeric lng
        text analysis_content
        timestamp analysis_date
        timestamp created_at
        timestamp updated_at
    }
    TIEFE_ANALYSE {
        int id PK
        varchar place_id
        varchar name
        text address
        varchar phone
        varchar website
        numeric lat
        numeric lng
        text analysis_content
        timestamp analysis_date
        timestamp created_at
        timestamp updated_at
    }
    USERS {
        int id PK
        varchar name
        varchar email
        varchar password_hash
        timestamp created_at
        varchar company_name
        text address
        varchar phone
        varchar website
        text description
        varchar industry
        text company_info_points
        int employee_count
        int analysis_count
        int activity_count
    }
    SELECTED_COMPANIES_OLD {
        int id PK
        varchar place_id
        varchar company_name
        text address
        timestamp created_at
        uuid user_id
    }
    USER_STRATEGIES_OLD {
        int id PK
        varchar strategy_name
        text company_info
        text personal_goal
        text short_term_tasks
        text long_term_goals
        jsonb relevant_aspects
        int short_term_value
        int long_term_value
        bool analysis_started
        text analysis_result
        timestamp created_at
        timestamp updated_at
        uuid user_id
    }
    RELEVANTE_ASPEKTE_ANALYSE {
        int id PK
        int user_id FK
        int strategy_id FK
        text content
        timestamptz created_at
        timestamptz updated_at
        jsonb aspects
        text prompt
    }
    PRIORISIERUNGEN {
        bigint id PK
        int user_id
        bigint strategy_id
        int short_term_value
        int long_term_value
        text prompt
        text content
        timestamptz created_at
        timestamptz updated_at
    }
    OPULAB_PERSOENLICHE_ZIELSETZUNG {
        bigint id PK
        int user_id
        bigint strategy_id
        text prompt
        text content
        timestamptz created_at
        timestamptz updated_at
    }
    OPULAB_STRATEGIE_BRIEFING {
        int id PK
        int user_id FK
        int strategy_id FK
        text short_term_tasks
        text long_term_goals
        text prompt
        text content
        timestamptz created_at
        timestamptz updated_at
    }
    SCAN_RESULTS_OLD {
        bigint id PK
        int user_id FK
        int strategy_id FK
        int selected_company_id FK
        text result_text
        timestamptz created_at
    }
    PROFILES {
        uuid id PK
        text name
        text company_name
        text address
        text phone
        text website
        text employee_count
        text company_info_points
        timestamptz created_at
        timestamptz updated_at
    }
    USER_ID_MAPPINGS {
        int numeric_id PK
        uuid uuid
        text user_email
        timestamptz created_at
    }
    USER_STRATEGIES {
        int id PK
        int user_id FK
        varchar strategy_name
        text company_info
        text personal_goal
        text short_term_tasks
        text long_term_goals
        jsonb relevant_aspects
        int short_term_value
        int long_term_value
        bool analysis_started
        text analysis_result
        timestamp created_at
        timestamp updated_at
    }
    SELECTED_COMPANIES {
        int id PK
        int user_id FK
        varchar place_id
        varchar company_name
        text address
        timestamp created_at
    }
    SCAN_RESULTS {
        int id PK
        int user_id FK
        int strategy_id FK
        int selected_company_id FK
        text result_text
        timestamptz created_at
    }
    DELETED_COMPANIES_MAPPING {
        int id PK
        int user_id FK
        varchar place_id
        int old_company_id
        timestamptz deleted_at
    }

    USERS ||--o{ RELEVANTE_ASPEKTE_ANALYSE : "1:n"
    USER_STRATEGIES_OLD ||--o{ RELEVANTE_ASPEKTE_ANALYSE : "1:n"
    OPULAB_STRATEGIE_BRIEFING }o--|| USERS : "n:1"
    OPULAB_STRATEGIE_BRIEFING }o--|| USER_STRATEGIES_OLD : "n:1"
    SCAN_RESULTS_OLD }o--|| USERS : "n:1"
    SCAN_RESULTS_OLD }o--|| USER_STRATEGIES_OLD : "n:1"
    SCAN_RESULTS_OLD }o--|| SELECTED_COMPANIES_OLD : "n:1"
    PROFILES ||--|| USERS : "1:1"
    USER_ID_MAPPINGS ||--o{ SCAN_RESULTS : "1:n"
    USER_STRATEGIES ||--o{ SCAN_RESULTS : "1:n"
    SELECTED_COMPANIES ||--o{ SCAN_RESULTS : "1:n"
