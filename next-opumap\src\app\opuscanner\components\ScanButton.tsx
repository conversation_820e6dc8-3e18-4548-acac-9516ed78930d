'use client';
import React from 'react';
import styled from 'styled-components';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
}

const ScanButton: React.FC<ButtonProps> = ({ children, onClick, ...props }) => {
  return (
    <StyledWrapper>
      <button type="button" className="button" onClick={onClick} {...props}>
        <span className="fold" />
        <div className="points_wrapper">
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
          <i className="point" />
        </div>
        <span className="inner">
          {/* Removed SVG Icon */}
          {children} {/* Use children for button text */}
        </span>
      </button>
    </StyledWrapper>
  );
}

const StyledWrapper = styled.div`
  .button {
    /* Using variables from globals.css */
    --h-button: 56px;
    --w-button: auto;
    /* Use the global radius variable */
    --round: var(--radius);
    cursor: pointer;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    /* Transition only background property */
    transition: background 0.25s ease;
    background: radial-gradient(
        65.28% 65.28% at 50% 100%,
        hsl(var(--primary) / 0.8) 0%,
        hsl(var(--primary) / 0) 100%
      ),
      linear-gradient(0deg, hsl(var(--primary)), hsl(var(--primary)));
    border-radius: var(--round);
    border: none;
    /* Remove default outline, focus style handles it */
    outline: none;
    padding: 20px 36px;
    min-height: var(--h-button);
  }
  .button::before,
  .button::after {
    content: "";
    position: absolute;
    inset: var(--space);
    transition: all 0.5s ease-in-out;
    border-radius: calc(var(--round) - var(--space));
    z-index: 0;
  }
  .button::before {
    --space: 1px;
    /* Use card color with low opacity for subtle inner border/highlight */
    background: linear-gradient(
      177.95deg,
      hsl(var(--card) / 0.1) 0%,
      hsl(var(--card) / 0) 100%
    );
  }
  .button::after {
    --space: 2px;
     /* Replicate background using CSS variables */
    background: radial-gradient(
        65.28% 65.28% at 50% 100%,
        hsl(var(--primary) / 0.8) 0%,
        hsl(var(--primary) / 0) 100%
      ),
      linear-gradient(0deg, hsl(var(--primary)), hsl(var(--primary)));
    /* Apply specific transition to background */
    transition: background 0.25s ease-in-out;
  }
  .button:active {
    transform: scale(0.95);
  }

  /* Remove the direct button hover style */

  .button:hover::after {
     /* Only change ::after background on hover */
     background: #1D4ED8; /* Test with blue-700 */
  }

  .button:disabled {
      cursor: not-allowed;
      opacity: 0.6;
      /* Use muted variables for disabled state */
      background: hsl(var(--muted));
  }
  .button:disabled::before,
  .button:disabled::after {
      display: none;
  }
  .button:disabled .fold,
  .button:disabled .points_wrapper {
    display: none;
  }
  .button:disabled .inner {
    /* Use muted foreground for disabled text */
    color: hsl(var(--muted-foreground));
  }

  .fold {
    z-index: 1;
    position: absolute;
    top: 0;
    right: 0;
    height: 1rem;
    width: 1rem;
    display: inline-block;
    transition: all 0.5s ease-in-out;
    /* Slightly adjusted primary for fold background */
    background: radial-gradient(
      100% 75% at 55%,
      hsl(var(--primary) / 0.9) 0%,
      hsl(var(--primary) / 0) 100%
    );
    /* Softer shadow using foreground color */
    box-shadow: -1px 1px 3px hsl(var(--foreground) / 0.15);
    border-bottom-left-radius: calc(var(--radius) * 0.6); /* Slightly smaller radius */
    /* Use global radius for top-right */
    border-top-right-radius: var(--round);
  }
  .fold::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 150%;
    height: 150%;
    transform: rotate(45deg) translateX(0%) translateY(-18px);
    /* Use foreground color for contrast - dark in light mode, light in dark mode */
    background-color: hsl(var(--foreground));
    pointer-events: none;
  }
  .button:hover .fold {
    /* Keep hover effect */
    margin-top: -1rem;
    margin-right: -1rem;
  }

  .points_wrapper {
    overflow: hidden;
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: absolute;
    z-index: 1;
  }

  .points_wrapper .point {
    bottom: -10px;
    position: absolute;
    animation: floating-points infinite ease-in-out;
    pointer-events: none;
    width: 2px;
    height: 2px;
    background-color: hsl(var(--primary-foreground)); /* Use primary foreground for points */
    border-radius: 9999px;
    opacity: 0;
  }
  @keyframes floating-points {
    0% {
      transform: translateY(0);
      opacity: 0.7; /* Start visible */
    }
    85% {
      opacity: 0.1; /* Fade out */
    }
    100% {
      transform: translateY(calc(-1 * var(--h-button))); /* Float up button height */
      opacity: 0;
    }
  }
  .points_wrapper .point:nth-child(1) { left: 10%; animation-duration: 2.35s; animation-delay: 0.2s; }
  .points_wrapper .point:nth-child(2) { left: 30%; animation-duration: 2.5s; animation-delay: 0.5s; opacity: 0.7; }
  .points_wrapper .point:nth-child(3) { left: 25%; animation-duration: 2.2s; animation-delay: 0.1s; opacity: 0.8; }
  .points_wrapper .point:nth-child(4) { left: 44%; animation-duration: 2.05s; animation-delay: 0.3s; opacity: 0.6; }
  .points_wrapper .point:nth-child(5) { left: 50%; animation-duration: 1.9s; animation-delay: 0.0s; opacity: 1.0; }
  .points_wrapper .point:nth-child(6) { left: 75%; animation-duration: 1.5s; animation-delay: 1.5s; opacity: 0.5; }
  .points_wrapper .point:nth-child(7) { left: 88%; animation-duration: 2.2s; animation-delay: 0.8s; opacity: 0.9; }
  .points_wrapper .point:nth-child(8) { left: 58%; animation-duration: 2.25s; animation-delay: 1.2s; opacity: 0.8; }
  .points_wrapper .point:nth-child(9) { left: 98%; animation-duration: 2.6s; animation-delay: 0.6s; opacity: 0.6; }
  .points_wrapper .point:nth-child(10){ left: 65%; animation-duration: 2.5s; animation-delay: 1.0s; opacity: 1.0; }

  .inner {
    z-index: 2;
    gap: 8px;
    position: relative;
    width: 100%;
    /* Use primary-foreground for text */
    color: hsl(var(--primary-foreground));
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.5;
    transition: color 0.2s ease-in-out;
  }

  .button:focus-visible {
    /* Use ring variable for focus outline */
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
  .button:hover .inner {
     /* Optional hover effect for text */
     filter: brightness(1.1);
  }

`;

export default ScanButton; 