import { BusinessData, AnalysisResult as SharedAnalysisResult } from '@/types'; // Import shared types
import { fetchWithAuth } from '@/lib/supabaseClient';

// Define structure for saving data to our backend (can reuse if identical to schnelle-analyse)
interface SaveAnalysisPayload {
    businessData: {
        name: string;
        address: string;
        phone: string;
        website: string;
        placeId: string;
        lat: number;
        lng: number;
    };
    analysisContent: string;
    analysisDate: string; // ISO string date
}

/**
 * Runs a deep analysis using the Perplexity API and saves the result.
 * IMPORTANT: Calling Perplexity API directly from the client exposes the API key.
 * Consider moving this logic to a Next.js API route for better security.
 * @param businessData - Data of the business to analyze.
 * @returns The analysis content and the result from saving to the database, or null on failure.
 */
// Define a more specific return type if the structure of saveResult is known
interface RunDeepAnalysisResult {
    analysisContent: string;
    analysisDate: string;
    saveResult: unknown; // Use 'unknown' instead of 'any'
}
export async function runDeepAnalysis(businessData: BusinessData): Promise<RunDeepAnalysisResult | null> {
    // Construct prompt safely
    let prompt = "Gebe mir eine detaillierte Analyse über das folgende Unternehmen mit Fokus auf Key-Kennzahlen, Wettbewerbsposition, Markttrends und Zukunftsperspektiven:\n\n";
    if (businessData.name) prompt += `- Unternehmensname: ${businessData.name}\n`;
    if (businessData.formatted_address) prompt += `- Adresse: ${businessData.formatted_address}\n`;
    if (businessData.formatted_phone_number) prompt += `- Telefonnummer: ${businessData.formatted_phone_number}\n`;
    if (businessData.website) prompt += `- Webseite: ${businessData.website}\n`;

    // Get API key
    const token = process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;
    if (!token) {
        console.error("Perplexity API Token (NEXT_PUBLIC_PERPLEXITY_API_KEY) is not defined.");
        throw new Error("Perplexity API Token ist nicht konfiguriert.");
    }

    const options = {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            accept: 'application/json',
        },
        body: JSON.stringify({
            model: "sonar-pro", // Use the large online sonar model as requested
            messages: [
                { role: "system", content: "Provide a comprehensive analysis with key metrics, competitive positioning, market trends, and future outlook. Be precise and well-structured." },
                { role: "user", content: prompt }
            ],
             // max_tokens: 5000, // Consider adjusting based on model and needs
             // temperature: 0.2,
        })
    };

    try {
        const response = await fetch('https://api.perplexity.ai/chat/completions', options);
        if (!response.ok) {
            const errorText = await response.text();
            console.error("Perplexity API Error Response:", errorText);
            throw new Error(`Perplexity API Fehler (${response.status}): ${errorText.substring(0, 100)}...`);
        }

        const data = await response.json();
        let analysisContent = "";

        if (data?.choices?.[0]?.message?.content) {
            analysisContent = data.choices[0].message.content;
        } else {
            console.warn("Could not extract analysis content from Perplexity response:", data);
            analysisContent = "Detaillierte Analyse konnte nicht extrahiert werden.";
        }

        const analysisDate = new Date().toISOString();
        const saveResult = await saveAnalysisToDatabase(businessData, analysisContent, analysisDate);

        return {
            analysisContent,
            analysisDate,
            saveResult // Assuming saveResult is the direct JSON response
        };
    } catch (error: unknown) {
        console.error("Fehler in runDeepAnalysis:", error);
        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
        throw new Error(`Fehler bei der Tiefenanalyse: ${message}`);
    }
}

/**
 * Saves the deep analysis result to the application's backend database.
 * @param businessData - Data of the analyzed business.
 * @param analysisContent - The content generated by the analysis API.
 * @param analysisDate - The ISO string date of the analysis.
 * @returns The result from the backend save operation (use unknown or a specific type).
 */
async function saveAnalysisToDatabase(businessData: BusinessData, analysisContent: string, analysisDate: string): Promise<unknown> {
    // Extract lat/lng carefully
    let lat: number = 0;
    let lng: number = 0;
    if ('geometry' in businessData && businessData.geometry?.location) {
        const location = businessData.geometry.location;
        if (typeof location.lat === 'function' && typeof location.lng === 'function') {
            lat = location.lat();
            lng = location.lng();
        } else {
            // Fallback: Explicitly cast to unknown then LatLngLiteral
            const literalLocation = location as unknown as google.maps.LatLngLiteral;
            lat = literalLocation.lat ?? 0;
            lng = literalLocation.lng ?? 0;
        }
    } else if ('position' in businessData && businessData.position) {
        lat = businessData.position.lat;
        lng = businessData.position.lng;
    }

    // Determine placeId safely
    let placeId = businessData.place_id ?? "";
    if (!placeId && 'id' in businessData) {
        placeId = `demo_${businessData.id}`;
    }

    const payload: SaveAnalysisPayload = {
        businessData: {
            name: businessData.name ?? "Unbekannt",
            address: businessData.formatted_address ?? "",
            phone: businessData.formatted_phone_number ?? "",
            website: businessData.website ?? "",
            placeId: placeId,
            lat,
            lng
        },
        analysisContent,
        analysisDate
    };

    try {
        // Use the new analyses endpoint with type=deep
        const response = await fetchWithAuth('/api/analyses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...payload,
                type: 'deep' // Add the type parameter
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Server Error Saving Deep Analysis:", errorText);
            throw new Error(`Server-Fehler (${response.status}) beim Speichern der Tiefenanalyse.`);
        }

        return await response.json(); // Return parsed JSON response
    } catch (error: unknown) {
        console.error("Fehler beim Speichern der Tiefenanalyse:", error);
        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
        throw new Error(`Fehler beim Speichern der Tiefenanalyse: ${message}`);
    }
}

/**
 * Fetches existing deep analysis data for a given place ID from the backend.
 * @param placeId - The Google Place ID of the business.
 * @returns The analysis data or null if not found.
 */
export async function getDeepAnalysis(placeId: string): Promise<SharedAnalysisResult | null> { // Use shared type
    if (!placeId) {
        console.warn("getDeepAnalysis called without placeId");
        return null;
    }
    try {
        // Use the new analyses/deep endpoint
        const response = await fetchWithAuth(`/api/analyses/deep/${placeId}`);

        // Handle authentication errors gracefully
        if (response.status === 401) {
            console.warn("Authentication required for fetching deep analysis data");
            return null; // Return null instead of throwing an error
        }

        if (!response.ok) {
            if (response.status === 404) {
                console.log(`Keine tiefe Analyse für placeId ${placeId} gefunden.`);
                return null;
            }
            const errorText = await response.text();
            console.error("Server Error Fetching Deep Analysis:", errorText);
            // Don't throw, just return null to prevent UI crashes
            return null;
        }

        const data: SharedAnalysisResult = await response.json(); // Use shared type
        return data;
    } catch (error: unknown) {
        console.error("Fehler beim Abrufen der Tiefenanalyse:", error);
        return null;
    }
}
