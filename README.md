# OpuMap-nextJS

A cooperative strategy platform that connects companies via innovative solutions. This repository hosts both the front-end and back-end applications for OpuMap-nextJS.
- **Frontend:** Built with React.js and styled using Tailwind CSS. Integrates Google Maps via the @react-google-maps/api package.
- **Backend:** Built with Express.js. It includes user authentication with JWT, integration with Supabase for database operations, and a KI (strategy) endpoint that uses the OpenAI API to generate company strategy recommendations.

## Frontend Setup (React & Tailwind)
1. Navigate to the `/client` directory:
   ```
   cd client
   ```
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables:
   - Create a `.env` file in the `/client` directory.
   - Add your Google Maps API key:
     ```
     REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
     ```
4. Start the React application:
   ```
   npm start
   ```
   The app will run at [http://localhost:3000](http://localhost:3000).

## Backend Setup (Express, Supabase, OpenAI)
1. Navigate to the `/server` directory:
   ```
   cd server
   ```
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables:
   - Create a `.env` file in the `/server` directory.
   - Add the following environment variables:
     ```
     PORT=5000
     JWT_SECRET=your_jwt_secret_here
     SUPABASE_URL=your_supabase_url_here
     SUPABASE_KEY=your_supabase_key_here
     OPENAI_API_KEY=your_openai_api_key_here
     ```
4. Start the Express server:
   ```
   npm start
   ```
   The server will listen on port 5000.

## Deployment Guidelines
- **Heroku Deployment:**
  1. Ensure your project repository is pushed to Git.
  2. Create a Heroku app and set the buildpacks (Node.js).
  3. Set the required environment variables on Heroku:
     - `PORT`
     - `JWT_SECRET`
     - `SUPABASE_URL`
     - `SUPABASE_KEY`
     - `OPENAI_API_KEY`
  4. Deploy via Git by pushing your repository to Heroku.
  5. The Procfile in the `/server` directory configures Heroku to run `node index.js`.

## Manual Testing Instructions
- **Registration & Login:**
  1. Use a tool like Postman to test the registration endpoint:
     - POST `http://localhost:5000/api/auth/register` with JSON body containing `email` and `password`.
  2. Test login by POSTing to `http://localhost:5000/api/auth/login` with the registered credentials and retrieve a JWT.
- **Companies Endpoint:**
  1. After authentication, use the GET endpoint at `http://localhost:5000/api/companies` to view companies.
  2. Test adding a company using the POST endpoint.
- **KI Strategy Endpoint:**
  1. Authenticate and POST to `http://localhost:5000/api/strategy` with a JSON body containing `companyDetails`.
  2. Verify the recommendation response.

## Additional Notes
- Ensure you follow the environment configuration guidelines and do not hardcode sensitive data.
- For any issues, review the console logs in both the client and server consoles.

Happy Coding!

## Setup
1. Run .\setup.ps1 to initialize local environment files. This will copy client\.env.example to client\.env and server\.env.example to server\.env if they do not exist.
2. Update the generated .env files with your local configuration (e.g., database credentials).
3. Run any necessary database migration scripts.
