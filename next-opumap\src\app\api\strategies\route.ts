import { NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { verifySupabaseAuth, createAuthResponse } from '@/lib/supabaseAuth';

// GET /api/strategies
export async function GET(_request: NextRequest) {
  try {
    // Verify authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();
    if (!authResult || !authResult.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const { user } = authResult;

    // Create a Supabase client
    const supabase = await createClient();

    // Use the UUID directly
    const userId = user.id;

    // Get all strategies for this user
    const { data: strategies, error } = await supabase
      .from('strategies')
      .select(`
        id,
        name,
        company_info,
        personal_goal,
        short_term_tasks,
        long_term_goals,
        relevant_aspects,
        short_term_value,
        long_term_value,
        analysis_started,
        analysis_result,
        created_at,
        updated_at
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    // Map the name field to strategy_name for backward compatibility
    const mappedStrategies = strategies?.map(strategy => ({
      ...strategy,
      strategy_name: strategy.name
    }));

    if (error) {
      console.error('Error fetching strategies:', error);
      return createAuthResponse({ error: 'Fehler beim Abrufen der Strategien.', details: error.message }, 500);
    }

    return createAuthResponse({ strategies: mappedStrategies });
  } catch (err) {
    console.error(err);
    const msg = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return createAuthResponse({ error: 'Fehler beim Abrufen der Strategien.', details: msg }, 500);
  }
}

// POST /api/strategies
export async function POST(request: NextRequest) {
  try {
    // Verify authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();
    if (!authResult || !authResult.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const { user } = authResult;

    const body = await request.json();
    const {
      strategy_name,
      company_info,
      personal_goal,
      short_term_tasks,
      long_term_goals,
      relevant_aspects,
      short_term_value,
      long_term_value
    } = body;

    if (!strategy_name) {
      return createAuthResponse({ error: 'Strategiename ist erforderlich.' }, 400);
    }

    const relevantAspectsJson = typeof relevant_aspects === 'string'
      ? relevant_aspects
      : JSON.stringify(relevant_aspects || []);

    // Create a Supabase client
    const supabase = await createClient();

    // Use the UUID directly
    const userId = user.id;

    // Insert the strategy with explicit created_at timestamp
    const now = new Date().toISOString();
    const { data: strategy, error } = await supabase
      .from('strategies')
      .insert({
        user_id: userId,
        name: strategy_name,
        company_info: company_info || '',
        personal_goal: personal_goal || '',
        short_term_tasks: short_term_tasks || '',
        long_term_goals: long_term_goals || '',
        relevant_aspects: relevantAspectsJson,
        short_term_value: short_term_value ?? 50,
        long_term_value: long_term_value ?? 50,
        analysis_started: false,
        analysis_result: null,
        created_at: now,
        updated_at: now
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating strategy:', error);
      return createAuthResponse({ error: 'Fehler beim Erstellen der Strategie.', details: error.message }, 500);
    }

    // Map the name field to strategy_name for backward compatibility
    const mappedStrategy = {
      ...strategy,
      strategy_name: strategy.name
    };

    return createAuthResponse({ message: 'Strategie erstellt.', strategy: mappedStrategy });
  } catch (err) {
    console.error(err);
    const msg = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return createAuthResponse({ error: 'Fehler beim Erstellen der Strategie.', details: msg }, 500);
  }
}

// PUT /api/strategies?id=…
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();
    if (!authResult || !authResult.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const { user } = authResult;

    const url = new URL(request.url);
    const strategyId = url.searchParams.get('id');
    if (!strategyId) {
      return createAuthResponse({ error: 'Strategie-ID ist erforderlich.' }, 400);
    }

    const body = await request.json();
    const {
      strategy_name,
      company_info,
      personal_goal,
      short_term_tasks,
      long_term_goals,
      relevant_aspects,
      short_term_value,
      long_term_value,
      analysis_started,
      analysis_result
    } = body;

    if (!strategy_name) {
      return createAuthResponse({ error: 'Strategiename ist erforderlich.' }, 400);
    }

    const relevantAspectsJson = typeof relevant_aspects === 'string'
      ? relevant_aspects
      : JSON.stringify(relevant_aspects || []);

    // Create a Supabase client
    const supabase = await createClient();

    // Use the UUID directly
    const userId = user.id;

    // Verify ownership
    const { data: checkData, error: checkError } = await supabase
      .from('strategies')
      .select('id')
      .eq('id', strategyId)
      .eq('user_id', userId)
      .single();

    if (checkError || !checkData) {
      return createAuthResponse({ error: 'Nicht gefunden oder keine Berechtigung.' }, 404);
    }

    // Update the strategy
    const { data: strategy, error } = await supabase
      .from('strategies')
      .update({
        name: strategy_name,
        company_info: company_info || '',
        personal_goal: personal_goal || '',
        short_term_tasks: short_term_tasks || '',
        long_term_goals: long_term_goals || '',
        relevant_aspects: relevantAspectsJson,
        short_term_value: short_term_value ?? 50,
        long_term_value: long_term_value ?? 50,
        analysis_started: analysis_started ?? false,
        analysis_result: analysis_result || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', strategyId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating strategy:', error);
      return createAuthResponse({ error: 'Fehler beim Aktualisieren der Strategie.', details: error.message }, 500);
    }

    // Map the name field to strategy_name for backward compatibility
    const mappedStrategy = {
      ...strategy,
      strategy_name: strategy.name
    };

    return createAuthResponse({ message: 'Strategie aktualisiert.', strategy: mappedStrategy });
  } catch (err) {
    console.error(err);
    const msg = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return createAuthResponse({ error: 'Fehler beim Aktualisieren der Strategie.', details: msg }, 500);
  }
}

// DELETE /api/strategies?id=…
export async function DELETE(request: NextRequest) {
  try {
    // Verify authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();
    if (!authResult || !authResult.user) {
      return createAuthResponse({ error: 'Nicht authentifiziert.' }, 401);
    }
    const { user } = authResult;

    const url = new URL(request.url);
    const strategyId = url.searchParams.get('id');
    if (!strategyId) {
      return createAuthResponse({ error: 'Strategie-ID ist erforderlich.' }, 400);
    }

    // Create a Supabase client
    const supabase = await createClient();

    // Use the UUID directly
    const userId = user.id;

    // Verify ownership
    const { data: checkData, error: checkError } = await supabase
      .from('strategies')
      .select('id')
      .eq('id', strategyId)
      .eq('user_id', userId)
      .single();

    if (checkError || !checkData) {
      return createAuthResponse({ error: 'Nicht gefunden oder keine Berechtigung.' }, 404);
    }

    // Delete the strategy
    const { error } = await supabase
      .from('strategies')
      .delete()
      .eq('id', strategyId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error deleting strategy:', error);
      return createAuthResponse({ error: 'Fehler beim Löschen der Strategie.', details: error.message }, 500);
    }

    return createAuthResponse({ message: 'Strategie gelöscht.' });
  } catch (err) {
    console.error(err);
    const msg = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return createAuthResponse({ error: 'Fehler beim Löschen der Strategie.', details: msg }, 500);
  }
}
