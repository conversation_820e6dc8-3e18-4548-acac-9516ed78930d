'use client';
import React from 'react';
import ScanButton from './ScanButton';
import { ScanProgressStatus } from './ui/ScannerStatusIndicator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FooterScanProps {
  isScanning: boolean; // Zeigt an, ob der Prozess *aktiv* läuft (loading)
  onStartScan: () => void;
  hasSelectedStrategy: boolean;
  hasSelectedCompanies: boolean;
  scanProgressStatus: ScanProgressStatus; // Neuer Name für den detaillierten Status
}

export const FooterScan: React.FC<FooterScanProps> = ({
  isScanning, // isScanning ist true, wenn scanProgressStatus 'loading' ist
  onStartScan,
  hasSelectedStrategy,
  hasSelectedCompanies,
  scanProgressStatus = 'idle', // Standardwert
}) => {
  // Button ist deaktiviert, wenn gescannt wird ODER wenn Voraussetzungen nicht erfüllt sind
  const isButtonDisabled = isScanning || !hasSelectedStrategy || !hasSelectedCompanies;

  let tooltipMessage = "Starten Sie den Scan, um Chancen zu identifizieren.";
  let buttonText = 'Scan starten';

  // Logik basierend auf dem neuen Status
  switch (scanProgressStatus) {
    case 'loading':
      tooltipMessage = "Scan läuft, Ergebnisse werden analysiert...";
      buttonText = 'Scanne...';
      break;
    case 'completed':
      tooltipMessage = "Scan erfolgreich abgeschlossen.";
      buttonText = 'Neuen Scan starten'; // Text für nach Abschluss
      break;
    case 'error':
      tooltipMessage = "Scan fehlgeschlagen. Bitte überprüfen Sie die Details und versuchen Sie es erneut.";
      buttonText = 'Scan erneut starten'; // Text nach Fehler
      break;
    // case 'timeout': // Wird jetzt als 'error' behandelt
    //   tooltipMessage = "Zeitüberschreitung beim Scan. Bitte versuchen Sie es erneut.";
    //   buttonText = 'Scan erneut starten';
    //   break;
    case 'idle':
    default:
      if (!hasSelectedStrategy) {
        tooltipMessage = "Bitte wählen Sie zuerst eine Opulab-Strategie aus.";
      } else if (!hasSelectedCompanies) {
        tooltipMessage = "Bitte wählen Sie mindestens ein Unternehmen aus.";
      }
      // Sonst bleibt der Standardtext "Scan starten"
      break;
  }

  return (
    <footer className="mt-8 flex justify-center items-center flex-shrink-0 py-4">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {/* Deaktivierungslogik vereinfacht */}
            <span tabIndex={isButtonDisabled ? 0 : -1}>
              <ScanButton
                onClick={onStartScan}
                disabled={isButtonDisabled}
              >
                {buttonText}
              </ScanButton>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipMessage}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </footer>
  );
};
