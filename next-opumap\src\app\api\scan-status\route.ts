import { NextRequest, NextResponse } from 'next/server';
import pkg from 'pg';
const { Client } = pkg;
import { verifyAuth } from '@/lib/auth';

// Helper: PG client configuration (copied from scan-results/route.ts)
function getPgClientConfig() {
  const connectionString = process.env.POSTGRES_URL;
  if (!connectionString) {
    console.error('POSTGRES_URL environment variable not found.');
    throw new Error('POSTGRES_URL environment variable not found.');
  }
  return {
    connectionString,
    ssl: { rejectUnauthorized: false } // Adjust SSL based on your DB requirements
  };
}

export async function POST(request: NextRequest) {
  let dbClient: pkg.Client | null = null;
  try {
    // 1. Verify Authentication
    const auth = await verifyAuth(request);
    if (!auth?.user?.id) {
      console.error('[scan-status] Authentication failed via verifyAuth');
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'Authentication failed'
      }, { status: 401 });
    }
    const userId = auth.user.id;
    console.log('[scan-status] User authenticated:', userId);

    // 2. Parse Request Body
    const { strategyId, companyIds, scanStartTime } = await request.json();
    console.log('[scan-status] Received request:', { userId, strategyId, companyIds, scanStartTime });

    // 3. Validate Input
    if (!strategyId || typeof strategyId !== 'number') {
        console.error('[scan-status] Invalid or missing strategyId:', strategyId);
        return NextResponse.json({ error: 'Bad Request', details: 'Invalid or missing strategyId' }, { status: 400 });
    }
    if (!companyIds || !Array.isArray(companyIds) || companyIds.length === 0 || companyIds.some(id => typeof id !== 'number')) {
         console.error('[scan-status] Invalid or missing selectedCompanyIds:', companyIds);
         return NextResponse.json({ error: 'Bad Request', details: 'Invalid or missing selectedCompanyIds array' }, { status: 400 });
     }
    // Validate scanStartTime (should be a positive number, epoch milliseconds)
    if (!scanStartTime || typeof scanStartTime !== 'number' || scanStartTime <= 0) {
         console.error('[scan-status] Invalid or missing scanStartTime:', scanStartTime);
         return NextResponse.json({ error: 'Bad Request', details: 'Invalid or missing scanStartTime' }, { status: 400 });
     }

    // 4. Connect to Database
    dbClient = new Client(getPgClientConfig());
    await dbClient.connect();
    console.log('[scan-status] DB connected.');

    // 5. Query Scan Results Count
    // Check how many results exist for this specific user, strategy, and set of companies.
    // We could also check the `created_at` timestamp if we only want results generated *after* the scan started,
    // but counting existing results is simpler for now to determine completion.
    const query = `
      SELECT COUNT(*)
      FROM scan_results
      WHERE user_id = $1
        AND strategy_id = $2
        AND company_id = ANY($3::int[])
        AND created_at >= to_timestamp($4 / 1000.0) -- Check timestamp against scanStartTime
    `;
    // Pass scanStartTime as the 4th parameter (converted from ms to seconds for to_timestamp)
    const result = await dbClient.query(query, [userId, strategyId, companyIds, scanStartTime]);
    const count = parseInt(result.rows[0].count, 10);
    const expectedCount = companyIds.length;

    console.log(`[scan-status] Found ${count} results out of expected ${expectedCount} for strategy ${strategyId}.`);

    // 6. Determine Status
    let status: 'pending' | 'completed' | 'error' = 'pending'; // Default to pending
    let details = `${count} / ${expectedCount} companies processed.`;

    if (count >= expectedCount) {
      status = 'completed';
      details = 'All companies processed.';
       console.log(`[scan-status] Scan considered complete for strategy ${strategyId}.`);
    } else {
         console.log(`[scan-status] Scan still pending for strategy ${strategyId}.`);
         // NOTE: This doesn't detect errors within the background process itself.
         // A more robust solution might involve the background process updating a central status field.
    }

    // 7. Prepare and Send Response
    const responsePayload = { status, details, processedCount: count, totalCount: expectedCount };
    const response = NextResponse.json(responsePayload, { status: 200 });

    // Include refreshed token if provided by verifyAuth
    if (auth.refreshedToken) {
         response.headers.set('X-Refreshed-Token', auth.refreshedToken);
         console.log('[scan-status] Included refreshed token in response.');
     }

    return response;

  } catch (error) {
    console.error('[scan-status] Error:', error);
    let errorMessage = 'Unknown server error';
    let statusCode = 500;

    if (error instanceof Error && error.message.includes('POSTGRES_URL')) {
        errorMessage = 'Database configuration error';
    } else if (error instanceof SyntaxError) {
        // Error parsing request JSON
        errorMessage = 'Invalid request body format';
        statusCode = 400;
    } else if (error instanceof Error) {
        errorMessage = error.message;
        // Keep default 500 or adjust based on specific DB errors if needed
    }

    return NextResponse.json({
      error: statusCode === 400 ? 'Bad Request' : 'Server Error',
      details: errorMessage
    }, { status: statusCode });

  } finally {
    if (dbClient) {
      await dbClient.end();
      console.log('[scan-status] DB disconnected.');
    }
  }
}

// Optional: Add OPTIONS handler if needed for CORS preflight requests
export async function OPTIONS() {
  // Basic CORS headers - adjust origins and methods as needed for your setup
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*', // Or specific origin
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}