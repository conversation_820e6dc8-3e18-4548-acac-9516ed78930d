#!/usr/bin/env python3
import os

class DirectoryHierarchy:
    def __init__(self, root_dir, exclude_folder="node_modules", indent="    "):
        self.root = root_dir
        self.exclude = exclude_folder
        self.indent = indent

    def build_hierarchy(self, current_path=None, level=0):
        if current_path is None:
            current_path = self.root
        lines = []
        # Determine the folder name for display
        folder_name = os.path.basename(current_path)
        if folder_name == "":
            folder_name = current_path
        prefix = self.indent * level
        lines.append(f"{prefix}- {folder_name}/")
        try:
            items = sorted(os.listdir(current_path), key=lambda s: s.lower())
        except PermissionError:
            # Skip folders that cannot be accessed
            return lines

        for item in items:
            if item == ".git":
                continue
            full_path = os.path.join(current_path, item)
            if os.path.isdir(full_path):
                if item == self.exclude:
                    # For excluded folders (e.g. node_modules), list as single node without recursing
                    lines.append(f"{prefix}{self.indent}- {item}/")
                else:
                    lines.extend(self.build_hierarchy(full_path, level + 1))
            else:
                lines.append(f"{prefix}{self.indent}- {item}")
        return lines

    def write_to_file(self, output_file):
        hierarchy = "\n".join(self.build_hierarchy())
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(hierarchy)

if __name__ == "__main__":
    base_dir = os.getcwd()
    output_filename = "hierarchy.md"
    dh = DirectoryHierarchy(base_dir)
    dh.write_to_file(output_filename)
    print(f"Directory hierarchy successfully generated in {output_filename}")
