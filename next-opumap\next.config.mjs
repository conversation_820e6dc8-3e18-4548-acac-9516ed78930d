/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'places.googleapis.com',
        port: '',
        pathname: '/v1/places/**', // Allows any path under /v1/places/
      },
      // You might also have photos from the older Places API, which use a different hostname
      // If you see URLs like maps.googleapis.com/maps/api/place/photo... add this:
      // {
      //   protocol: 'https',
      //   hostname: 'maps.googleapis.com',
      //   port: '',
      //   pathname: '/maps/api/place/photo/**',
      // },
    ],
  },
  // Any other Next.js configurations you might have can go here
};

export default nextConfig;
