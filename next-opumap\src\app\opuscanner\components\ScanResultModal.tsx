'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { ScanResult, DisplayScanResult } from '../types';

// Styling für das Modal, das dem AnalysisModal auf der Homepage entspricht
const modalStyles = {
  backdrop: 'fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4',
  container: 'bg-card rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden flex flex-col',
  header: 'flex justify-between items-center p-4 border-b border-border',
  title: 'text-xl font-semibold text-card-foreground',
  closeButton: 'text-muted-foreground hover:text-primary',
  body: 'p-6 overflow-y-auto flex-grow',
  dateInfo: 'mb-4 text-sm text-muted-foreground',
  content: 'prose prose-sm dark:prose-invert max-w-none p-4 rounded bg-background [&>strong]:font-bold [&>strong]:text-black dark:[&>strong]:text-white [&>h1]:text-3xl [&>h1]:font-bold [&>h1]:mb-6 [&>h2]:text-2xl [&>h2]:font-semibold [&>h2]:mb-4 [&>h3]:text-xl [&>h3]:mb-3 [&>p]:text-base [&>p]:leading-relaxed [&>ul]:list-disc [&>ul]:ml-6 [&>ul]:mb-4 [&>ol]:list-decimal [&>ol]:ml-6 [&>ol]:mb-4',
  footer: 'flex justify-end p-4 border-t border-border bg-muted/50 rounded-b-lg',
  closeButtonLarge: 'bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-5 rounded-lg transition duration-150 ease-in-out'
};

// Zusätzliche Styles
const additionalStyles = {
  modalCloseIcon: 'w-6 h-6',
};

interface ScanResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Typ der Prop erweitern
  result: ScanResult | DisplayScanResult | null;
}

export const ScanResultModal: React.FC<ScanResultModalProps> = ({ isOpen, onClose, result }) => {
  if (!result || !isOpen) return null;

  // Unterscheide, welche Felder angezeigt werden sollen
  const companyName = 'companyName' in result ? result.companyName : result.title;
  const descriptionText = 'result_text' in result ? result.result_text : result.description;
  // Ensure we have place_id for both types
  const placeId = 'place_id' in result ? result.place_id : ('company_id' in result ? null : null);

  // Datum formatieren (falls vorhanden)
  const scanDate = 'created_at' in result && result.created_at ? result.created_at : new Date().toISOString();
  const formattedDate = new Date(scanDate).toLocaleDateString('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    // Modal backdrop
    <div className={modalStyles.backdrop}>
      {/* Modal content */}
      <div className={modalStyles.container}>
        {/* Modal header */}
        <div className={modalStyles.header}>
          <h2 className={modalStyles.title}>
            Erkannte Chance: {companyName}
          </h2>
          {/* Close button */}
          <button onClick={onClose} className={modalStyles.closeButton}>
            <svg className={additionalStyles.modalCloseIcon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        {/* Modal body - scrollable */}
        <div className={modalStyles.body}>
          <div className={modalStyles.dateInfo}>
            Analyse vom: {formattedDate}
          </div>
          {/* Use prose for basic markdown-like formatting if analysis content is text */}
          <div className={modalStyles.content}>
            {/* Use ReactMarkdown to render descriptionText */}
            {descriptionText ? (
              <ReactMarkdown>{descriptionText}</ReactMarkdown>
            ) : (
              'Keine Detailbeschreibung verfügbar.'
            )}
          </div>
        </div>
        {/* Modal footer */}
        <div className={modalStyles.footer}>
          <button
            onClick={onClose}
            className={modalStyles.closeButtonLarge}
          >
            Schließen
          </button>
        </div>
      </div>
    </div>
  );
};