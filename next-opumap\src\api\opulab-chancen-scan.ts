import OpenAI from 'openai'; // Corrected quotes

// Initialize OpenRouter client
// Ensure OPENROUTER_API_KEY is available in the environment where this code runs
const openRouterClient = new OpenAI({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.OPENROUTER_API_KEY,
});

/**
 * Constructs the prompt for opportunity scan and calls OpenRouter AI.
 * @param userCompanyInfo - Information about the user's company.
 * @param strategyAnalysisResult - Analysis result of the selected user strategy.
 * @param companyAnalysisContent - Deep or fast analysis content of the target company.
 * @param companyName - Name of the target company.
 * @returns Promise<string> - The generated text result from the AI.
 */
export async function runChancenScan(
  userCompanyInfo: string,
  strategyAnalysisResult: string,
  companyAnalysisContent: string,
  companyName: string
): Promise<string> {

  const prompt = `--- // Corrected template literal

Gegebene Unternehmensinformationen:

///

${userCompanyInfo}

///

Ausgewählte Strategie aus dem Strategiewunsch:
///
${strategyAnalysisResult}
///
###
Erstelle mir konkrete Vorschläge und Ideen wie ich mit meinem Unternehmen welche Chancen bei dem folgend genannten Unternehmen (${companyName}) konkret nutzen könnte. Vermeide Fluff oder ungenaue sowie generalisierte Aussagen, sondern treffe konkrete Empfehlungen mit einer klaren Handelsanweisung so wie es ein Experte in dem Feld machen würde:

###

///

${companyAnalysisContent}
///

---`; // Corrected template literal

  try {
    const completion = await openRouterClient.chat.completions.create({
      model: "google/gemma-3-27b-it:free", //or google/gemini-2.5-flash-preview
      messages: [
        { role: "user", content: prompt }
      ],
    });

    const scanResultText = completion.choices?.[0]?.message?.content?.trim() ?? '';
    return scanResultText;

  } catch (aiError) {
    console.error(`Error calling OpenRouter for company ${companyName}`, aiError);
    // Rethrow or handle as appropriate for the calling function
    throw new Error(`AI service error for company ${companyName}`);
  }
}

// Basic check for required environment variable
if (!process.env.OPENROUTER_API_KEY) {
  console.warn('[opulab-chancen-scan.ts] OPENROUTER_API_KEY environment variable is not set. AI calls will fail.');
} 