import { NextRequest, NextResponse } from 'next/server';
import pkg from 'pg'; // Import pg
const { Client } = pkg; // Destructure Client

// Helper function to get pg client configuration
function getPgClientConfig() {
    const connectionString = process.env.POSTGRES_URL;
    if (!connectionString) {
        throw new Error('POSTGRES_URL environment variable not found.');
    }
    return {
        connectionString,
        ssl: {
            rejectUnauthorized: false, // Allow self-signed certificates (use with caution)
        },
    };
}

// GET /api/schnelle-analyse/all/[placeId] - Fetch all analyses by placeId
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function GET(request: NextRequest, context: any) {
    const client = new Client(getPgClientConfig());
    try {
        await client.connect();

        const placeId = context?.params?.placeId; // Access params safely with optional chaining

        if (!placeId) {
            return NextResponse.json({ error: 'Place ID ist erforderlich' }, { status: 400 });
        }

        // Use pg client query
        const query = `
            SELECT * FROM schnelle_analyse
            WHERE place_id = $1
            ORDER BY analysis_date DESC
          `;
        const result = await client.query(query, [placeId]);

        // Return all found analyses (could be an empty array)
        return NextResponse.json({
            count: result.rows.length,
            analyses: result.rows // Assuming the DB rows match the desired output structure
        });

    } catch (error: unknown) { // Use unknown type for error
        console.error('Fehler beim Abrufen aller Schnellanalysen:', error);
        const message = error instanceof Error ? error.message : 'Unbekannter Serverfehler';
        return NextResponse.json({
            error: 'Serverfehler beim Abrufen der Analysen',
            details: message
        }, { status: 500 });
    } finally {
         if (client) {
            await client.end();
         }
    }
}
