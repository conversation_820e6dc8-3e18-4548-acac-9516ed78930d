import React from "react";
import { BusinessData } from "@/types";
import { opuScannerStyles } from "../styles";
import Switch from "./CustomSwitch";
import type { User } from "@/contexts/AuthContext";

interface OpuScannerSectionProps {
  selectedBusiness: BusinessData | null;
  user: User | null;
  addToOpuScanner: boolean;
  setAddToOpuScanner: (value: boolean) => void;
  isAddingToOpuScanner: boolean;
  hasQuickAnalysis: boolean;
  hasDeepAnalysis: boolean;
}

/**
 * OpuScanner integration section component
 */
const OpuScannerSection: React.FC<OpuScannerSectionProps> = ({
  selectedBusiness,
  user,
  addToOpuScanner,
  setAddToOpuScanner,
  isAddingToOpuScanner,
  hasQuickAnalysis,
  hasDeepAnalysis
}) => {
  const analysisMissing = selectedBusiness && !hasQuickAnalysis && !hasDeepAnalysis;
  const isDisabled = !selectedBusiness || isAddingToOpuScanner || !user || analysisMissing;

  let statusTextContent;
  if (!selectedBusiness) {
    statusTextContent = "Wählen Sie ein Unternehmen aus";
  } else if (!user) {
    statusTextContent = "Bitte einloggen, um diese Funktion zu nutzen";
  } else if (analysisMissing) {
    statusTextContent = "Analyse (Schnell oder Tief) erforderlich";
  } else if (isAddingToOpuScanner) {
    statusTextContent = "Wird bearbeitet...";
  } else if (addToOpuScanner) {
    statusTextContent = "Unternehmen wird im OpuScanner angezeigt";
  } else {
    statusTextContent = "Unternehmen wird nicht im OpuScanner angezeigt";
  }

  return (
    <div className={opuScannerStyles.container}>
      <div className={opuScannerStyles.card}>
        <div className={opuScannerStyles.content}>
          <div className={opuScannerStyles.header}>
            <h2 className={opuScannerStyles.title}>OpuScanner Integration</h2>
          </div>
          <div className={opuScannerStyles.rowContainer}>
            <div className={opuScannerStyles.statusContainer}>
              <div className={opuScannerStyles.statusBox}>
                <p className={opuScannerStyles.statusText}>
                  <span className={opuScannerStyles.statusIndicator(addToOpuScanner && !analysisMissing)}></span>
                  {statusTextContent}
                </p>
              </div>
            </div>
            <div className={opuScannerStyles.toggleContainer}>
              <span className={opuScannerStyles.toggleLabel}>
                Zum OpuScanner hinzufügen
              </span>
              <Switch
                checked={addToOpuScanner}
                onCheckedChange={(checked) => {
                  if (checked && analysisMissing) {
                    return;
                  }
                  setAddToOpuScanner(checked);
                }}
                disabled={isDisabled as boolean | undefined}
                className={isDisabled ? opuScannerStyles.toggleDisabled : ""}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpuScannerSection;
