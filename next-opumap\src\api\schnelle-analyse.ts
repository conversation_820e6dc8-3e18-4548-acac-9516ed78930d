import { BusinessData, AnalysisResult as SharedAnalysisResult } from '@/types'; // Import shared types
import { fetchWithAuth } from '@/lib/supabaseClient';

// Define structure for saving data to our backend
interface SaveAnalysisPayload {
    businessData: {
        name: string;
        address: string;
        phone: string;
        website: string;
        placeId: string;
        lat: number;
        lng: number;
    };
    analysisContent: string;
    analysisDate: string; // ISO string date
}

/**
 * Runs a quick analysis using the Perplexity API and saves the result.
 * IMPORTANT: Calling Perplexity API directly from the client exposes the API key.
 * Consider moving this logic to a Next.js API route for better security.
 * @param businessData - Data of the business to analyze.
 * @returns The analysis content and the result from saving to the database, or null on failure.
 */
// Define a more specific return type if the structure of saveResult is known
interface RunQuickAnalysisResult {
    analysisContent: string;
    analysisDate: string;
    saveResult: unknown; // Use 'unknown' instead of 'any' for the save result initially
}
export async function runQuickAnalysis(businessData: BusinessData): Promise<RunQuickAnalysisResult | null> {
    // Construct prompt safely, checking for property existence
    let prompt = "Gebe mir in kurzen Stichpunkten eine schnelle Analyse über das folgende Unternehmen mit Fokus auf Key-Kennzahlen und Wettbewerbsposition:\n\n";
    if (businessData.name) prompt += `- Unternehmensname: ${businessData.name}\n`;
    if (businessData.formatted_address) prompt += `- Adresse: ${businessData.formatted_address}\n`;
    if (businessData.formatted_phone_number) prompt += `- Telefonnummer: ${businessData.formatted_phone_number}\n`;
    if (businessData.website) prompt += `- Webseite: ${businessData.website}\n`;

    // Get API key from environment variables (client-side accessible)
    const token = process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;
    if (!token) {
        console.error("Perplexity API Token (NEXT_PUBLIC_PERPLEXITY_API_KEY) is not defined.");
        throw new Error("Perplexity API Token ist nicht konfiguriert.");
    }

    const options = {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            accept: 'application/json',
        },
        body: JSON.stringify({
            model: "sonar",
            messages: [
                { role: "system", content: "Be precise and concise. Provide key metrics and competitive position analysis in bullet points. No explanations or extra formatting." },
                { role: "user", content: prompt }
            ],
            // Adjust parameters as needed
            // max_tokens: 3000,
            // temperature: 0.2,
        })
    };

    try {
        const response = await fetch('https://api.perplexity.ai/chat/completions', options);
        if (!response.ok) {
            const errorText = await response.text();
            console.error("Perplexity API Error Response:", errorText);
            throw new Error(`Perplexity API Fehler (${response.status}): ${errorText.substring(0, 100)}...`); // Limit error message length
        }

        const data = await response.json();
        let analysisContent = "";

        // Extract analysis content
        if (data?.choices?.[0]?.message?.content) {
            analysisContent = data.choices[0].message.content;
        } else {
            console.warn("Could not extract analysis content from Perplexity response:", data);
            analysisContent = "Analyse konnte nicht extrahiert werden."; // Provide default message
        }

        // Save the analysis to the application's database via its API route
        const analysisDate = new Date().toISOString();
        const saveResult = await saveAnalysisToDatabase(businessData, analysisContent, analysisDate);

        return {
            analysisContent,
            analysisDate, // Return the date used for saving
            saveResult // Assuming saveResult is the direct JSON response from the backend save operation
        };
    } catch (error: unknown) {
        console.error("Fehler in runQuickAnalysis:", error);
        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
        // Re-throw or handle error appropriately for the UI
        throw new Error(`Fehler bei der Schnellanalyse: ${message}`);
    }
}

/**
 * Saves the analysis result to the application's backend database.
 * @param businessData - Data of the analyzed business.
 * @param analysisContent - The content generated by the analysis API.
 * @param analysisDate - The ISO string date of the analysis.
 * @returns The result from the backend save operation (use unknown or a specific type).
 */
async function saveAnalysisToDatabase(businessData: BusinessData, analysisContent: string, analysisDate: string): Promise<unknown> {
    // Extract lat/lng carefully, checking for different structures
    let lat: number = 0;
    let lng: number = 0;
    if ('geometry' in businessData && businessData.geometry?.location) {
        const location = businessData.geometry.location;
        // Check if lat/lng are functions and call them, otherwise treat as numbers
        if (typeof location.lat === 'function' && typeof location.lng === 'function') {
            lat = location.lat();
            lng = location.lng();
        } else {
            // Fallback: Explicitly cast to unknown then LatLngLiteral
            const literalLocation = location as unknown as google.maps.LatLngLiteral;
            lat = literalLocation.lat ?? 0;
            lng = literalLocation.lng ?? 0;
        }
    } else if ('position' in businessData && businessData.position) { // Handle DemoMarker position
        lat = businessData.position.lat;
        lng = businessData.position.lng;
    }

    // Determine placeId safely
    let placeId = businessData.place_id ?? ""; // Prefer place_id if it exists
    if (!placeId && 'id' in businessData) { // If no place_id, check if it's a DemoMarker with an id
        placeId = `demo_${businessData.id}`;
    }

    const payload: SaveAnalysisPayload = {
        businessData: {
            name: businessData.name ?? "Unbekannt",
            address: businessData.formatted_address ?? "",
            phone: businessData.formatted_phone_number ?? "",
            website: businessData.website ?? "",
            placeId: placeId, // Use the safely determined placeId
            lat,
            lng
        },
        analysisContent,
        analysisDate
    };

    try {
        // Use the new analyses endpoint with type=quick
        const response = await fetchWithAuth('/api/analyses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...payload,
                type: 'quick' // Add the type parameter
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Server Error Saving Analysis:", errorText);
            throw new Error(`Server-Fehler (${response.status}) beim Speichern der Analyse.`);
        }

        return await response.json(); // Return the parsed JSON response
    } catch (error: unknown) {
        console.error("Fehler beim Speichern der Analyse:", error);
        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';
        throw new Error(`Fehler beim Speichern der Analyse: ${message}`);
    }
}

/**
 * Fetches existing quick analysis data for a given place ID from the backend.
 * @param placeId - The Google Place ID of the business.
 * @returns The analysis data or null if not found.
 */
export async function getQuickAnalysis(placeId: string): Promise<SharedAnalysisResult | null> { // Use shared type for return
    if (!placeId) {
        console.warn("getQuickAnalysis called without placeId");
        return null;
    }
    try {
        // Use the new analyses/quick endpoint
        const response = await fetchWithAuth(`/api/analyses/quick/${placeId}`);

        // Handle authentication errors gracefully
        if (response.status === 401) {
            console.warn("Authentication required for fetching analysis data");
            return null; // Return null instead of throwing an error
        }

        if (!response.ok) {
            if (response.status === 404) {
                console.log(`Keine schnelle Analyse für placeId ${placeId} gefunden.`);
                return null; // Not found is not necessarily an error here
            }
            const errorText = await response.text();
            console.error("Server Error Fetching Analysis:", errorText);
            // Don't throw, just return null to prevent UI crashes
            return null;
        }

        const data: SharedAnalysisResult = await response.json(); // Use shared type
        return data;
    } catch (error: unknown) {
        console.error("Fehler beim Abrufen der Analyse:", error);
        // Always return null to indicate failure without crashing UI
        return null;
    }
}
