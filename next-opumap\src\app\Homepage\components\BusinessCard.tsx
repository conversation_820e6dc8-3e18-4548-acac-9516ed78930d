import React from "react";
import Image from "next/image";
import { ScrollArea } from "@/components/ui/scroll-area";
import { BusinessData } from "@/types";
import {
  flipCardStyles,
  placeholderCardStyles,
  businessCardAdditionalStyles
} from "../styles";
import { iconPaths } from "@/styles";
import { getPhotoUrl } from "@/utils";
import { formatOpeningHours } from "@/utils/formatters";

interface BusinessCardProps {
  selectedBusiness: BusinessData | null;
  isFlipped: boolean;
  toggleFlip: () => void;
}

/**
 * Business information card component with flip animation
 */
const BusinessCard: React.FC<BusinessCardProps> = ({
  selectedBusiness,
  isFlipped,
  toggleFlip
}) => {
  if (!selectedBusiness) {
    return (
      // Placeholder card when no business is selected
      <div className={placeholderCardStyles.container}>
        {/* Simple location icon */}
        <svg
          className={placeholderCardStyles.icon}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d={iconPaths.location.path1}
          ></path>
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d={iconPaths.location.path2}
          ></path>
        </svg>
        <p className={placeholderCardStyles.text}>Klicken Sie auf ein Unternehmen auf der Karte.</p>
      </div>
    );
  }

  return (
    // Flip card container - uses inline styles with responsive Tailwind classes
    <div
      className={businessCardAdditionalStyles.flipCardContainer}
      style={flipCardStyles.container}
      onClick={toggleFlip} // Toggle flip on click
    >
      <div style={{ ...flipCardStyles.inner, ...(isFlipped ? flipCardStyles.flipped : {}) }}>
        {/* Front Side */}
        <div style={flipCardStyles.front}>
          <h2 style={flipCardStyles.title}>{selectedBusiness.name}</h2>
          <p style={flipCardStyles.address}>{selectedBusiness.formatted_address}</p>
          {/* Conditionally render image/description */}
          {(("photos" in selectedBusiness && selectedBusiness.photos && selectedBusiness.photos.length > 0) ||
            ("description" in selectedBusiness && selectedBusiness.description)) && (
              <div style={flipCardStyles.imageContainer}>
                <Image
                  src={getPhotoUrl(selectedBusiness) || "https://via.placeholder.com/120"}
                  alt={selectedBusiness.name || "Business image"}
                  width={120}
                  height={120}
                  style={flipCardStyles.image}
                  // Prevent optimization for placeholder images
                  unoptimized={getPhotoUrl(selectedBusiness)?.startsWith("https://via.placeholder.com")}
                />
              </div>
            )}
          <p style={flipCardStyles.hint}>Klicken für Details</p>
        </div>
        {/* Back Side */}
        <div style={flipCardStyles.back}>
          {/* Use ShadCN ScrollArea for potentially long content */}
          <ScrollArea className={businessCardAdditionalStyles.scrollArea}>
            <h3 style={flipCardStyles.sectionTitle}>Kontakt</h3>
            <p><strong>Telefon:</strong> {selectedBusiness.formatted_phone_number || "N/A"}</p>
            <p>
              <strong>Website:</strong>{" "}
              {selectedBusiness.website ? (
                <a
                  href={selectedBusiness.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  // Style link appropriately for the card background
                  style={businessCardAdditionalStyles.websiteLink}
                >
                  Besuchen
                </a>
              ) : "N/A"}
            </p>
            {/* Display opening hours if available */}
            {"opening_hours" in selectedBusiness && selectedBusiness.opening_hours && (
              <div>
                <h3 style={flipCardStyles.sectionTitle}>Öffnungszeiten</h3>
                <ul style={flipCardStyles.infoList}>
                  {(selectedBusiness.opening_hours.weekday_text && selectedBusiness.opening_hours.weekday_text.length > 0
                    ? selectedBusiness.opening_hours.weekday_text
                    : formatOpeningHours(selectedBusiness.opening_hours.periods ?? [])
                  ).map((day, index) => (
                    <li key={index} style={flipCardStyles.infoItem}>{day}</li>
                  ))}
                </ul>
              </div>
            )}
            {/* Display description if available (for demo markers) */}
            {"description" in selectedBusiness && selectedBusiness.description && (
              <div>
                <h3 style={flipCardStyles.sectionTitle}>Beschreibung</h3>
                <p>{selectedBusiness.description}</p>
              </div>
            )}
            <p style={flipCardStyles.hint}>Klicken zum Schließen</p>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};

export default BusinessCard;
