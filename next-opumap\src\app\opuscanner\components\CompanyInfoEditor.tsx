'use client';
import React, { ChangeEvent } from 'react';
import MarkdownEditor from '@/components/MarkdownEditor';

interface CompanyInfoEditorProps {
  companyInfo: string;
  isSaving: boolean;
  onChange: (value?: string) => void;
  onSave: () => void;
  useMarkdown?: boolean;
}

const CompanyInfoEditor: React.FC<CompanyInfoEditorProps> = ({
  companyInfo,
  isSaving,
  onChange,
  onSave,
  useMarkdown = true // Default to using markdown editor
}) => {
  // Handler for traditional textarea change
  const handleTextareaChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  return (
    <div className="bg-card p-5 rounded-lg shadow-sm flex flex-col h-full">
      <h2 className="font-semibold text-lg text-card-foreground mb-4 border-b border-border pb-3 flex-shrink-0">
        Unternehmensinformationen
      </h2>

      {useMarkdown ? (
        <div className="flex-grow flex flex-col">
          <MarkdownEditor
            value={companyInfo}
            onChange={onChange}
            height={380}
            preview="edit"
          />
        </div>
      ) : (
        <textarea
          id="unternehmensinfos"
          name="unternehmensinfos"
          className="w-full p-2 border border-input rounded-md focus:ring-ring focus:border-primary text-foreground bg-background text-sm resize-none flex-grow"
          placeholder="Geben Sie hier Ihre Unternehmensinformationen ein..."
          value={companyInfo}
          onChange={handleTextareaChange}
        />
      )}
      <div className="mt-3 flex-shrink-0">
        <button
          onClick={onSave}
          disabled={isSaving}
          className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white dark:text-primary-foreground font-semibold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out disabled:opacity-50"
        >
          {isSaving ? "Speichern..." : "Änderungen speichern"}
        </button>
      </div>
    </div>
  );
};

export default CompanyInfoEditor;
