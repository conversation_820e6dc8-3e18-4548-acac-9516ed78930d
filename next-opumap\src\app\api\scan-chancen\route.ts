import { NextRequest, NextResponse } from 'next/server';
import { runChancenScan } from '@/api/opulab-chancen-scan';
import pkg from 'pg';
const { Client } = pkg;
import { verifySupabaseAuth } from '@/lib/supabaseAuth';
import { mapUuidToNumericId } from '@/utils/userIdMapping';

// Helper: PG client configuration (copied from scan-results/route.ts)
function getPgClientConfig() {
  // Deaktiviere SSL-Überprüfung für selbstsignierte Zertifikate
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

  const connectionString = process.env.POSTGRES_URL;
  if (!connectionString) {
    console.error('POSTGRES_URL environment variable not found.');
    throw new Error('POSTGRES_URL environment variable not found.');
  }
  return {
    connectionString,
    ssl: { rejectUnauthorized: false }
  };
}

// --- Background Scan Processing Function ---
async function processScanInBackground(userId: string, strategyId: number, placeIds: string[], signal: AbortSignal, onProgress?: (payload: { processedCount: number; result: { place_id: string; company_name: string; result_text: string }; total: number }) => void) {
  console.log(`[Background Scan] Starting for user ${userId}, strategy ${strategyId}, place_ids: ${placeIds.join(', ')}`);
  let dbClient: pkg.Client | null = null;
  const results: { place_id: string; company_name: string; result_text: string }[] = [];
  const total = placeIds.length;

  try {
    dbClient = new Client(getPgClientConfig());
    await dbClient.connect();
    console.log('[Background Scan] DB connected.');

    // 1. Load userCompanyInfo from profiles table
    const profileQuery = 'SELECT company_info_points FROM profiles WHERE id = $1';
    const profileResult = await dbClient.query(profileQuery, [userId]); // userId is now a string (UUID)
    const userCompanyInfo = profileResult.rows[0]?.company_info_points;
    if (!userCompanyInfo) {
      console.error(`[Background Scan] User company info not found for user ${userId}`);
      throw new Error(`User company info not found for user ${userId}`);
    }
    console.log('[Background Scan] Loaded user company info.');

    // 2. Load strategyAnalysisResult from strategies table
    const strategyQuery = 'SELECT analysis_result FROM strategies WHERE id = $1 AND user_id = $2';
    const strategyResult = await dbClient.query(strategyQuery, [strategyId, userId]); // userId is now a string (UUID)
    const strategyAnalysisResult = strategyResult.rows[0]?.analysis_result;
    if (!strategyAnalysisResult) {
      console.error(`[Background Scan] Strategy analysis result not found for strategy ${strategyId}, user ${userId}`);
      throw new Error(`Strategy analysis result not found for strategy ${strategyId}`);
    }
    console.log('[Background Scan] Loaded strategy analysis result.');

    // 3. Iterate over place_ids directly
    for (const placeId of placeIds) {
      if (signal.aborted) {
        console.log('[Background Scan] Aborted by client disconnect');
        break;
      }
      console.log(`[Background Scan] Processing place_id: ${placeId}`);
      let companyName: string | null = null; // Define companyName here
      try {
        // 3a. Get company data directly from companies table using place_id
        const companyQuery = 'SELECT id, name FROM companies WHERE place_id = $1';
        const companyResult = await dbClient.query(companyQuery, [placeId]);
        const companyData = companyResult.rows[0];

        if (!companyData || !companyData.name) {
          console.error(`[Background Scan] Company data not found for place_id ${placeId}`);
          console.error(`[Background Scan] Query used: SELECT id, name FROM companies WHERE place_id = ${placeId}`);
          continue; // Skip this company
        }

        companyName = companyData.name;
        console.log(`[Background Scan] Found company: ${companyName} (Place ID: ${placeId})`);

        // Check if this company is in the user's selected companies
        const selectedCompanyQuery = 'SELECT id FROM selected_companies WHERE place_id = $1 AND user_id = $2 AND is_deleted = false';
        const selectedCompanyResult = await dbClient.query(selectedCompanyQuery, [placeId, userId]);

        if (selectedCompanyResult.rows.length === 0) {
          console.log(`[Background Scan] Company ${companyName} (Place ID: ${placeId}) is not in user's selected companies. Adding it.`);

          // Add to selected_companies if not already there
          try {
            await dbClient.query(
              'INSERT INTO selected_companies (place_id, name, company_name, user_id, created_at) VALUES ($1, $2, $3, $4, NOW())',
              [placeId, companyName, companyName, userId]
            );
            console.log(`[Background Scan] Added ${companyName} to selected_companies`);
          } catch (insertError) {
            // Ignore duplicate key errors, which means the company is already in selected_companies
            if (!(insertError instanceof Error && insertError.message.includes('duplicate key'))) {
              console.error(`[Background Scan] Error adding company to selected_companies: ${insertError}`);
            }
          }
        }

        // Get company_id for analyses lookup
        const companyId = companyData.id;

        // 3c. Load analysis_content (Prioritize deep analysis, fallback to fast analysis)
        let companyAnalysisContent: string | null = null;

        // Try fetching deep analysis first
        const deepAnalysisQuery = 'SELECT content FROM analyses WHERE company_id = $1 AND type = $2 ORDER BY analysis_date DESC LIMIT 1';
        const deepAnalysisResult = await dbClient.query(deepAnalysisQuery, [companyId, 'deep']);
        if (deepAnalysisResult.rows.length > 0 && deepAnalysisResult.rows[0].content) {
            companyAnalysisContent = deepAnalysisResult.rows[0].content;
            console.log(`[Background Scan] Using DEEP analysis content for ${companyName}`);
        } else {
            // If not found in deep, try fetching quick analysis
            console.log(`[Background Scan] Deep analysis not found for ${companyName}. Trying quick analysis...`);
            const quickAnalysisQuery = 'SELECT content FROM analyses WHERE company_id = $1 AND type = $2 ORDER BY analysis_date DESC LIMIT 1';
            const quickAnalysisResult = await dbClient.query(quickAnalysisQuery, [companyId, 'quick']);
            if (quickAnalysisResult.rows.length > 0 && quickAnalysisResult.rows[0].content) {
                companyAnalysisContent = quickAnalysisResult.rows[0].content;
                console.log(`[Background Scan] Using QUICK analysis content for ${companyName}`);
            }
        }

        // Check if we found any analysis content
        if (!companyAnalysisContent) {
            console.error(`[Background Scan] NO analysis content found (neither deep nor fast) for company ${companyName} (Place ID: ${placeId})`);
            continue; // Skip this company if no analysis is available
        }

        // Ensure companyName is a string before calling runChancenScan
        if (typeof companyName !== 'string') {
             console.error(`[Background Scan] companyName is unexpectedly null for place_id ${placeId} after checks. Skipping.`);
             continue;
        }

        // 3c. Call runChancenScan
        console.log(`[Background Scan] Calling runChancenScan for ${companyName}...`);
        const scanResultText = await runChancenScan(
          userCompanyInfo,
          strategyAnalysisResult,
          companyAnalysisContent, // Use the content found from either deep or fast analysis
          companyName
        );
        console.log(`[Background Scan] Received scan result for ${companyName}. Length: ${scanResultText.length}`);

        if (!scanResultText || scanResultText.trim() === '') {
             console.warn(`[Background Scan] Empty scan result received for company ${companyName}. Skipping save.`);
             continue; // Skip saving if result is empty
        }

        // 3d. Save the result to scan_results using place_id
        try {
          // Check if an entry already exists
          const checkQuery = `
            SELECT id FROM scan_results
            WHERE user_id = $1 AND strategy_id = $2 AND place_id = $3
          `;
          const checkResult = await dbClient.query(checkQuery, [userId, strategyId, placeId]);

          let saveResult;
          if (checkResult.rows.length > 0) {
            // Entry exists, update it
            const updateQuery = `
              UPDATE scan_results
              SET result_text = $4, created_at = NOW()
              WHERE user_id = $1 AND strategy_id = $2 AND place_id = $3
            `;
            saveResult = await dbClient.query(updateQuery, [userId, strategyId, placeId, scanResultText]);
            console.log(`[Background Scan] Updated existing scan result for company ${companyName}`);
          } else {
            // No entry exists, insert new one
            const insertQuery = `
              INSERT INTO scan_results (user_id, strategy_id, company_id, place_id, result_text, created_at)
              VALUES ($1, $2, $3, $4, $5, NOW())
            `;
            saveResult = await dbClient.query(insertQuery, [userId, strategyId, companyId, placeId, scanResultText]);
            console.log(`[Background Scan] Inserted new scan result for company ${companyName}`);
          }

          if (saveResult.rowCount === 1) {
            console.log(`[Background Scan] Successfully saved scan result for company ${companyName}`);
          } else {
            console.error(`[Background Scan] Save operation did not affect exactly one row for company ${companyName}. Rows affected: ${saveResult.rowCount}`);
          }
        } catch (dbError) {
          console.error(`[Background Scan] Error saving scan result for company ${companyName}:`, dbError);
          console.error(`[Background Scan] Attempted to insert with user_id: ${userId}, strategy_id: ${strategyId}, place_id: ${placeId}`);
          throw dbError; // Pass the error up
        }

        console.log(`[Background Scan] Saved scan result for company ${companyName}`);
        results.push({
          place_id: placeId,
          company_name: companyName,
          result_text: scanResultText
        });

        if (onProgress) {
          onProgress({ processedCount: results.length, total, result: results[results.length - 1] });
        }

      } catch (companyError) {
        // Ensure companyName is logged even if error happens after fetching it
        const logName = companyName || `Place ID ${placeId}`;
        console.error(`[Background Scan] Error processing company ${logName}:`, companyError);
        // Continue with the next company even if one fails
      }
    }
    console.log(`[Background Scan] Finished processing all companies for user ${userId}, strategy ${strategyId}.`);

    } catch (error) {
      console.error('[Background Scan] General error during background processing:', error);
    } finally {
      if (dbClient) {
        await dbClient.end();
        console.log('[Background Scan] DB disconnected.');
      }
    }
    return results;
}

// --- API Route Handler ---
export async function POST(request: NextRequest) {
  try {
    // Verify user authentication using Supabase Auth
    const authResult = await verifySupabaseAuth();

    if (!authResult || !authResult.user) {
      console.error('Authentication failed via verifySupabaseAuth');
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'Authentication failed'
      }, { status: 401 });
    }

    const { user } = authResult;

    // Map the UUID to a numeric ID for database compatibility
    const userIdString = await mapUuidToNumericId(user.id);
    console.log('User authenticated via verifySupabaseAuth:', user.id, 'mapped to numeric ID:', userIdString);

    if (!process.env.OPENROUTER_API_KEY) {
      console.error('OPENROUTER_API_KEY is not configured');
      return NextResponse.json({ error: 'Server configuration error', details: 'OpenRouter API Key missing' }, { status: 500 });
    }

    try {
        getPgClientConfig();
    } catch (configError) {
        console.error('Database configuration error:', configError);
        return NextResponse.json({ error: 'Server configuration error', details: 'Database connection string missing' }, { status: 500 });
    }

    const { strategyId, selectedCompanyIds, placeIds, companyIds, isCompanyIds } = await request.json();
    console.log('Received request with:', { strategyId, selectedCompanyIds, placeIds, companyIds, isCompanyIds });
    const encoder = new TextEncoder();
    const { signal } = request;
    const stream = new ReadableStream({
      async start(controller) {
        signal.addEventListener('abort', () => {
          console.log('Client disconnected – scan aborted');
        }, { once: true });

        // Validate place_ids before processing
        let dbClient: pkg.Client | null = null;
        const validPlaceIds: string[] = [];
        const skippedPlaceIds: { id: string, reason: string }[] = [];

        // Extract all place_ids from the request
        const requestPlaceIds = placeIds || [];

        try {
          dbClient = new Client(getPgClientConfig());
          await dbClient.connect();
          console.log('[Validation] DB connected for place_id validation.');

          // Process all place_ids
          console.log('[Validation] Validating place_ids from frontend.');
          for (const placeId of requestPlaceIds) {
            // Check if this place_id exists in the companies table
            const companyQuery = 'SELECT id, name FROM companies WHERE place_id = $1';
            const companyResult = await dbClient.query(companyQuery, [placeId]);

            if (companyResult.rows.length > 0) {
              const companyId = companyResult.rows[0].id;
              const companyName = companyResult.rows[0].name;

              // Check if this place_id is in selected_companies for the user
              const selectedCompanyQuery = 'SELECT id FROM selected_companies WHERE place_id = $1 AND user_id = $2 AND is_deleted = false';
              const selectedCompanyResult = await dbClient.query(selectedCompanyQuery, [placeId, userIdString]);

              if (selectedCompanyResult.rows.length === 0) {
                // If not in selected_companies, add it
                console.log(`[Validation] Place_id ${placeId} found in companies but not in selected_companies for user ${userIdString}. Adding it.`);
                try {
                  await dbClient.query(
                    'INSERT INTO selected_companies (place_id, name, company_name, user_id, company_id, created_at) VALUES ($1, $2, $3, $4, $5, NOW())',
                    [placeId, companyName, companyName, userIdString, companyId]
                  );
                  console.log(`[Validation] Added place_id ${placeId} to selected_companies for user ${userIdString}`);
                } catch (insertError) {
                  console.error(`[Validation] Error adding place_id ${placeId} to selected_companies: ${insertError}`);
                  // Continue anyway since we have the place_id
                }
              }

              // Add to valid place_ids for processing
              validPlaceIds.push(placeId);
              console.log(`[Validation] Validated place_id ${placeId} for company ${companyName}`);
            } else {
              skippedPlaceIds.push({ id: placeId, reason: 'Place ID not found in companies table' });
              console.error(`[Validation] Skipped place_id ${placeId} as it was not found in companies table.`);
            }
          }
        } catch (error) {
          console.error('[Validation] Error during company ID or place_id validation:', error);
        } finally {
          if (dbClient) {
            await dbClient.end();
            console.log('[Validation] DB disconnected after company ID or place_id validation.');
          }
        }

        const total = validPlaceIds.length;
        console.log(`[Validation] Valid place IDs for processing: ${validPlaceIds.join(', ')}`);
        console.log(`[Validation] Skipped place IDs: ${JSON.stringify(skippedPlaceIds)}`);

        // Wrap onProgress to avoid enqueue on closed stream
        const onProgressWrapped = ({ processedCount, total, result }: { processedCount: number; total: number; result: { place_id: string; company_name: string; result_text: string; }; }) => {
          if (signal.aborted) return;
          try {
            controller.enqueue(encoder.encode(`data:${JSON.stringify({ type: 'progress', processedCount, total })}\n\n`));
            controller.enqueue(encoder.encode(`data:${JSON.stringify({ type: 'result', result })}\n\n`));
          } catch (e) {
            console.warn('Stream closed, skipping enqueue.', e);
          }
        };

        controller.enqueue(encoder.encode(`data:${JSON.stringify({type:'init', total, skipped: skippedPlaceIds})}\n\n`));
        try {
          const finalResults = await processScanInBackground(
            userIdString, // Pass userIdString (UUID) directly
            Number(strategyId),
            validPlaceIds,
            signal,
            onProgressWrapped
          );
          if (!signal.aborted) {
            try {
              controller.enqueue(encoder.encode(`data:${JSON.stringify({ type: 'done', total, results: finalResults, skipped: skippedPlaceIds })}\n\n`));
            } catch (e) {
              console.warn('Stream closed before done event.', e);
            }
          }
        } catch (error: Error | unknown) {
          if (!signal.aborted) {
            try {
              const errorMessage = error instanceof Error ? error.message : 'Scan error';
              controller.enqueue(encoder.encode(`data:${JSON.stringify({ type: 'error', message: errorMessage })}\n\n`));
            } catch (e) {
              console.warn('Stream closed before error event.', e);
            }
          }
        } finally {
          controller.close();
        }
      }
    });
    const response = new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive'
      }
    });
    return response;

  } catch (error) {
    console.error('Unexpected error in scan-chancen API route:', error);
    let errorMessage = 'Unknown server error';
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    return NextResponse.json({
      error: 'Server error',
      details: errorMessage
    }, { status: 500 });
  }
}

// You might want to add OPTIONS handler for CORS if called from different origins, etc.
// export async function OPTIONS(request: Request) {
//   // Handle CORS preflight requests
//   return new NextResponse(null, { status: 204 });
// }
