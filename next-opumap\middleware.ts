import { NextRequest, NextResponse } from 'next/server';
import { updateSession } from './src/utils/supabase/middleware';

export async function middleware(req: NextRequest) {
  // Always run updateSession for proper cookie synchronization
  // This is crucial for Supabase SSR to work correctly
  console.log('Middleware: Processing request for:', req.nextUrl.pathname);
  
  try {
    const result = await updateSession(req);
    console.log('Middleware: updateSession completed successfully');
    return result;
  } catch (error) {
    console.error('Middleware: Error in updateSession:', error);
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api routes (these handle their own auth)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ]
};
