import { useState, useRef, useCallback, useEffect } from "react";
import { mapDefaults, darkMapStyle } from "../styles";
import { MapState } from "@/types";
import type { User } from "@/contexts/AuthContext";

/**
 * Custom hook to manage map state including center, zoom, and persistence
 */
export const useMapState = (user: User | null) => {
  // Map state variables
  const [mapCenter, setMapCenter] = useState<google.maps.LatLngLiteral>(mapDefaults.center);
  const [mapZoom, setMapZoom] = useState<number>(mapDefaults.zoom);
  const [mapStateLoaded, setMapStateLoaded] = useState<boolean>(false);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(
    typeof document !== 'undefined' && document.documentElement.classList.contains('dark')
  );

  // Map reference
  const mapRef = useRef<google.maps.Map | null>(null);
  
  // Track user changes to reset map state on login/logout
  const prevUserRef = useRef<User | null>(null);

  // Function to save map state to localStorage
  const saveMapState = useCallback((businessPlaceId?: string) => {
    if (!user || !mapRef.current) return; // Only save if user is logged in and map is loaded

    const map = mapRef.current;
    const center = map.getCenter()?.toJSON() || mapDefaults.center;
    const zoom = map.getZoom() || mapDefaults.zoom;

    const mapState: MapState = {
      center,
      zoom,
      selectedBusinessPlaceId: businessPlaceId,
      lastUpdated: new Date().toISOString()
    };

    localStorage.setItem(mapDefaults.storageKey, JSON.stringify(mapState));
    console.log('Map state saved:', mapState);
  }, [user]);

  // Function to load map state from localStorage
  const loadMapState = useCallback(() => {
    if (!user) return null; // Only load if user is logged in

    try {
      const savedState = localStorage.getItem(mapDefaults.storageKey);
      if (!savedState) return null;

      const mapState: MapState = JSON.parse(savedState);
      console.log('Map state loaded:', mapState);
      return mapState;
    } catch (error) {
      console.error('Error loading map state:', error);
      return null;
    }
  }, [user]);

  // Function to reset the map to default state
  const resetMapToDefaults = useCallback(() => {
    console.log("Resetting map to defaults");

    // Reset map center and zoom
    setMapCenter(mapDefaults.center);
    setMapZoom(mapDefaults.zoom);
    setMapStateLoaded(false);

    // Update map if it's loaded
    if (mapRef.current) {
      mapRef.current.panTo(mapDefaults.center);
      mapRef.current.setZoom(mapDefaults.zoom);
    }

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem(mapDefaults.storageKey);
    }
  }, []);

  // Handle dark mode changes
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const isDark = document.documentElement.classList.contains('dark');
      setIsDarkMode(isDark);
      if (mapRef.current) {
        mapRef.current.setOptions({ styles: isDark ? darkMapStyle : [] });
      }
    });
    
    if (typeof document !== 'undefined') {
      observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
    }
    
    return () => observer.disconnect();
  }, []);

  // Reset map state ONLY on login/logout, not on page navigation
  useEffect(() => {
    // Only run this effect when the component mounts or when user changes
    // If this is the first render, initialize prevUserRef
    if (prevUserRef.current === null && user !== null) {
      // First login after component mount
      console.log("First login after component mount");
      prevUserRef.current = user;
      return;
    }

    // If user changed from non-null to null (logout) or from null to non-null (login)
    // But not when it's just a re-render with the same user
    if ((prevUserRef.current === null && user !== null) || (prevUserRef.current !== null && user === null)) {
      console.log("User login/logout detected, resetting map to defaults");
      resetMapToDefaults();
    }

    // Update the previous user ref
    prevUserRef.current = user;
  }, [user, resetMapToDefaults]);

  return {
    mapCenter,
    setMapCenter,
    mapZoom,
    setMapZoom,
    mapStateLoaded,
    setMapStateLoaded,
    isDarkMode,
    mapRef,
    saveMapState,
    loadMapState,
    resetMapToDefaults
  };
};
