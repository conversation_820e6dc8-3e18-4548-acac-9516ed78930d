'use client';

import React, { useState, useEffect } from 'react';
import Loader from '@/components/ui/Loader'; // Importiere den globalen Loader
import OpulabStrategiePage from './OpulabStrategiePage';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function OpulabPage() {
  const [isClient, setIsClient] = useState(false);
  const [showLoader, setShowLoader] = useState(false);

  // Set isClient to true after component mounts
  useEffect(() => {
    setIsClient(true);
    
    // Nur den Loader anzeigen, wenn die Hydration länger als 300ms dauert
    const loaderTimer = setTimeout(() => {
      if (!isClient) {
        setShowLoader(true);
      }
    }, 300);
    
    return () => clearTimeout(loaderTimer);
  }, [isClient]);

  // Show loading state if not yet mounted on client and after delay
  if (!isClient) {
    // Leeres Fragment zurückgeben, bis showLoader true ist
    if (!showLoader) {
      return <></>;
    }
    
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader />
      </div>
    );
  }

  // Render the main Opulab page content for authenticated users
  return (
    <ProtectedRoute>
      <OpulabStrategiePage />
    </ProtectedRoute>
  );
}
