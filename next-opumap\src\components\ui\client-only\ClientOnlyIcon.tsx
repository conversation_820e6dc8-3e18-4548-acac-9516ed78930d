'use client';

import { ReactNode } from 'react';
import ClientOnly from './ClientOnly';

interface ClientOnlyIconProps {
  children: ReactNode;
  className?: string;
  width?: string | number;
  height?: string | number;
}

/**
 * ClientOnlyIcon Komponente, die sicherstellt, dass Icon-Komponenten (wie Lucide-Icons) 
 * nur auf dem Client gerendert werden.
 * Dies verhindert Hydration-Fehler, die durch Browser-Erweiterungen wie Dark Reader verursacht werden können.
 */
export default function ClientOnlyIcon({ 
  children, 
  className, 
  width = '24px', 
  height = '24px' 
}: ClientOnlyIconProps) {
  // Einfacher Platzhalter für das Icon während des Server-Renderings
  const fallback = (
    <div 
      className={className}
      style={{ 
        width: width, 
        height: height,
        display: 'inline-block'
      }} 
    />
  );

  return (
    <ClientOnly fallback={fallback}>
      {children}
    </ClientOnly>
  );
}
