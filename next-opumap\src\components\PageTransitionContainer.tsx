'use client';

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';

interface PageTransitionContainerProps {
  children: React.ReactNode;
}

/**
 * A container component that handles global page transition setup
 * This component ensures that transitions are smooth and prevents layout shifts
 */
const PageTransitionContainer: React.FC<PageTransitionContainerProps> = ({ 
  children 
}) => {
  const pathname = usePathname();
  
  // Set up global transition handling
  useEffect(() => {
    // Clean up any lingering transition classes when pathname changes
    return () => {
      document.body.classList.remove('page-transition-active');
    };
  }, [pathname]);

  return (
    <div className="w-full h-full overflow-hidden">
      {children}
    </div>
  );
};

export default PageTransitionContainer;
