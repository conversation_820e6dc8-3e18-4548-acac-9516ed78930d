import { Strategy as OpulabStrategy } from "../opulab/strategyService";

// Represents the structure of a scan result received from the backend poll or load.
export interface ScanResult {
  place_id: string;
  companyName: string;
  result_text: string;
}

// Represents a scan result formatted for display in the RecognizedChancesSection.
export interface DisplayScanResult {
  id: number; // scan_results.id
  title: string; // companies.name
  description: string; // scan_results.result_text
  place_id: string; // Using place_id as the primary identifier
  created_at: string; // scan_results.created_at
}

// Represents a standard strategy option (non-Opulab).
export interface StrategyOption {
  value: string;
  label: string;
  info: string;
}

// Represents the possible states of the scan polling process.
export type PollStatus = 'idle' | 'polling' | 'completed' | 'error' | 'timeout';

// Re-export Opulab Strategy type if needed elsewhere
export type { OpulabStrategy };

// Type for selected company data
export interface SelectedCompany {
    id: number;
    user_id: string; // UUID from Supabase Auth
    place_id: string;
    name: string; // Changed from company_name to name to match the database
    address?: string;
    created_at: string;
    is_deleted: boolean;
    company_id?: number; // Reference to the companies table
    companies?: { id: number; name: string }; // Nested companies data from join
}

// Type for User Profile Data used in saving company info
export interface UserProfileUpdateData {
    name: string | null;
    email: string | null;
    company_name: string | null;
    address: string | null;
    phone: string | null;
    website: string | null;
    employee_count: number | null;
    company_info_points: string | null;
}