'use client'; // Needs to be client component for auth check & conditional rendering

import React, { useState, useEffect } from 'react'; // Import useState and useEffect
import LandingPage from '@/app/Landingpage';
import MapComponent from '@/app/Homepage';
// Import the useAuth hook instead of the context
import { useAuth } from '@/contexts/AuthContext';

export default function HomePage() {
  // Use the useAuth hook
  const { user, isLoading: isLoadingAuth, initialAuthDone } = useAuth();
  const isAuthenticated = !!user; // Simple check for user existence

  // State to track if component has mounted on the client
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true only after mounting
  useEffect(() => {
    console.log("HomePage: Component mounted on client.");
    setIsClient(true);
  }, []);

  // Log the values before conditional rendering
  console.log("HomePage: Rendering - isClient:", isClient, "isLoadingAuth:", isLoadingAuth, "isAuthenticated:", isAuthenticated, "initialAuthDone:", initialAuthDone);

  // Show loading spinner only while the initial authentication determination is in progress.
  // Once initialAuthDone is true, we rely on the user state for rendering.
  if (!isClient || !initialAuthDone) {
    console.log("HomePage: Displaying initial loading spinner.");
    // Zeige den gleichen Ladeindikator wie in der ProtectedRoute
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">Laden...</p>
        </div>
      </div>
    );
  }

  console.log("HomePage: Rendering main content based on authentication state.");
  // Conditionally render based on authentication state only after mount and initial auth check are complete
  return isAuthenticated ? <MapComponent /> : <LandingPage />;
}
