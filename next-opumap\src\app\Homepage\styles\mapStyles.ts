import type { Libraries } from '@react-google-maps/api';
import { DemoMarker } from "@/types";

// Google Maps dark mode style
export const darkMapStyle: google.maps.MapTypeStyle[] = [
  { elementType: "geometry", stylers: [{ color: "#242f3e" }] },
  { elementType: "labels.text.stroke", stylers: [{ color: "#242f3e" }] },
  { elementType: "labels.text.fill", stylers: [{ color: "#746855" }] },
  {
    featureType: "administrative.locality",
    elementType: "labels.text.fill",
    stylers: [{ color: "#d59563" }]
  },
  {
    featureType: "poi",
    elementType: "labels.text.fill",
    stylers: [{ color: "#d59563" }]
  },
  {
    featureType: "poi.park",
    elementType: "geometry",
    stylers: [{ color: "#263c3f" }]
  },
  {
    featureType: "poi.park",
    elementType: "labels.text.fill",
    stylers: [{ color: "#6b9a76" }]
  },
  {
    featureType: "road",
    elementType: "geometry",
    stylers: [{ color: "#38414e" }]
  },
  {
    featureType: "road",
    elementType: "geometry.stroke",
    stylers: [{ color: "#212a37" }]
  },
  {
    featureType: "road",
    elementType: "labels.text.fill",
    stylers: [{ color: "#9ca5b3" }]
  },
  {
    featureType: "road.highway",
    elementType: "geometry",
    stylers: [{ color: "#746855" }]
  },
  {
    featureType: "road.highway",
    elementType: "geometry.stroke",
    stylers: [{ color: "#1f2835" }]
  },
  {
    featureType: "road.highway",
    elementType: "labels.text.fill",
    stylers: [{ color: "#f3d19c" }]
  },
  {
    featureType: "transit",
    elementType: "geometry",
    stylers: [{ color: "#2f3948" }]
  },
  {
    featureType: "transit.station",
    elementType: "labels.text.fill",
    stylers: [{ color: "#d59563" }]
  },
  {
    featureType: "water",
    elementType: "geometry",
    stylers: [{ color: "#17263c" }]
  },
  {
    featureType: "water",
    elementType: "labels.text.fill",
    stylers: [{ color: "#515c6d" }]
  },
  {
    featureType: "water",
    elementType: "labels.text.stroke",
    stylers: [{ color: "#17263c" }]
  }
];

// Default map settings
export const mapDefaults = {
  center: {
    lat: 52.2799, // Osnabrück center
    lng: 8.0472,
  },
  zoom: 10,
  libraries: ["places"] as Libraries,
  storageKey: 'opumap_map_state'
};

// Map options
export const mapOptions = (isDarkMode: boolean) => ({
  disableDefaultUI: true, // Cleaner map interface
  clickableIcons: true, // Allow clicking POIs
  scrollwheel: true,
  gestureHandling: "greedy", // Allow map interaction without modifier keys
  styles: isDarkMode ? darkMapStyle : [],
});

// Layout styles for map section
export const mapLayoutStyles = {
  container: 'map-container flex flex-col bg-background text-foreground',
  mapSection: 'map-section w-full',
  mapContainer: 'w-full h-[400px] border border-border rounded-lg',
  contentSection: 'flex flex-col md:flex-row flex-wrap justify-center items-center md:items-start gap-4 md:gap-8 p-4 md:p-8',
};

// Marker options
export const markerStyles = {
  selectedMarkerOptions: {
    icon: {
      url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'
    }
  }
};

// Error and loading states
export const statusStyles = {
  errorMessage: 'text-red-500 text-center p-4',
  loadingMessage: 'text-center p-4',
};

// Demo markers (replace with actual data fetching if needed)
export const DEMO_MARKERS: DemoMarker[] = [
  {
    id: "1", // Changed to string
    place_id: "demo_1",
    position: { lat: 52.5200, lng: 13.4050 },
    name: "Beispiel Unternehmen 1",
    formatted_address: "Musterstraße 123, Berlin",
    formatted_phone_number: "+49 30 123456",
    website: "www.beispiel1.de",
    description: "Dies ist ein Beispiel-Unternehmen in Berlin.",
  },
  {
    id: "2", // Changed to string
    place_id: "demo_2",
    position: { lat: 52.5300, lng: 13.4150 },
    name: "Beispiel Unternehmen 2",
    formatted_address: "Testweg 45, Berlin",
    formatted_phone_number: "+49 30 654321",
    website: "www.beispiel2.de",
    description: "Ein weiteres Beispiel-Unternehmen in Berlin.",
  },
];
