import Link from 'next/link';
import { FaGith<PERSON>, FaTwi<PERSON>, Fa<PERSON>inkedin, FaEnvelope } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    company: [
      { name: '<PERSON>ber uns', href: '/about' },
      { name: 'Team', href: '/team' },
      { name: '<PERSON><PERSON><PERSON>', href: '/careers' },
      { name: '<PERSON><PERSON><PERSON>', href: '/contact' },
    ],
    legal: [
      { name: 'Impressum', href: '/legal/impressum' },
      { name: '<PERSON><PERSON>chu<PERSON>', href: '/legal/privacy' },
      { name: 'AGB', href: '/legal/terms' },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/legal/cookies' },
    ],
    resources: [
      { name: 'Doku<PERSON>', href: '/docs' },
      { name: 'Blog', href: '/blog' },
      { name: '<PERSON><PERSON><PERSON> & <PERSON>', href: '/support' },
      { name: '<PERSON>Q', href: '/faq' },
    ],
  };

  const socialLinks = [
    { icon: <FaGithub size={20} />, href: 'https://github.com/yourusername' },
    { icon: <FaTwitter size={20} />, href: 'https://twitter.com/yourusername' },
    { icon: <FaLinkedin size={20} />, href: 'https://linkedin.com/company/yourcompany' },
    { icon: <FaEnvelope size={20} />, href: 'mailto:<EMAIL>' },
  ];

  return (
    <footer className="bg-card border-t border-border">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">OpuMap</h3>
            <p className="text-muted-foreground text-sm">
              Ihre Plattform für interaktive Karten und Standortdienste.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Unternehmen
            </h3>
            <ul className="mt-4 space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Rechtliches
            </h3>
            <ul className="mt-4 space-y-2">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Ressourcen
            </h3>
            <ul className="mt-4 space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-12 pt-8 border-t border-border">
          <p className="text-sm text-muted-foreground text-center">
            &copy; {currentYear} OpuMap. Alle Rechte vorbehalten.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
