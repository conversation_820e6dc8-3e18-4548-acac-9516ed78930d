// src/types/index.ts
import { Database } from './supabase';

// Type for the demo markers used in MapComponent
export interface DemoMarker {
    id: string; // Changed from number to string
    position: google.maps.LatLngLiteral;
    name: string;
    formatted_address: string;
    formatted_phone_number?: string;
    website?: string;
    photos?: google.maps.places.PlacePhoto[]; // Added for consistency and to hold photos
    description?: string;
    place_id?: string; // This is Google's Place ID, id property is now also string
}

// Combined type for business data, accepting Google Place results or our demo markers
export type BusinessData = Omit<google.maps.places.PlaceResult, 'photos' | 'opening_hours' | 'geometry'> & {
    photos?: google.maps.places.PlacePhoto[];
    opening_hours?: google.maps.places.OpeningHours;
    geometry?: { location: google.maps.LatLng | google.maps.LatLngLiteral };
} | DemoMarker;

// Type for the structure of analysis results fetched/saved
export interface AnalysisResult {
    analysisContent: string;
    analysisDate: string; // ISO string date
    // Add other relevant fields if needed
}

// Type for storing map state in localStorage
export interface MapState {
    center: google.maps.LatLngLiteral;
    zoom: number;
    selectedBusinessPlaceId?: string;
    lastUpdated: string; // ISO string date
}

// You can add other shared types here, e.g., for User profile if needed across multiple files
export interface UserProfile {
    id?: string;
    company_name?: string | null;
    name?: string | null;
    email?: string | null;
    address?: string | null;
    phone?: string | null;
    website?: string | null;
    employee_count?: number | string | null;
    company_info_points?: string | null;
    description?: string | null; // Added based on db.js ALTER TABLE
    industry?: string | null;    // Added based on db.js ALTER TABLE
    password_hash?: string;      // Only used internally, never sent to client
    createdAt?: string;
    updatedAt?: string;
}

// Supabase Profile types
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

/* eslint-disable @typescript-eslint/no-namespace */
// Augment google.maps.places.OpeningHours to include weekday_text
declare global {
    namespace google.maps.places {
        interface OpeningHours {
            weekday_text?: string[];
        }
    }
}
/* eslint-enable @typescript-eslint/no-namespace */
