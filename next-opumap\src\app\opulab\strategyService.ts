'use client';

import { createClient } from '@/utils/supabase/client';

// Define the Strategy interface
export interface Strategy {
  id?: number;
  strategy_name: string;
  name?: string;
  company_info?: string;
  personal_goal?: string;
  short_term_tasks?: string;
  long_term_goals?: string;
  relevant_aspects?: string[]; // JSON parsed array of aspect IDs
  short_term_value?: number; // Value for kurzfristige Umsetzbarkeit slider (0-100)
  long_term_value?: number; // Value for langfristiger Erfolg slider (0-100)
  analysis_started?: boolean;
  analysis_result?: string;
  created_at?: string;
  updated_at?: string;
}

export class StrategyService {
  // Get all strategies for the current user
  static async getStrategies(): Promise<Strategy[]> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        throw new Error('Nicht authentifiziert.');
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch('/api/strategies', {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 401) {
          window.location.href = '/login';
          throw new Error('Sitzung abgelaufen. Bitte melden Sie sich erneut an.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Fehler beim Laden der Strategien');
      }

      const data = await response.json();
      return (data.strategies || []).map((s: Strategy) => ({
        ...s,
        // Ensure strategy_name is always available for backward compatibility
        strategy_name: s.strategy_name || s.name,
        relevant_aspects: typeof s.relevant_aspects === 'string'
          ? JSON.parse(s.relevant_aspects)
          : s.relevant_aspects
      }));
    } catch (error) {
      console.error('Error loading strategies:', error);
      throw error;
    }
  }

  // Create a new strategy
  static async createStrategy(strategy: Strategy): Promise<Strategy> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        throw new Error('Nicht authentifiziert.');
      }

      const response = await fetch('/api/strategies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(strategy)
      });

      if (!response.ok) {
        if (response.status === 401) {
          window.location.href = '/login';
          throw new Error('Sitzung abgelaufen. Bitte melden Sie sich erneut an.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Fehler beim Erstellen der Strategie');
      }

      const data = await response.json();
      return data.strategy;
    } catch (error) {
      console.error('Error creating strategy:', error);
      throw error;
    }
  }

  // Update an existing strategy
  static async updateStrategy(id: number, strategy: Strategy): Promise<Strategy> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        throw new Error('Nicht authentifiziert.');
      }

      const response = await fetch(`/api/strategies?id=${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(strategy)
      });

      if (!response.ok) {
        if (response.status === 401) {
          window.location.href = '/login';
          throw new Error('Sitzung abgelaufen. Bitte melden Sie sich erneut an.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Fehler beim Aktualisieren der Strategie');
      }

      const data = await response.json();
      return data.strategy;
    } catch (error) {
      console.error('Error updating strategy:', error);
      throw error;
    }
  }

  // Delete a strategy
  static async deleteStrategy(id: number): Promise<void> {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.warn("No active session found");
        throw new Error('Nicht authentifiziert.');
      }

      const response = await fetch(`/api/strategies?id=${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        if (response.status === 401) {
          window.location.href = '/login';
          throw new Error('Sitzung abgelaufen. Bitte melden Sie sich erneut an.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Fehler beim Löschen der Strategie');
      }
    } catch (error) {
      console.error('Error deleting strategy:', error);
      throw error;
    }
  }
}
