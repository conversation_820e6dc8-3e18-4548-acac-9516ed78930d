import { NextRequest, NextResponse } from 'next/server';
import pkg from 'pg';
const { Client } = pkg;
import { verifyAuth } from '@/lib/auth';
import { mapUuidToNumericId } from '@/utils/userIdMapping';

// Helper: PG client config
function getPgClientConfig() {
  const connectionString = process.env.POSTGRES_URL;
  if (!connectionString) {
    throw new Error('POSTGRES_URL environment variable not found.');
  }
  return {
    connectionString,
    ssl: { rejectUnauthorized: false }
  };
}

/**
 * GET /api/lab-files?strategyId=...
 * Returns the lab file for the authenticated user and strategy, if it exists.
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuth(request);
    if (!auth?.user?.id) {
      return NextResponse.json({ error: 'Nicht authentifiziert.' }, { status: 401 });
    }
    // Map the UUID to a numeric ID for database compatibility
    const userId = await mapUuidToNumericId(auth.user.id);
    const url = new URL(request.url);
    const strategyId = url.searchParams.get('strategyId');
    if (!strategyId) {
      return NextResponse.json({ error: 'strategyId ist erforderlich.' }, { status: 400 });
    }

    const client = new Client(getPgClientConfig());
    try {
      await client.connect();
      const query = `
        SELECT id, user_id, strategy_id, content, created_at, updated_at
        FROM lab_files
        WHERE user_id = $1 AND strategy_id = $2
        LIMIT 1
      `;
      const result = await client.query(query, [userId, strategyId]);
      const file = result.rows[0] || null;
      const response = NextResponse.json({ file });
      response.headers.set('X-Refreshed-Token', auth.refreshedToken);
      return response;
    } finally {
      await client.end();
    }
  } catch (err) {
    console.error('Error in GET /api/lab-files:', err);
    const message = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}

/**
 * POST /api/lab-files
 * Body: { strategyId: number }
 * Creates a new lab file if none exists, or returns existing.
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuth(request);
    if (!auth?.user?.id) {
      return NextResponse.json({ error: 'Nicht authentifiziert.' }, { status: 401 });
    }
    // Map the UUID to a numeric ID for database compatibility
    const userId = await mapUuidToNumericId(auth.user.id);
    const body = await request.json();
    const { strategyId } = body;
    if (!strategyId) {
      return NextResponse.json({ error: 'strategyId ist erforderlich.' }, { status: 400 });
    }

    const client = new Client(getPgClientConfig());
    try {
      await client.connect();
      // Check existing
      const checkQ = `
        SELECT id, user_id, strategy_id, content, created_at, updated_at
        FROM lab_files
        WHERE user_id = $1 AND strategy_id = $2
        LIMIT 1
      `;
      const existing = await client.query(checkQ, [userId, strategyId]);
      if (existing.rows.length > 0) {
        const response = NextResponse.json({ file: existing.rows[0] });
        response.headers.set('X-Refreshed-Token', auth.refreshedToken);
        return response;
      }
      // Insert new
      const insertQ = `
        INSERT INTO lab_files (user_id, strategy_id, content)
        VALUES ($1, $2, '')
        RETURNING id, user_id, strategy_id, content, created_at, updated_at
      `;
      const inserted = await client.query(insertQ, [userId, strategyId]);
      const response = NextResponse.json({ file: inserted.rows[0] });
      response.headers.set('X-Refreshed-Token', auth.refreshedToken);
      return response;
    } finally {
      await client.end();
    }
  } catch (err) {
    console.error('Error in POST /api/lab-files:', err);
    const message = err instanceof Error ? err.message : 'Unbekannter Fehler';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
