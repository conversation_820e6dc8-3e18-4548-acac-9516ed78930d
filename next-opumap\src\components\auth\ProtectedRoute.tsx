'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, isLoading, initialAuthDone } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log(`ProtectedRoute Effect: initialAuthDone=${initialAuthDone}, isLoading=${isLoading}, user=${!!user}`);
    // Only redirect if initial auth determination is complete, not currently loading, and no user
    if (initialAuthDone && !isLoading && !user) {
      console.log("ProtectedRoute: Redirecting to /login");
      router.push('/login');
    }
  }, [user, isLoading, initialAuthDone, router]);

  // Show loading spinner only if the initial auth determination hasn't finished yet.
  // Temporary isLoading changes after initial auth is done (e.g., from Fast Refresh) should not show spinner.
  if (!initialAuthDone) {
    console.log(`ProtectedRoute Render: Showing initial loading spinner (initialAuthDone=${initialAuthDone})`);
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">Laden...</p>
        </div>
      </div>
    );
  }

  // If initial auth is done, not loading, but still no user:
  // The useEffect will handle the redirect. Returning null prevents a flash of content.
  if (!user) {
    console.log("ProtectedRoute Render: No user, and redirect effect should handle it. Returning null.");
    return null;
  }

  // If initial auth is done, not loading, and user exists: render children.
  console.log("ProtectedRoute Render: User authenticated, rendering children.");
  return <>{children}</>;
}
