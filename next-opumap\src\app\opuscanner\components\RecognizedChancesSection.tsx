'use client';
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2 } from 'lucide-react';
import { DisplayScanResult } from '../types';

interface RecognizedChancesSectionProps {
  scanResults: DisplayScanResult[]; // Use the consolidated list type
  isLoading: boolean; // Use the combined loading state
  onResultClick: (result: DisplayScanResult) => void; // Update click handler to accept the type used in the list (DisplayScanResult)
  showAllCompanies: boolean; // New prop for toggle state
  onToggleChange: (event: React.ChangeEvent<HTMLInputElement>) => void; // New prop for toggle handler
}

export const RecognizedChancesSection: React.FC<RecognizedChancesSectionProps> = ({ 
  scanResults,
  isLoading, 
  onResultClick,
  showAllCompanies,
  onToggleChange
}) => {
  return (
    <Card className="mt-6 flex-grow flex flex-col">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">
            Scan Ergebnisse
            {isLoading && <Loader2 className="inline-block ml-2 h-5 w-5 animate-spin" />}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <label htmlFor="toggleView" className="text-sm text-muted-foreground">
              {showAllCompanies ? 'Alle Ergebnisse für Strategie' : 'Ausgewählte Unternehmen'}
            </label>
            <input
              type="checkbox"
              id="toggleView"
              checked={showAllCompanies}
              onChange={onToggleChange}
              className="w-4 h-4"
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow overflow-y-auto custom-scrollbar space-y-4">
        {!isLoading && scanResults.length > 0 && (
          <div className="space-y-2">
            {scanResults.map((result) => (
              <button
                key={`result-${result.id}`}
                onClick={() => onResultClick(result)}
                className="w-full text-left p-3 border rounded-lg hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors duration-150 ease-in-out"
              >
                <Badge variant="secondary">{result.title}</Badge>
                <p className="text-sm text-muted-foreground mt-1 truncate">
                  {result.description ? result.description.substring(0, 80) + '...' : 'Klicken um Details zu sehen...'}
                </p>
              </button>
            ))}
          </div>
        )}

        {isLoading && scanResults.length === 0 && (
          <p className="text-center text-muted-foreground">Lade Ergebnisse...</p>
        )}
        
        {!isLoading && scanResults.length === 0 && (
          <p className="text-center text-muted-foreground">Keine Scan-Ergebnisse für diese Strategie gefunden. Starten Sie einen Scan.</p>
        )}
      </CardContent>
    </Card>
  );
};
