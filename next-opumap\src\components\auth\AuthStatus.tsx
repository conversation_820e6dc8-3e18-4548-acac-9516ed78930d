'use client';

import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { setIntentionalLogout } from '@/components/SessionExpiredModal';
import LogoutButton from '@/components/auth/LogoutButton';
import { Button } from '@/components/ui/button'; // Import Button component

export default function AuthStatus() {
  const { user, signOut, isLoading } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    // Markieren, dass dies ein absichtliches Ausloggen ist
    setIntentionalLogout();
    await signOut();
    router.push('/');
  };

  if (isLoading) {
    return <div className="text-sm text-[var(--color-muted-foreground)]">Laden...</div>;
  }

  if (!user) {
    return (
      <div className="flex items-center gap-2">
        <Button asChild size="md" className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-sm">
          <Link
            href="/login"
          >
            Anmelden
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-4">
      <div className="text-sm text-[var(--color-muted-foreground)]">
        Angemeldet als <span className="font-medium text-[var(--color-foreground)]">{user.name || user.email}</span>
      </div>
      <LogoutButton
        onClick={handleSignOut}
        className="text-sm transition-colors"
      >
        Abmelden
      </LogoutButton>
    </div>
  );
}
