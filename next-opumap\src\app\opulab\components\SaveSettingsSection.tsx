'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Save, Trash2 } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

// Import styles
import { cardStyles, formStyles } from '../styles';

interface SaveSettingsSectionProps {
  onSaveSettings?: (strategyName: string) => void;
  onDeleteStrategy?: () => void;
  isSaving?: boolean;
  selectedStrategy?: string;
  isNewStrategy?: boolean;
  message?: string;
  disabled?: boolean;
}

const SaveSettingsSection: React.FC<SaveSettingsSectionProps> = ({
  onSaveSettings = () => console.log('Saving settings...'),
  onDeleteStrategy = () => console.log('Deleting strategy...'),
  isSaving = false,
  selectedStrategy = '',
  isNewStrategy = false,
  message = '',
  disabled = false
}) => {
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [strategyName, setStrategyName] = useState(selectedStrategy);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Aktualisiere den Strategienamen, wenn sich die selectedStrategy-Prop ändert
  useEffect(() => {
    setStrategyName(selectedStrategy);
  }, [selectedStrategy]);

  const handleOpenSaveDialog = () => {
    setStrategyName(selectedStrategy);
    setShowSaveDialog(true);
  };

  const handleSave = () => {
    if (!strategyName.trim()) return;
    onSaveSettings(strategyName);
    setShowSaveDialog(false);
  };

  const handleDelete = () => {
    onDeleteStrategy();
    setShowDeleteDialog(false);
  };

  return (
    <div className={cardStyles.elevated}>
      <CardHeader className={cardStyles.header}>
        <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>
          Strategie speichern
        </CardTitle>
        <p className={cardStyles.subtitle}>Speichern Sie Ihre aktuelle Strategie</p>
      </CardHeader>
      <CardContent className={`${cardStyles.content}`}>
        <div className="flex flex-col space-y-4 pb-1">
          <p className="text-sm text-muted-foreground">
            Speichern Sie Ihre aktuelle Strategie mit allen Einstellungen und Auswahlen.
          </p>

          {message && (
            <p className="text-sm font-medium text-primary">{message}</p>
          )}

          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              size="sm"
              className="bg-primary hover:bg-primary/90 text-primary-foreground flex items-center"
              onClick={handleOpenSaveDialog}
              disabled={isSaving || disabled}
            >
              {isSaving ? 'Speichern...' : 'Strategie speichern'}
              <Save className="ml-2 h-4 w-4" />
            </Button>

            {!isNewStrategy && (
              <Button
                size="sm"
                variant="outline"
                className="border-destructive/50 hover:border-destructive text-destructive flex items-center"
                onClick={() => setShowDeleteDialog(true)}
                disabled={isSaving || disabled}
              >
                Strategie löschen
                <Trash2 className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>

      {/* Save Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Strategie speichern</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <label htmlFor="strategy-name" className={formStyles.label}>
              Strategiename
            </label>
            <Input
              id="strategy-name"
              value={strategyName}
              onChange={(e) => setStrategyName(e.target.value)}
              placeholder="Geben Sie einen Namen für Ihre Strategie ein"
              className="mt-1"
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>Abbrechen</Button>
            <Button onClick={handleSave} disabled={!strategyName.trim()}>
              {isNewStrategy ? 'Speichern' : 'Aktualisieren'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Strategie löschen</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Sind Sie sicher, dass Sie die Strategie &quot;{selectedStrategy}&quot; löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>Abbrechen</Button>
            <Button className="bg-destructive hover:bg-destructive/90 text-destructive-foreground" onClick={handleDelete}>Löschen</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SaveSettingsSection;
