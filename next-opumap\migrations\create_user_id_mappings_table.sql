-- Create a table to map <PERSON><PERSON><PERSON> Auth UUIDs to numeric IDs
CREATE TABLE IF NOT EXISTS user_id_mappings (
  numeric_id SERIAL PRIMARY KEY,
  uuid UUID NOT NULL UNIQUE,
  user_email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on the UUID column for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_id_mappings_uuid ON user_id_mappings(uuid);

-- Grant necessary permissions
ALTER TABLE user_id_mappings ENABLE ROW LEVEL SECURITY;

-- Create policies to allow authenticated users to read their own mappings
CREATE POLICY "Users can view their own mappings" 
  ON user_id_mappings 
  FOR SELECT 
  TO authenticated 
  USING (auth.uid() = uuid);

-- Only allow service role to insert/update mappings
CREATE POLICY "Service role can manage all mappings" 
  ON user_id_mappings 
  FOR ALL 
  TO service_role 
  USING (true);
