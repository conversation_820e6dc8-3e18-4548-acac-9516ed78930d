'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>nt,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Rocket } from 'lucide-react';
import { Slider } from "@/components/ui/slider";
import { TouchStartLaborButton } from './ui';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Layer,
  TooltipProps
} from 'recharts';

// Import styles
import { cardStyles, analyseStyles, buttonStyles } from '../styles';

interface StrategieAnalyseProps {
  result: string;
  onStartAnalysis: () => void;
  showPrioritization?: boolean;
  showAnalysisResult?: boolean;
  shortTermValue?: number;
  longTermValue?: number;
  onShortTermChange?: (value: [number]) => void;
  onLongTermChange?: (value: [number]) => void;
}

interface CustomTooltipPayload {
  value: number;
  dataKey: string;
  color: string;
}

interface GraphDataPoint {
  x: number;
  y: number;
  y2: number;
  y3: number;
}

// Funktion zur Berechnung der Graphenwerte basierend auf den Slider-Werten
const calculateGraphData = (shortTerm: number, longTerm: number): GraphDataPoint[] => {
  // Normalisierte Parameter (0-1)
  const s = shortTerm / 100;
  const l = longTerm / 100;

  // Generiere Datenpunkte für den Graphen
  const data = [];
  for (let i = 0; i <= 100; i += 5) {
    // Kubische Funktion mit Sinusüberlagerung, die beide Parameter berücksichtigt
    // s beeinflusst die Amplitude, l beeinflusst die Frequenz
    const value = (s * 3) * (Math.sin(i * (0.1 + l * 0.2)) + Math.cos(i * 0.05 * (1 + l)));
    data.push({
      x: i,
      y: value,
      // Zusätzliche Kurven mit leichten Variationen
      y2: value * 0.8 + 0.2 * Math.sin(i * 0.2),
      y3: value * 0.6 - 0.3 * Math.cos(i * 0.15)
    });
  }
  return data;
};

// Benutzerdefinierter Tooltip für den Graphen
const CustomTooltip = ({ active, payload }: TooltipProps<number, string> & { payload?: CustomTooltipPayload[] }) => {
  if (active && payload && payload.length) {
    const isDarkMode = document.documentElement.classList.contains('dark');
    return (
      <div className={`p-2 rounded-md shadow-md ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} border border-border`}>
        <p className="text-sm font-medium">Wert: {payload[0].value.toFixed(2)}</p>
      </div>
    );
  }
  return null;
};

const StrategieAnalyse: React.FC<StrategieAnalyseProps> = ({
  result,
  onStartAnalysis,
  showPrioritization = true,
  showAnalysisResult = true,
  shortTermValue: propShortTermValue,
  longTermValue: propLongTermValue,
  onShortTermChange,
  onLongTermChange
}) => {
  // State für Slider-Werte - jetzt als Tuple typisiert
  const [shortTermValue, setShortTermValue] = useState<[number]>([propShortTermValue ?? 50]);
  const [longTermValue, setLongTermValue] = useState<[number]>([propLongTermValue ?? 50]);

  // Update local state when props change
  useEffect(() => {
    if (propShortTermValue !== undefined) {
      setShortTermValue([propShortTermValue]);
    }
  }, [propShortTermValue]);

  useEffect(() => {
    if (propLongTermValue !== undefined) {
      setLongTermValue([propLongTermValue]);
    }
  }, [propLongTermValue]);

  // Berechne Graphendaten basierend auf Slider-Werten
  const graphData = calculateGraphData(shortTermValue[0], longTermValue[0]);

  // Typisierte Handler mit sicherer Umwandlung
  const handleShortTermChange = (value: number[]) => {
    const safeValue: [number] = value.length > 0 ? [value[0]] : [50];
    setShortTermValue(safeValue);
    if (onShortTermChange) {
      onShortTermChange(safeValue);
    }
  };

  const handleLongTermChange = (value: number[]) => {
    const safeValue: [number] = value.length > 0 ? [value[0]] : [50];
    setLongTermValue(safeValue);
    if (onLongTermChange) {
      onLongTermChange(safeValue);
    }
  };
  return (
    <div className={`grid grid-cols-1 ${showPrioritization && showAnalysisResult ? 'md:grid-cols-2' : 'md:grid-cols-1'}`}>
      {/* Priorisierung Card */}
      {showPrioritization && (
        <div>
          <CardHeader className={cardStyles.header}>
            <CardTitle className={`${cardStyles.title} break-words text-base sm:text-lg md:text-xl`}>Priorisierung</CardTitle>
            <p className={cardStyles.subtitle}>Visualisierung der strategischen Prioritäten</p>
          </CardHeader>
          <CardContent className={`${cardStyles.content}`}>
            {/* Split into two columns - left for text, right for sliders */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left column - Interactive Graph with Recharts */}
              <div className="flex flex-col justify-center">
                <div
                  className={
                    `${analyseStyles.graph} relative h-[250px] overflow-hidden rounded-xl shadow-lg border-2 border-primary/20 dark:border-primary/40 bg-gradient-to-br from-blue-50/80 via-white/80 to-blue-100/80 dark:from-blue-900/60 dark:via-gray-900/80 dark:to-blue-950/80 backdrop-blur-[2px]`
                  }
                >
                  {/* Optional: Deko-Glow-Element */}
                  <div className="pointer-events-none absolute -inset-2 z-0 rounded-xl bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 blur-2xl" />
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={graphData} margin={{ top: 10, right: 10, left: 0, bottom: 10 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#475569" strokeOpacity={0.3} />
                      <XAxis
                        dataKey="x"
                        tick={{ fontSize: 12, fill: "#94a3b8" }}
                        stroke="#94a3b8"
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => value % 25 === 0 ? value.toString() : ''}
                      />
                      <YAxis hide />
                      <Tooltip content={<CustomTooltip />} />
                      <Line
                        type="monotone"
                        dataKey="y"
                        stroke="#3b82f6"
                        strokeWidth={2.5}
                        dot={false}
                        activeDot={{ r: 6, fill: "#3b82f6" }}
                        isAnimationActive={true}
                        animationDuration={500}
                      />

                      {/* Immer sichtbarer, smooth bewegter Punkt für aktuellen Sliderwert */}
                      <Line
                        type="monotone"
                        dataKey="y2"
                        stroke="#60a5fa"
                        strokeWidth={1.5}
                        dot={false}
                        activeDot={false}
                        isAnimationActive={true}
                        animationDuration={700}
                      />
                      <Line
                        type="monotone"
                        dataKey="y3"
                        stroke="#93c5fd"
                        strokeWidth={1.5}
                        dot={false}
                        activeDot={false}
                        isAnimationActive={true}
                        animationDuration={900}
                      />
                      {/* Custom Dot Layer für smoothen, dauerhaften Punkt */}
                      <Layer>
                        {(() => {
                          const chartWidth = 400;
                          const chartHeight = 230;
                          const xMax = 100;
                          const yVals = graphData.map(d => d.y);
                          const yMin = Math.min(...yVals), yMax = Math.max(...yVals);
                          
                          const x = shortTermValue[0];
                          const i = Math.floor(x / 5);
                          const frac = (x % 5) / 5;
                          const y = (i >= graphData.length - 1)
                            ? graphData[graphData.length - 1].y
                            : graphData[i].y + (graphData[i + 1].y - graphData[i].y) * frac;

                          const pointX = (x / xMax) * chartWidth;
                          const pointY = ((yMax - y) / (yMax - yMin)) * chartHeight;

                          return (
                            <svg width="100%" height="100%" style={{ position: 'absolute', left: 0, top: 0, pointerEvents: 'none', zIndex: 10 }}>
                              <circle
                                cx={pointX}
                                cy={pointY}
                                r="10"
                                fill="#3b82f6"
                                stroke="white"
                                strokeWidth="3"
                                style={{ filter: 'drop-shadow(0 0 8px #3b82f6aa)' }}
                              />
                            </svg>
                          );
                        })()}
                      </Layer>
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Right column - Sliders */}
              <div className="flex flex-col space-y-8">
                {/* Kurzfristige Umsetzbarkeit Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-semibold flex items-center gap-2">
                      <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M5 12h14M12 5l7 7-7 7" /></svg>
                      Kurzfristige Umsetzbarkeit
                    </label>
                    <span className="inline-block rounded-full bg-primary/90 text-primary-foreground text-xs font-bold px-3 py-1 shadow border border-primary/60">
                      {shortTermValue[0]}
                    </span>
                  </div>
                  <Slider
                    value={shortTermValue}
                    max={100}
                    step={1}
                    onValueChange={handleShortTermChange}
                    className="mt-2"
                  >
                    {/* Custom Track/Thumb via Tailwind */}
                  </Slider>
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>0</span>
                    <span>100</span>
                  </div>
                </div>

                {/* Langfristiger Erfolg Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-semibold flex items-center gap-2">
                      <svg className="w-5 h-5 text-secondary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path d="M12 6v6l4 2" /></svg>
                      Langfristiger Erfolg
                    </label>
                    <span className="inline-block rounded-full bg-secondary/90 text-secondary-foreground text-xs font-bold px-3 py-1 shadow border border-secondary/60">
                      {longTermValue[0]}
                    </span>
                  </div>
                  <Slider
                    value={longTermValue}
                    max={100}
                    step={1}
                    onValueChange={handleLongTermChange}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>0</span>
                    <span>100</span>
                  </div>
                </div>
              </div>
            </div>

<p className="text-xs text-muted-foreground">
              Die Priorisierung balanciert kurzfristige Umsetzbarkeit mit langfristigem Erfolg für eine nachhaltige Strategie.
            </p>
          </CardContent>
        </div>
      )}

      {/* Analyseergebnis Card */}
      {showAnalysisResult && (
        <div className={analyseStyles.container + ' w-full'}>
          <CardHeader className={cardStyles.header + ' text-center'}>
            <CardTitle className={`${cardStyles.title} tracking-tight break-words text-base sm:text-lg md:text-xl`}>Analyseergebnis</CardTitle>
            <p className={cardStyles.subtitle + ' mb-2'}>Starten Sie die KI-gestützte Analyse</p>
          </CardHeader>
          <CardContent className={cardStyles.content + ' flex flex-col items-center bg-gradient-to-br from-primary/5 via-card to-secondary/10 rounded-b-xl shadow-lg border-t-0'}>
            <TouchStartLaborButton onClick={onStartAnalysis}>
              Labor starten <Rocket className={`${buttonStyles.icon} inline-block ml-2`} />
            </TouchStartLaborButton>
            <div className="mt-6 w-full max-w-md flex flex-col items-center">
              <span className="text-sm text-muted-foreground mb-2">Aktuelles Ergebnis:</span>
              <div
                className="transition-all duration-300 ease-in-out bg-primary/10 dark:bg-primary/20 border border-primary/30 dark:border-primary/40 rounded-2xl px-6 py-4 shadow-md text-lg font-semibold text-primary dark:text-primary-foreground tracking-wide animate-in fade-in"
                style={{ minWidth: 180, minHeight: 48 }}
              >
                {result || "Keine Analyse"}
              </div>
            </div>
          </CardContent>
        </div>
      )}
    </div>
  );
};

export default StrategieAnalyse;
