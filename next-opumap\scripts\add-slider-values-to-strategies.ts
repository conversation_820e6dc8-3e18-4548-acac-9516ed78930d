import dotenv from 'dotenv';
import pkg from 'pg';
const { Client } = pkg;
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env.local in the parent directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '../.env.local') });

async function addSliderValuesToStrategies() {
  console.log('Starting database update to add slider values to user_strategies table...');
  const connectionString = process.env.POSTGRES_URL;

  if (!connectionString) {
    console.error('Error: POSTGRES_URL environment variable not found in .env.local');
    process.exit(1);
  }

  console.log(`Attempting to connect with URL: ${connectionString.replace(/postgres:.*@/, 'postgres://<user>:<password>@')}`);

  // Use the pg Client directly with SSL settings
  const client = new Client({
    connectionString,
    ssl: {
      rejectUnauthorized: false // Allow self-signed certificates
    }
  });

  try {
    await client.connect();
    console.log('Database client connected.');

    // Add short_term_value and long_term_value columns to user_strategies table
    await client.query(`
      ALTER TABLE user_strategies 
      ADD COLUMN IF NOT EXISTS short_term_value INTEGER DEFAULT 50,
      ADD COLUMN IF NOT EXISTS long_term_value INTEGER DEFAULT 50;
    `);
    console.log("Added short_term_value and long_term_value columns to user_strategies table.");

    console.log('Database update completed successfully.');
  } catch (error) {
    console.error('Error updating database:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the function
addSliderValuesToStrategies().catch(console.error);
