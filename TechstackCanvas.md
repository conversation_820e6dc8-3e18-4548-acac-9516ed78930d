# Techstack Canvas für "Strategie-Kooperationsplattform"

Dieses Dokument ist ein ausführlicher, idiotensicherer Schritt-für-Schritt-Plan zur Umsetzung deines Projekts. Hier findest du detaillierte Babysteps, konkrete Befehle und Hinweise, um die komplette Entwicklung von der Idee bis zum Deployment abzuschließen.

---

## 1. Projektüberblick

**Projektziel:**  
Erstelle eine interaktive Website, die:
- Eine Google Maps-Karte anzeigt, auf der ausschließlich Unternehmen verzeichnet werden.
- Beim Anklicken eines Unternehmens auf der Karte ein individuelles Popup mit KI-generierten Strategieempfehlungen öffnet.
- Einen eigenen, geschützten Bereich besitzt, in dem du dein eigenes Unternehmen detailliert erfassen und verwalten kannst.
- Nutzerregistrierung, Login/Logout und personalisierte Datenspeicherung über ein sicheres System ermöglicht.

**Wichtig:**  
Die KI ermittelt im Hintergrund passende Kooperationsmöglichkeiten und Verkaufsempfehlungen basierend auf den Unternehmensdaten – auch deinen eigenen.

---

## 2. Konkreter Tech- & Provider-Stack

### Frontend
- **Framework:** React.js (mit Create React App)
- **Styling:** Tailwind CSS (für schnelles, responsives Design)
- **Kartenintegration:** Google Maps JavaScript API

### Backend
- **Programmiersprache:** JavaScript (Node.js)
- **Framework:** Express.js (für einfache API-Entwicklung)
- **KI-Integration:** OpenAI API (für die Generierung der Strategieempfehlungen)
- **Authentifizierung:** JSON Web Tokens (JWT) mit Hilfe von Bibliotheken wie `jsonwebtoken` und `bcrypt` (für Passwortverschlüsselung)

### Datenbank & Authentifizierung
- **Datenbank:** Supabase (basiert auf PostgreSQL, bietet zudem Authentifizierung und Storage)
- **ORM (optional):** Verwende Supabase-Clientbibliotheken, um direkt mit der Datenbank zu arbeiten

### Cloud Provider & Deployment
- **Cloud Provider:** Heroku (für das Hosting des Backends und die einfache Skalierung)
- **Versionsverwaltung & CI/CD:** Git (z.B. GitHub als Repository, Heroku Pipelines für automatisiertes Deployment)

---

## 3. Schritt-für-Schritt Checkliste

### Phase 1: Planung & Anforderungsanalyse
- [ ] **Projektziele und Anwendungsfälle definieren:**  
  - Notiere, was die Website leisten soll (Kartenanzeige, Popup mit KI, Unternehmensverwaltung, Nutzeraccounts).
  - Schreibe Use Cases: z.B. „Als Besucher klicke ich auf ein Unternehmen, um Empfehlungen zu sehen“.
- [ ] **Tech-Stack bestätigen:**  
  - Entscheide dich bewusst für React, Express, Supabase, OpenAI und Heroku.
- [ ] **Meilensteine und Zeitplan erstellen:**  
  - Lege fest, wann welcher Teil des Projekts (Frontend, Backend, KI, Deployment) fertig sein soll.

### Phase 2: Entwicklungsumgebung einrichten
- [ ] **Lokale Umgebung vorbereiten:**  
  - **Node.js installieren:**  
    Lade die aktuelle LTS-Version von [nodejs.org](https://nodejs.org/) herunter.
  - **Git installieren:**  
    Stelle sicher, dass Git auf deinem Rechner installiert ist.
- [ ] **Repository erstellen:**  
  - Erstelle ein neues Repository auf GitHub oder GitLab und klone es lokal.
  - Beispielbefehl:
    ```bash
    git clone https://github.com/dein-benutzername/projektname.git
    ```
- [ ] **Projektstruktur aufsetzen:**  
  - Erstelle Ordner für Frontend (`/client`) und Backend (`/server`).
- [ ] **Initialisierung:**  
  - Im Frontend-Ordner:  
    ```bash
    npx create-react-app .
    ```
  - Im Backend-Ordner:  
    ```bash
    npm init -y
    npm install express cors dotenv jsonwebtoken bcrypt
    ```

### Phase 3: Frontend-Entwicklung mit React & Tailwind CSS
- [x] **Tailwind CSS integrieren:**  
  - Folge der offiziellen [Tailwind CSS Anleitung für Create React App](https://tailwindcss.com/docs/guides/create-react-app).
  - Erstelle eine einfache Startseite mit Navigation (Home, Login, Dashboard).
- [x] **Google Maps einbinden:**  
  - Beantrage einen API-Schlüssel in der [Google Cloud Console](https://console.cloud.google.com/).
  - Installiere ein React-Paket für Google Maps, z.B. `@react-google-maps/api`:
    ```bash
    npm install @react-google-maps/api
    ```
  - Erstelle eine Komponente, die die Karte anzeigt und Marker basierend auf Unternehmensdaten setzt.
- [x] **Popup-Fenster entwickeln:**  
  - Implementiere ein modales Popup, das beim Klicken auf einen Marker erscheint.
  - Zeige darin Platzhalter für KI-Strategieempfehlungen an, die später dynamisch befüllt werden.

### Phase 4: Backend-Entwicklung mit Express.js
- [x] **Server-Grundaufbau:**  
  - Erstelle in `/server` eine Datei `index.js` und richte einen einfachen Express-Server ein:
    ```javascript
    const express = require('express');
    const cors = require('cors');
    const app = express();
    require('dotenv').config();
    
    app.use(cors());
    app.use(express.json());
    
    const PORT = process.env.PORT || 5000;
    app.listen(PORT, () => console.log(`Server läuft auf Port ${PORT}`));
    ```
- [x] **API-Endpunkte definieren:**  
  - Erstelle Routen für:
    - Nutzerregistrierung und Login (z.B. `/api/auth/register` und `/api/auth/login`)
    - Unternehmensdaten abrufen und speichern (z.B. `/api/companies`)
    - KI-Anfragen an die OpenAI API (z.B. `/api/strategy`)
- [x] **Sicherheitsmaßnahmen implementieren:**  
  - Validierung der Nutzereingaben
  - Nutzung von JWT zur Authentifizierung und Sicherung der Endpunkte

### Phase 5: Supabase als Datenbank & Authentifizierungsplattform
- [x] **Supabase-Projekt erstellen:**  
  - Registriere dich bei [Supabase](https://supabase.io/) und erstelle ein neues Projekt.
- [x] **Datenbank konfigurieren:**  
  - Erstelle Tabellen für:
    - **Users:** (ID, Name, Email, Passwort-Hash, etc.)
    - **Companies:** (ID, Name, Standort, etc.)
    - **CompanyDetails:** (Details deines eigenen Unternehmens, zugeordnet zu einem User)
  - Nutze das Supabase Dashboard, um das Schema zu definieren.
- [x] **Supabase-Client in das Backend integrieren:**  
  - Installiere die Supabase-JavaScript-Bibliothek:
    ```bash
    npm install @supabase/supabase-js
    ```
  - Erstelle eine Verbindung in deinem Backend:
    ```javascript
    const { createClient } = require('@supabase/supabase-js');
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_KEY;
    const supabase = createClient(supabaseUrl, supabaseKey);
    ```
- [x] **Datenbank-Operationen testen:**  
  - Schreibe einfache CRUD-Operationen für Nutzer und Unternehmen.

### Phase 6: KI-Integration (OpenAI API)
- [x] **API-Schlüssel besorgen:**  
  - Registriere dich bei [OpenAI](https://openai.com/) und erstelle einen API-Schlüssel.
- [x] **Integration in das Backend:**  
  - Installiere das OpenAI SDK:
    ```bash
    npm install openai
    ```
  - Erstelle eine Funktion, die Anfragen an die OpenAI API sendet und Strategieempfehlungen basierend auf den übergebenen Daten zurückgibt.
  - Beispielcode:
    ```javascript
    const { Configuration, OpenAIApi } = require("openai");
    const configuration = new Configuration({
      apiKey: process.env.OPENAI_API_KEY,
    });
    const openai = new OpenAIApi(configuration);
    
    async function getStrategyRecommendation(companyDetails) {
      const prompt = `Erstelle eine Strategieempfehlung für das Unternehmen basierend auf: ${companyDetails}`;
      const response = await openai.createCompletion({
        model: "text-davinci-003",
        prompt: prompt,
        max_tokens: 150,
      });
      return response.data.choices[0].text.trim();
    }
    ```
- [x] **Endpoint für KI-Anfragen erstellen:**  
  - Implementiere einen Express-Endpoint, der diese Funktion nutzt und das Ergebnis an das Frontend sendet.

### Phase 7: Nutzerverwaltung & Authentifizierung
- [ ] **Registrierung und Login implementieren:**  
  - Entwickle Formulare im Frontend zur Registrierung und Anmeldung.
  - Im Backend:  
    - Erstelle Endpunkte zur Nutzerregistrierung, bei denen Passwörter mit `bcrypt` gehasht werden.
    - Erstelle Login-Endpunkte, die einen JWT generieren, wenn die Anmeldedaten korrekt sind.
- [ ] **JWT und Session-Management:**  
  - Speichere den JWT im Frontend (z.B. in Local Storage) und füge ihn bei API-Anfragen im Header hinzu.
  - Erstelle Middleware in Express, die die Authentifizierung der Anfragen prüft.

### Phase 8: Testing & Qualitätssicherung
- [ ] **Unit- und Integrationstests schreiben:**  
  - Für das Backend: Nutze Frameworks wie Jest oder Mocha, um API-Endpunkte und Funktionen zu testen.
  - Für das Frontend: Nutze Testing Library und Jest, um Komponenten und Interaktionen zu prüfen.
- [ ] **Manuelle Tests:**  
  - Überprüfe jeden Flow (Registrierung, Login, Kartenanzeige, Popup, KI-Antwort) Schritt für Schritt.
- [ ] **Sicherheitstests:**  
  - Prüfe, ob alle Endpunkte geschützt sind und sensible Daten niemals unverschlüsselt übertragen werden.

### Phase 9: Deployment auf Heroku & Monitoring
- [ ] **Heroku-Konto einrichten:**  
  - Registriere dich bei [Heroku](https://www.heroku.com/) und installiere die Heroku CLI.
- [ ] **Vorbereitung des Deployments:**  
  - Erstelle in deinem Backend-Projekt eine `Procfile`:
    ```
    web: node index.js
    ```
  - Setze alle notwendigen Umgebungsvariablen (PORT, SUPABASE_URL, SUPABASE_KEY, OPENAI_API_KEY, etc.) im Heroku-Dashboard.
- [ ] **Deployment durchführen:**  
  - Führe folgende Befehle aus:
    ```bash
    heroku login
    git add .
    git commit -m "Deployment vorbereitet"
    git push heroku main
    ```
- [ ] **Monitoring & Logging:**  
  - Richte in Heroku Add-ons (wie Papertrail) für Log-Überwachung ein.
  - Überwache die Performance und stelle sicher, dass alle API-Endpunkte reibungslos funktionieren.

### Phase 10: Wartung & Weiterentwicklung
- [ ] **Regelmäßige Updates:**  
  - Plane regelmäßige Code-Reviews, Sicherheitsupdates und Bugfixes ein.
- [ ] **Nutzerfeedback einholen:**  
  - Implementiere Tools wie Google Analytics und Feedback-Formulare, um Nutzererfahrungen zu sammeln.
- [ ] **Erweiterungen planen:**  
  - Definiere weitere Features und Verbesserungen in einer Roadmap, z.B. erweiterte KI-Funktionalitäten, zusätzliche Filter für die Karte etc.

---

## 4. Zusätzliche Babysteps & Tipps

- **Dokumentation:**  
  - Schreibe zu jedem API-Endpunkt, jeder Komponente und jedem Modul klare Kommentare.
  - Nutze ein Wiki oder README-Dateien, um die Projektstruktur und die Nutzung der einzelnen Komponenten zu erklären.

- **Versionskontrolle:**  
  - Mache häufige, sinnvolle Commits.
  - Arbeite in Feature-Branches und merge diese nach erfolgreichen Tests in den Haupt-Branch.

- **Sicherheit:**  
  - Speichere sensible Daten (wie API-Schlüssel und Passwörter) niemals direkt im Code. Nutze `.env`-Dateien und Herokus Umgebungsvariablen.
  - Führe regelmäßige Sicherheitschecks und Code-Audits durch.

- **Agiles Vorgehen:**  
  - Nutze einfache Tools wie Trello oder GitHub Projects, um Aufgaben zu organisieren und den Fortschritt zu visualisieren.
  - Arbeite in Sprints (z.B. wöchentliche Ziele) und überprüfe regelmäßig den Projektstatus.

- **Teamkommunikation:**  
  - Falls du im Team arbeitest, sorge für regelmäßige Meetings und eine klare Aufgabenverteilung.
  - Nutze Chat-Tools wie Slack oder Microsoft Teams für die tägliche Abstimmung.

---

## Abschließende Hinweise

- **Flexibilität:**  
  Passe diesen Plan bei neuen Anforderungen oder unerwarteten Herausforderungen flexibel an.
- **Lerneffekt:**  
  Nutze diesen Plan als Leitfaden und als Lernwerkzeug – jeder Schritt ist so gestaltet, dass auch Einsteiger ohne Vorkenntnisse folgen können.
- **Support:**  
  Ziehe bei Problemen offizielle Dokumentationen, Community-Foren (z.B. Stack Overflow) und Tutorials zu Rate.

---

Viel Erfolg bei der Umsetzung deines Projekts! Arbeite Schritt für Schritt die Checkliste ab und dokumentiere deine Fortschritte – so baust du nicht nur eine funktionierende Anwendung, sondern lernst auch nachhaltig, wie man komplexe Webprojekte umsetzt.
