#!/bin/bash

# Deployment-Skript für OpuMap Next.js Anwendung
# Dieses Skript baut die Anwendung und startet sie in der Produktionsumgebung

# Farben für die Ausgabe
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== OpuMap Next.js Deployment ===${NC}\n"

# Verzeichnis erstellen, falls es nicht existiert
mkdir -p logs

# Umgebungsvariablen prüfen
if [ ! -f .env.local ]; then
  echo -e "${RED}Fehler: .env.local Datei nicht gefunden${NC}"
  echo "Bitte stellen Sie sicher, dass die .env.local Datei mit den notwendigen Umgebungsvariablen existiert."
  exit 1
fi

# Abhängigkeiten installieren
echo -e "${YELLOW}Installiere Abhängigkeiten...${NC}"
npm ci

# Anwendung bauen
echo -e "${YELLOW}Baue die Anwendung...${NC}"
npm run build

# Prüfen, ob der Build erfolgreich war
if [ $? -ne 0 ]; then
  echo -e "${RED}Build fehlgeschlagen. Bitte überprüfen Sie die Fehler oben.${NC}"
  exit 1
fi

# PM2 installieren, falls nicht vorhanden
if ! command -v pm2 &> /dev/null; then
  echo -e "${YELLOW}PM2 nicht gefunden. Installiere PM2 global...${NC}"
  npm install -g pm2
fi

# Anwendung mit PM2 starten/neustarten
echo -e "${YELLOW}Starte die Anwendung mit PM2...${NC}"
pm2 startOrRestart ecosystem.config.js

# Status anzeigen
echo -e "${YELLOW}PM2 Status:${NC}"
pm2 status

echo -e "\n${GREEN}Deployment abgeschlossen!${NC}"
echo -e "Die Anwendung läuft jetzt unter http://localhost:3000"
echo -e "Verwenden Sie 'pm2 logs' um die Logs anzuzeigen"
echo -e "Verwenden Sie 'pm2 stop opumap' um die Anwendung zu stoppen"
