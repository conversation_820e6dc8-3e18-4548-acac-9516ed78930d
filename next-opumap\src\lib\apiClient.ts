// Global variable to track if we're already showing an expiration message
// This prevents multiple alerts/redirects when multiple API calls fail simultaneously
let isHandlingExpiredToken = false;

/**
 * Custom fetch wrapper that handles token expiration
 * @param url The URL to fetch
 * @param options Fetch options
 * @returns The fetch response
 */
export async function fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
  // Only add auth header if we're on the client
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('token');
    
    // If we have a token, add it to the request
    if (token) {
      options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`
      };
    }
  }

  try {
    const response = await fetch(url, options);
    
    // Handle 401 Unauthorized errors (expired token)
    if (response.status === 401) {
      await handleTokenExpiration();
    }
    
    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

/**
 * Handle token expiration by clearing token and showing a message
 */
export async function handleTokenExpiration(): Promise<void> {
  // Prevent multiple handlers from running simultaneously
  if (isHandlingExpiredToken) return;
  
  try {
    isHandlingExpiredToken = true;
    
    // Clear the token from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      
      // Show a user-friendly message
      const event = new CustomEvent('session-expired');
      window.dispatchEvent(event);
    }
  } finally {
    // Reset after a short delay to prevent immediate re-triggering
    setTimeout(() => {
      isHandlingExpiredToken = false;
    }, 1000);
  }
}
